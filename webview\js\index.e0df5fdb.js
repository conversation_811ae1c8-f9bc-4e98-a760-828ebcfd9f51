import { d as defineComponent, c as computed, b as openBlock, m as createElementBlock, j as createBaseVNode } from "./bootstrap.ab073eb8.js";
import { _ as _export_sfc } from "./index.2e7c5603.js";
const _hoisted_1 = {
  class: "svg-icon",
  "aria-hidden": "true"
};
const _hoisted_2 = ["xlink:href"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    iconClass: {
      type: String,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const iconName = computed(() => {
      return `#icon-${props.iconClass}`;
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("svg", _hoisted_1, [
        createBaseVNode("use", { "xlink:href": iconName.value }, null, 8, _hoisted_2)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_49e1d1b6_lang = "";
const SvgIcon = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-49e1d1b6"]]);
export {
  SvgIcon as S
};
