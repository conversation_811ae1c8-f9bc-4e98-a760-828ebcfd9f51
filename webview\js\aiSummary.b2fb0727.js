import { $ as $post } from "./axios.0b09fd7a.js";
const sysUrl = "/zhhb-system";
function summaryStart(data) {
  return $post(`${sysUrl}/v1/ai/summary/start`, data);
}
function recordResourceReport(data) {
  return $post(`${sysUrl}/v1/ai/summary/resource_report`, data);
}
function getAISummaryByid(data) {
  return $post(`${sysUrl}/v1/ai/summary/byid`, data);
}
function getAISummaryDetail(data) {
  return $post(
    `${sysUrl}/v1/ai/summary/start/detail`,
    data
  );
}
export {
  getAISummaryByid as a,
  getAISummaryDetail as g,
  recordResourceReport as r,
  summaryStart as s
};
