#ifndef ZINDEXMANAGER_H
#define ZINDEXMANAGER_H

#include <QObject>
#include <QWidget>
#include <QMap>
#include <QList>
#include <QSet>
#include <QDebug>
#include <QTimer>
#include "IWindowManager.h"

#ifdef Q_OS_WIN
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>
// 取消Windows宏定义，避免与项目代码冲突
#ifdef DELETE
#undef DELETE
#endif
#ifdef max
#undef max
#endif
#ifdef min
#undef min
#endif
#endif

// 前向声明
class WindowActivationBlocker;

/**
 * @brief Z-Index层级定义
 * 
 * 根据层级图定义四个主要层级：
 * - 层级1: Qt原生组件-底部浮窗组件 (最底层)
 * - 层级2: CEF-资源选择、CEF-保存文件、CEF-应用中心全屏 (中下层)  
 * - 层级3: Qt原生组件-圆盘 (中上层)
 * - 层级4: CEF-随机点名、CEF-计时器、CEF-媒体资源预览、CEF-缩略图列表、Qt原生组件-右侧浮窗 (最顶层)
 */
enum class ZIndexLevel : int {
    // 层级1: 底部组件层 (0-999)
    BOTTOM_LAYER_BASE = 0,
    CANVAS_LAYER = 0,                    ///< 画布层 (最底层)
    BOTTOM_UI_LAYER = 100,               ///< 底部UI组件层
    
    // 层级2: CEF中层组件 (1000-1999)
    CEF_MIDDLE_LAYER_BASE = 1000,
    CEF_MEDIA_PREVIEW = 1003,            ///< CEF-媒体资源预览(mp3, mp4)
    
    // 层级3: Qt原生中上层组件 (2000-2999)
    QT_MIDDLE_LAYER_BASE = 2000,
    QT_FLOAT_MENU = 2001,                ///< 圆盘(浮动菜单)
    CEF_THUMBNAIL_LIST = 2002,           ///< CEF-缩略图列表(mp3, mp4)
    CEF_RESOURCE_SELECTOR = 2100,        ///< CEF-资源选择
    CEF_FILE_SAVE = 2101,                ///< CEF-保存文件
    CEF_CLASS_SUMMARY_DIALOG = 2100,     ///< CEF-课堂总结对话框
    CEF_EXIT_DIALOG = 2500,              ///< CEF-退出对话框
    
    // 层级4: 顶层组件 (3000-3999)
    TOP_LAYER_BASE = 3000,
    QT_SIDEBAR_PANEL = 3450,             ///< 侧边栏面板
    QT_OCTOPUS_DISC = 3475,              ///< 小章鱼圆盘
    CEF_RANDOM_CALL = 3500,              ///< CEF-随机点名
    CEF_TIMER = 3500,                    ///< CEF-计时器
    
    // 特殊层级: 临时置顶 (4000+)
    TEMPORARY_TOP = 4000,                ///< 临时置顶层
    CEF_APP_CENTER_FULLSCREEN = 4001,    ///< CEF-应用中心全屏
    CEF_SETTING_DIALOG = 4002,           ///< CEF-设置对话框
    QT_MAGNIFIER = 4100,                 ///< 放大镜
    MODAL_DIALOG = 5000                  ///< 模态对话框层
};

/**
 * @brief 组件类型定义
 */
enum class ComponentType {
    CANVAS,                    ///< 画布
    FLOAT_MENU,               ///< 浮动菜单
    CEF_WIDGET,               ///< CEF组件
    UI_PANEL,                 ///< UI面板
    PERFORMANCE_MONITOR,      ///< 性能监控
    BUTTON_PANEL,             ///< 按钮面板
    SIDEBAR,                  ///< 侧边栏
    MODAL_DIALOG              ///< 模态对话框
};

/**
 * @brief Z-Index管理器
 * 
 * 负责管理WhiteboardView中所有组件的层级关系，确保组件按照正确的Z-Order显示
 */
class ZIndexManager : public QObject, public IWindowManager
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit ZIndexManager(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ZIndexManager();

    /**
     * @brief 注册组件到指定层级
     * @param widget 要注册的组件
     * @param level Z-Index层级
     * @param type 组件类型
     * @param name 组件名称(用于调试)
     */
    void registerComponent(QWidget* widget, ZIndexLevel level, ComponentType type, const QString& name = QString());

    /**
     * @brief 预注册组件但不显示，用于避免自动置顶
     * @param widget 要管理的组件
     * @param level 层级
     * @param type 组件类型
     * @param name 组件名称（用于调试）
     */
    void preRegisterComponent(QWidget* widget, ZIndexLevel level, ComponentType type = ComponentType::UNKNOWN, const QString& name = "");

    /**
     * @brief 安全显示已预注册的组件，确保按正确层级显示
     * @param widget 要显示的组件
     */
    void safeShowComponent(QWidget* widget);

    /**
     * @brief 一步式安全注册并显示组件（推荐使用）
     * @param widget 要管理和显示的组件
     * @param level 层级
     * @param type 组件类型
     * @param name 组件名称（用于调试）
     */
    void registerAndShowComponent(QWidget* widget, ZIndexLevel level, ComponentType type = ComponentType::UNKNOWN, const QString& name = "");
    
    /**
     * @brief 注销组件
     * @param widget 要注销的组件
     */
    void unregisterComponent(QWidget* widget);
    
    /**
     * @brief 更新组件层级
     * @param widget 组件
     * @param newLevel 新的层级
     */
    void updateComponentLevel(QWidget* widget, ZIndexLevel newLevel);
    
    /**
     * @brief 应用所有组件的Z-Order
     */
    void applyZOrder();
    
    /**
     * @brief 临时置顶组件
     * @param widget 要置顶的组件
     */
    void bringToTop(QWidget* widget);
    
    /**
     * @brief 恢复组件到原始层级
     * @param widget 组件
     */
    void restoreOriginalLevel(QWidget* widget);
    
    /**
     * @brief 获取组件当前层级
     * @param widget 组件
     * @return 当前层级
     */
    ZIndexLevel getComponentLevel(QWidget* widget) const;
    
    /**
     * @brief 获取指定层级的所有组件
     * @param level 层级
     * @return 组件列表
     */
    QList<QWidget*> getComponentsAtLevel(ZIndexLevel level) const;
    
    /**
     * @brief 打印当前层级状态(调试用)
     */
    void printLevelStatus() const;
    
    /**
     * @brief 清理无效组件(已删除的组件)
     */
    void cleanupInvalidComponents();

    /**
     * @brief 检查组件是否为独立窗口
     * @param widget 组件
     * @return 是否为独立窗口
     */
    bool isIndependentWindow(QWidget* widget) const;

    /**
     * @brief 获取层级对应的窗口标志
     * @param level Z-Index层级
     * @return 窗口标志组合
     */
    Qt::WindowFlags getWindowFlagsForLevel(ZIndexLevel level) const;

    /**
     * @brief 应用窗口层级（独立窗口）
     * @param widget 独立窗口组件
     * @param level Z-Index层级
     */
    void applyWindowLevel(QWidget* widget, ZIndexLevel level);

    /**
     * @brief 应用子组件层级（传统Z-Order）
     * @param widget 子组件
     * @param level Z-Index层级
     */
    void applyChildLevel(QWidget* widget, ZIndexLevel level);

    /**
     * @brief 使用Windows API设置窗口层级
     * @param widget 窗口组件
     * @param level Z-Index层级
     */
    void setWindowLevelWithWinAPI(QWidget* widget, ZIndexLevel level);

    /**
     * @brief 禁用窗口的自动置顶行为
     * @param widget 窗口组件
     */
    void disableWindowAutoRaise(QWidget* widget);

    /**
     * @brief 使用DeferWindowPos批量设置所有独立窗口的层级
     * @param independentWindows 按层级排序的独立窗口列表
     */
    bool applyAllWindowLevels(const QList<QWidget*>& independentWindows);

    /**
     * @brief 强制重新应用层级（用于响应窗口激活事件）
     */
    void forceReapplyLevels();

    /**
     * @brief 检查窗口是否被ZIndexManager管理
     * @param hwnd Windows窗口句柄
     * @return 如果窗口被管理返回true
     */
#ifdef Q_OS_WIN
    bool isWindowManaged(HWND hwnd) const override;
#else
    bool isWindowManaged(void* hwnd) const override;
#endif



    /**
     * @brief 为窗口安装消息拦截器，阻止自动激活
     * @param widget 要保护的窗口
     * @param allowActivation 是否允许激活（某些窗口需要接收输入）
     */
    void installActivationBlocker(QWidget* widget, bool allowActivation = false);

public slots:
    /**
     * @brief 处理窗口可见性变化
     */
    void onWindowVisibilityChanged();

signals:
    /**
     * @brief 层级变化信号
     * @param widget 组件
     * @param oldLevel 旧层级
     * @param newLevel 新层级
     */
    void levelChanged(QWidget* widget, ZIndexLevel oldLevel, ZIndexLevel newLevel);

private:
    /**
     * @brief 组件信息结构
     */
    struct ComponentInfo {
        ZIndexLevel level;           ///< 当前层级
        ZIndexLevel originalLevel;   ///< 原始层级
        ComponentType type;          ///< 组件类型
        QString name;                ///< 组件名称
        bool isTemporaryTop;         ///< 是否临时置顶
        
        ComponentInfo() : level(ZIndexLevel::BOTTOM_LAYER_BASE), originalLevel(ZIndexLevel::BOTTOM_LAYER_BASE), 
                         type(ComponentType::UI_PANEL), isTemporaryTop(false) {}
        
        ComponentInfo(ZIndexLevel l, ComponentType t, const QString& n) 
            : level(l), originalLevel(l), type(t), name(n), isTemporaryTop(false) {}
    };
    
    /**
     * @brief 按层级排序组件
     * @param components 组件列表
     * @return 排序后的组件列表
     */
    QList<QWidget*> sortComponentsByLevel(const QList<QWidget*>& components) const;

private:
    QMap<QWidget*, ComponentInfo> m_components;    ///< 组件信息映射
    QMap<ZIndexLevel, QList<QWidget*>> m_levelMap; ///< 层级映射
    QSet<QWidget*> m_blockerInstalled;             ///< 已安装拦截器的窗口集合
    QMap<QWidget*, WindowActivationBlocker*> m_blockers; ///< 拦截器对象映射

#ifdef Q_OS_WIN
    QMap<HWND, QWidget*> m_hwndToWidget;           ///< 窗口句柄到组件的映射缓存
    QMap<QWidget*, HWND> m_widgetToHwnd;           ///< 组件到窗口句柄的反向映射缓存
#endif
};

#endif // ZINDEXMANAGER_H
