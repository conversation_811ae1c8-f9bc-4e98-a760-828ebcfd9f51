.flex[data-v-0eb1758c] {
  display: flex;
}
.flex-jc[data-v-0eb1758c] {
  justify-content: center;
}
.flex-ac[data-v-0eb1758c] {
  align-items: center;
}
.flex-v[data-v-0eb1758c] {
  flex-direction: column;
}
.flex-wrap[data-v-0eb1758c] {
  flex-wrap: wrap;
}
.active-scale[data-v-0eb1758c] {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.active-scale[data-v-0eb1758c]:active {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.pressed-scale[data-v-0eb1758c] {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.pressed-scale.pressed[data-v-0eb1758c] {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.text-ellipsis[data-v-0eb1758c] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis-2[data-v-0eb1758c] {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制文本为2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}
.save-blackboard-item[data-v-0eb1758c] {
  width: 10.16667rem;
  height: 10.16667rem;
  background-color: var(--color-white);
  border-radius: 1.16667rem;
  border: 0rem solid #979797;
  overflow: hidden;
  position: relative;
}
.save-blackboard-item + .save-blackboard-item[data-v-0eb1758c] {
  margin-left: 3.16667rem;
}
.save-blackboard-text[data-v-0eb1758c] {
  font-size: 0.75rem;
  color: var(--color-black-light-65);
  height: 0.75rem;
  line-height: 0.75rem;
  text-align: center;
}
.save-blackboard-qrcode[data-v-0eb1758c] {
  width: 6.66667rem;
  height: 6.66667rem;
  margin: 1.08333rem 0 0.75rem;
}
.save-blackboard-icon[data-v-0eb1758c] {
  width: 6.25rem;
  height: 5.66667rem;
  background: url('../webp/<EMAIL>') no-repeat center center;
  background-size: 6.25rem 4.91667rem;
  margin: 1.58333rem 0 1.25rem;
  cursor: pointer;
}
.save-blackboard-sync[data-v-0eb1758c] {
  margin: 1.58333rem auto 1.58333rem;
  font-weight: 400;
  font-size: 1.16667rem;
  color: #ffffff;
  line-height: 1.66667rem;
  cursor: pointer;
}
.save-blackboard-sync .radio-container[data-v-0eb1758c] {
  width: 1.16667rem;
  height: 1.16667rem;
  margin-right: 0.58333rem;
  line-height: 1.66667rem;
}
.save-blackboard-sync .radio-container[data-v-0eb1758c] .radio-icon {
  vertical-align: top;
}
.save-blackboard-split[data-v-0eb1758c] {
  position: relative;
}
.save-blackboard-split[data-v-0eb1758c]::after {
  position: absolute;
  left: 3rem;
  right: 3rem;
  content: '';
  display: block;
  height: 0.08333rem;
  background-image: linear-gradient(to right, var(--color-white) 50%, transparent 50%);
  /* 创建水平虚线 */
  background-size: 0.76667rem 0.76667rem;
  /* 每个虚线段的长度为 10px，间距为 20px */
  background-repeat: repeat-x;
  /* 垂直重复虚线 */
  opacity: 0.25;
}
.save-blackboard-exit[data-v-0eb1758c] {
  padding: 1.58333rem 0;
}
.save-blackboard-exit .save-blackboard-btn[data-v-0eb1758c] {
  cursor: pointer;
  color: var(--color-white-light-65);
  font-size: 1rem;
  line-height: 1rem;
}
.save-blackboard-exit .svg-icon[data-v-0eb1758c] {
  margin-left: 0.58333rem;
}
.custom-text-loading[data-v-0eb1758c] {
  font-size: 1.16667rem;
  background-color: rgba(255, 255, 255, 0.8);
  color: #409EFF;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.custom-text-loading__round[data-v-0eb1758c] {
  width: 0.83333rem;
}
.save-black-board-dialog[data-v-8e9c2de9] {
  z-index: var(--z-index-dialog);
}
.save-black-board-dialog[data-v-8e9c2de9] .dialog-title {
  height: 1.91667rem;
  font-size: 1.58333rem;
  line-height: 1.91667rem;
  letter-spacing: 0.08333rem;
  padding-bottom: 1.91667rem;
  padding-top: 1.58333rem;
}
.save-black-board-dialog[data-v-8e9c2de9] .dialog-container {
  min-width: 29.16667rem;
  min-height: 17.91667rem;
}
.save-black-board-dialog.positino-right[data-v-8e9c2de9] .dialog-container {
  position: absolute;
  right: 10.16667rem;
  top: 27.16667rem;
}
