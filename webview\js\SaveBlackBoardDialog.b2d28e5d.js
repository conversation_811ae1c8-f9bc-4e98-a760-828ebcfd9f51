import { j as arrayBufferToBase<PERSON>, l as logger, a as Bridge, r as reportTrackEvent, B as BridgeZmqUtils, W as WarnError, g as getDeviceIdWidget, v as v4, _ as _export_sfc, h as getWindowUrlParams, c as setBusinessInfoWidget, d as initLoggerWidget, p as pinia } from "./index.2e7c5603.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, r as ref, $ as onUnmounted, b as openBlock, m as createElementBlock, j as createBaseVNode, G as createTextVNode, H as toDisplayString, D as createCommentVNode, p as createVNode, F as Fragment, n as nextTick, c as computed, z as onMounted, e as createBlock, f as withCtx, u as unref, k as normalizeClass, ad as createApp } from "./bootstrap.ab073eb8.js";
import { D as Dialog } from "./index.533b593c.js";
import { R as ResourceTypeEnum } from "./IResource.516d6004.js";
import { S as SvgIcon } from "./index.e0df5fdb.js";
import { Q as QrCode } from "./index.52d22769.js";
import { R as Radio, u as useThemeStore } from "./theme.f3891772.js";
import { f as getFileNameWithoutExtension, s as syncPrepareLessons, h as uploadFiles, i as genQrcodeShareTempId } from "./resource.27b0a821.js";
import { E as EVENT_CODE } from "./bury.7c4aa589.js";
import { $ as $post } from "./axios.0b09fd7a.js";
import { O as OfficeViewSDK } from "./OfficeViewSDK.d6a90ca5.js";
import { l as loadImage, c as canvasToImageBlob } from "./image.77fe4778.js";
import { s as showWarning, b as showSuccess, a as showError } from "./toastWidget.2e2ba590.js";
import { C as CEF_RENDERER_MESSAGE_TYPE, Q as QT_CEF_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.1c1fd1ce.js";
import "./crypto-js.7319a219.js";
import "./toast.6c42a63f.js";
import "./hlwhiteboard.b54f17ff.js";
const sysUrl = "/zhhb-system";
async function pptConvertJpg(data) {
  try {
    const taskResult = await $post(
      `${sysUrl}/v1/teaching/file/convert_jpg`,
      data
    );
    const taskId = taskResult.data["task_id"];
    if (!taskId) {
      return Promise.reject("转换失败，taskId为空");
    }
    return new Promise((resolve, reject) => {
      const queryResult = async () => {
        const queryResult2 = (await $post(`${sysUrl}/v1/teaching/file/convert_task_query`, {
          taskId
        })).data;
        if (queryResult2.progress === 100) {
          clearInterval(intervalIndex);
          clearTimeout(timeoutIndex);
          resolve(queryResult2.result.images);
        } else {
          console.log("转换中...", queryResult2.progress);
        }
      };
      const intervalIndex = setInterval(queryResult, 3e3);
      const timeoutIndex = setTimeout(() => {
        clearInterval(intervalIndex);
        reject("转换超时");
      }, 6e4);
    });
  } catch (e) {
    console.log(e);
    return Promise.reject("转换失败");
  }
}
function isWYAndPPT(resource) {
  const info = resource.info;
  return resource.type === ResourceTypeEnum.PPT && info.transThirdName === "WY";
}
async function genResourceBlackBoardWebView(resource, indexList, imageList, width, height) {
  const canvasEl = document.createElement("canvas");
  try {
    let drawImageContain = function(ctx2, image, canvasWidth, canvasHeight) {
      const imgAspect = image.width / image.height;
      const canvasAspect = canvasWidth / canvasHeight;
      let drawWidth, drawHeight, offsetX, offsetY;
      if (imgAspect > canvasAspect) {
        drawWidth = canvasWidth;
        drawHeight = canvasWidth / imgAspect;
        offsetX = 0;
        offsetY = (canvasHeight - drawHeight) / 2;
      } else {
        drawHeight = canvasHeight;
        drawWidth = canvasHeight * imgAspect;
        offsetX = (canvasWidth - drawWidth) / 2;
        offsetY = 0;
      }
      ctx2.drawImage(image, offsetX, offsetY, drawWidth, drawHeight);
    };
    canvasEl.style.position = "absolute";
    canvasEl.style.zIndex = "9999";
    canvasEl.style.top = "-9999px";
    canvasEl.style.left = "-9999px";
    canvasEl.width = width;
    canvasEl.height = height;
    document.body.appendChild(canvasEl);
    const ctx = canvasEl.getContext("2d");
    if (!ctx) {
      throw new Error("canvas未定义");
    }
    const info = resource.info;
    const files = [];
    const isWYAndPPTRes = isWYAndPPT(resource);
    const canvasToImageFile = async (pageNum, index, options) => {
      ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
      const { imageUrl, color, filename } = options || {};
      if (imageUrl) {
        const image = await loadImage(imageUrl);
        drawImageContain(ctx, image, canvasEl.width, canvasEl.height);
      } else if (color) {
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, canvasEl.width, canvasEl.height);
      }
      if (imageList[index]) {
        const image = await loadImage(imageList[index]);
        ctx.drawImage(image, 0, 0, canvasEl.width, canvasEl.height);
      }
      const dataURL = await canvasToImageBlob(canvasEl, "image/jpeg", 1);
      let file = null;
      if (dataURL) {
        file = new File([dataURL], filename || `${pageNum}.jpeg`, {
          type: "image/jpeg",
          lastModified: Date.now()
        });
      }
      return file;
    };
    const _screenshot = async (pageNum, index) => {
      if ([ResourceTypeEnum.PDF, ResourceTypeEnum.PPT].includes(resource.type)) {
        const fullThumbnailUrl = OfficeViewSDK.getThumbnailUrl(info);
        return await canvasToImageFile(pageNum, index, {
          imageUrl: `${fullThumbnailUrl}${pageNum}.jpg`
        });
      }
      if (resource.type === ResourceTypeEnum.WHITEBOARD) {
        return await canvasToImageFile(pageNum, index, {
          color: "#234641",
          filename: `第${pageNum}页白板.jpeg`
        });
      }
      return await canvasToImageFile(pageNum, index);
    };
    if (isWYAndPPTRes) {
      if (indexList.length) {
        const imagesList = await pptConvertJpg({
          url: info.documentUrl,
          fileName: info.documentName,
          ranges: indexList
        });
        for (let i = 0; i < imagesList.length; i++) {
          const index = indexList[i];
          const imageUrl = imagesList[i].url;
          const file = await canvasToImageFile(index, i, {
            imageUrl
          });
          if (file) {
            files.push(file);
          }
        }
      }
    } else {
      for (const [index, pageNum] of indexList.entries()) {
        const file = await _screenshot(pageNum, index);
        if (file) {
          files.push(file);
        }
      }
    }
    return files;
  } finally {
    canvasEl.parentElement?.removeChild(canvasEl);
  }
}
async function qtSaveFile(file, filePath, options) {
  const { chunkSize = 100 * 1024 } = options || {};
  const totalChunks = Math.ceil(file.size / chunkSize);
  for (let i = 0; i < totalChunks; i++) {
    let base64Chunk = await file.slice(i * chunkSize, (i + 1) * chunkSize);
    const chunkIndex = i + 1;
    const chunk = {
      filePath,
      chunkIndex,
      chunkContent: arrayBufferToBase64(await base64Chunk.arrayBuffer())
    };
    const info = `第${i + 1}块, 块大小：${base64Chunk.size}，总数量: ${totalChunks}, 总长度：${file.size}`;
    base64Chunk = null;
    if (totalChunks === chunkIndex) {
      chunk["totalChunks"] = totalChunks;
    }
    logger.info("【jsbridge文件传输】", "开始传输", info);
    await Bridge.getInstance().call("saveFileChunk", chunk);
    logger.info("【jsbridge文件传输】", "传输结束", info);
  }
}
const _hoisted_1 = { class: "save-blackboard flex flex-jc" };
const _hoisted_2 = {
  key: 0,
  class: "custom-text-loading"
};
const _hoisted_3 = { class: "custom-text-loading__round" };
const _hoisted_4 = { class: "save-blackboard-item flex flex-v flex-ac" };
const _hoisted_5 = { class: "save-blackboard-qrcode" };
const _hoisted_6 = {
  key: 0,
  class: "custom-text-loading"
};
const _hoisted_7 = { class: "custom-text-loading__round" };
const _hoisted_8 = {
  key: 0,
  class: "save-blackboard-sync flex flex-ac flex-jc"
};
const _hoisted_9 = {
  key: 1,
  class: "save-blackboard-split"
};
const _hoisted_10 = {
  key: 2,
  class: "save-blackboard-exit flex flex-jc"
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "SaveBlackBoard",
  props: {
    resource: {},
    currentResourceIndex: {},
    hasTraceList: {},
    width: {},
    height: {},
    classTeacher: {},
    showExit: { type: Boolean },
    showSync: { type: Boolean }
  },
  emits: ["close", "exit"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const isSync = ref(true);
    const qrcodeLoading = ref(true);
    const saveLoading = ref(false);
    const loadingText = ref("...");
    let loadingCount = 0;
    const loadingIntervalClear = setInterval(() => {
      const num = loadingCount % 3 + 1;
      loadingText.value = ".".repeat(num);
      loadingCount++;
    }, 1e3);
    onUnmounted(() => {
      clearInterval(loadingIntervalClear);
    });
    const qrcodeBoardTempId = ref("");
    const qrcodeText = ref("");
    const taskResultPromise = ref(null);
    const genQrcodeShareUrl = async () => {
      if (!qrcodeBoardTempId.value) {
        return "";
      }
      const url = new URL("http://10.30.6.7/black-board-mobile/snapshot");
      url.searchParams.set("qrcodeBoardTempId", qrcodeBoardTempId.value);
      url.searchParams.set("staffId", await getDeviceIdWidget() || "");
      url.searchParams.set("userName", props.classTeacher.userName);
      url.searchParams.set("saasClassId", props.classTeacher.saasClassId);
      url.searchParams.set("saasUserId", props.classTeacher.saasUserId);
      url.searchParams.set("saasSchoolId", props.classTeacher.saasSchoolId);
      if (isSync.value && props.resource) {
        const { info } = props.resource;
        url.searchParams.set("sync", "1");
        url.searchParams.set("teachPlanId", info?.teachPlanId || "");
        url.searchParams.set("directoryId", info?.directoryId || "");
      }
      console.log("生成URL地址：", url.toString());
      return url.toString();
    };
    const onClickSyncBtn = async () => {
      isSync.value = !isSync.value;
      qrcodeText.value = await genQrcodeShareUrl();
      console.log("点击切换", qrcodeText.value);
    };
    const genBlackBoard = async (resource) => {
      let files = [];
      try {
        const hasTraceList = props.hasTraceList;
        if (!hasTraceList.length) {
          return [];
        }
        const genWhiteboardParams = [];
        if (resource?.type === ResourceTypeEnum.WHITEBOARD) {
          genWhiteboardParams.push({
            i: 0,
            index: hasTraceList[0]?.key,
            filename: v4() + ".png"
          });
        } else {
          hasTraceList.forEach((item) => {
            genWhiteboardParams.push({
              i: item.index,
              index: item.key,
              filename: v4() + ".png"
            });
          });
        }
        logger.info("【保存板书】", "生成白板图片参数", genWhiteboardParams);
        const imageList = await Bridge.getInstance().call(
          QT_CEF_MESSAGE_TYPE.QT_GET_TRACE_IMAGE_LIST,
          genWhiteboardParams
        );
        logger.info("【保存板书】", "生成白板图片", imageList);
        const indexList = hasTraceList.map((item) => item.index);
        files = await genResourceBlackBoardWebView(
          resource,
          indexList,
          imageList.map((item) => item.imageUrl),
          props.width,
          props.height
        );
      } catch (e) {
        throw new WarnError("生成资源板书失败", e);
      }
      return files;
    };
    const saveBlackBoardToCloud = async (files, resource) => {
      try {
        const resList = await uploadFiles(files, {
          group: `screenshot/${getFileNameWithoutExtension(resource.info.documentName)}`
        });
        return resList;
      } catch (e) {
        throw new WarnError("文件上传失败", e);
      }
    };
    const getQrcodeBoardTempId = async (uploadResList, resource) => {
      try {
        const resourceName = getFileNameWithoutExtension(resource.info.documentName);
        const { data } = await genQrcodeShareTempId({
          boardList: uploadResList.map((res) => {
            return {
              boardImgUrl: res.fileUrl,
              boardName: `${resourceName}-${res.originFileName}`
            };
          }),
          isSyncJzjx: false,
          saasSchoolId: props.classTeacher.saasSchoolId,
          saasUserId: props.classTeacher.saasUserId,
          teachPlanId: resource.info.teachPlanId,
          directoryId: resource.info.directoryId
        });
        return data.qrcodeBoardTempId;
      } catch (e) {
        throw new WarnError("获取分享白板临时id失败", e);
      }
    };
    const genTask = async (resource) => {
      const files = await genBlackBoard(resource);
      const uploadResList = await saveBlackBoardToCloud(files, resource);
      qrcodeBoardTempId.value = await getQrcodeBoardTempId(uploadResList, resource);
      return {
        files,
        uploadResList,
        qrcodeBoardTempId: qrcodeBoardTempId.value
      };
    };
    const saveBlackBoardToLocal = async (files, resource) => {
      if (!files.length) {
        showWarning("没有可保存的板书");
        return;
      }
      const dirRes = await Bridge.getInstance().call("showSaveFileDialog", {
        dialogTitle: "保存板书"
      });
      const dir = dirRes?.selectedDir;
      if (!dir) {
        return;
      }
      const { info } = resource;
      let dirName = "";
      if (resource.type !== ResourceTypeEnum.WHITEBOARD) {
        dirName = getFileNameWithoutExtension(info.documentName);
      }
      for (const file of files) {
        let filePath = dir;
        if (dirName) {
          filePath += `/${dirName}`;
        }
        const isWhiteboard = resource.type === ResourceTypeEnum.WHITEBOARD;
        filePath += `/${isWhiteboard ? file.name : `第${getFileNameWithoutExtension(file.name)}页板书.jpeg`}`;
        logger.info("【保存文件地址】", filePath);
        await qtSaveFile(file, filePath);
      }
      return true;
    };
    const saveBlackBoardToPrepareLessons = async (uploadResList, resource) => {
      try {
        const resourceName = getFileNameWithoutExtension(resource.info.documentName);
        await syncPrepareLessons({
          boardList: uploadResList.map((res) => {
            return {
              boardImgUrl: res.fileUrl,
              boardName: `${resourceName}-${res.originFileName}`
            };
          }),
          saasSchoolId: props.classTeacher.saasSchoolId,
          saasUserId: props.classTeacher.saasUserId,
          teachPlanId: resource.info.teachPlanId,
          directoryId: resource.info.directoryId,
          qrcodeBoardTempId: qrcodeBoardTempId.value,
          staffId: await getDeviceIdWidget()
        });
      } catch (e) {
        throw new WarnError("保存到备课中心失败", e);
      }
    };
    const onSave = async () => {
      if (saveLoading.value) {
        showWarning("当前已有保存中任务,请稍后");
        return;
      }
      if (!props.resource) {
        showWarning("当前无资源");
        return;
      }
      let _taskResultPromise = null;
      try {
        saveLoading.value = true;
        const resource = props.resource;
        if (!taskResultPromise.value) {
          taskResultPromise.value = genTask(resource);
        }
        _taskResultPromise = taskResultPromise.value;
        const { files, uploadResList } = await taskResultPromise.value;
        if (_taskResultPromise !== taskResultPromise.value) {
          return;
        }
        const saveRes = await saveBlackBoardToLocal(files, resource);
        if (!saveRes) {
          emits("close");
          return;
        }
        if (isSync.value) {
          await saveBlackBoardToPrepareLessons(uploadResList, resource);
        }
        resource.saved = true;
        showSuccess("保存成功");
        console.log("【保存板书】", "保存成功", files, resource);
        reportTrackEvent(EVENT_CODE.SAVE_BLACKBOARD_WRITING.code, {
          saveType: "local_save"
        });
        BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_RESOURCE_SAVED);
        if (props.showExit) {
          emits("exit");
        } else {
          emits("close");
        }
      } catch (e) {
        if (e instanceof WarnError) {
          logger.warn("【保存板书】", e.message, e.err);
        } else {
          logger.error("【保存板书】", "未知报错异常", e);
        }
        showError("保存失败");
      } finally {
        saveLoading.value = false;
      }
    };
    const startGen = async (enableSync = false) => {
      let _taskResultPromise = null;
      try {
        isSync.value = enableSync;
        saveLoading.value = false;
        qrcodeBoardTempId.value = "";
        qrcodeText.value = "";
        taskResultPromise.value = null;
        qrcodeLoading.value = true;
        await nextTick();
        const resource = props.resource;
        if (!resource) {
          return;
        }
        taskResultPromise.value = genTask(resource);
        _taskResultPromise = taskResultPromise.value;
        await taskResultPromise.value;
        if (_taskResultPromise !== taskResultPromise.value) {
          return;
        }
        qrcodeText.value = await genQrcodeShareUrl();
        console.log("首次生成二维码成功", qrcodeText.value);
      } catch (e) {
        if (e instanceof WarnError) {
          logger.warn("【保存板书】", e.message, e.err);
        } else {
          logger.warn("【保存板书】", "二维码生成失败", e);
        }
        showError("二维码生成失败");
      } finally {
        if (_taskResultPromise === taskResultPromise.value) {
          qrcodeLoading.value = false;
        }
      }
    };
    __expose({
      startGen
    });
    return (_ctx, _cache) => {
      const _component_SvgIcon = SvgIcon;
      return openBlock(), createElementBlock(Fragment, null, [
        createBaseVNode("div", _hoisted_1, [
          createBaseVNode("div", {
            class: "save-blackboard-item flex flex-v flex-ac",
            onClick: onSave
          }, [
            _cache[2] || (_cache[2] = createBaseVNode("div", { class: "save-blackboard-icon" }, null, -1)),
            _cache[3] || (_cache[3] = createBaseVNode("div", { class: "save-blackboard-text" }, "保存至一体机本地", -1)),
            saveLoading.value ? (openBlock(), createElementBlock("div", _hoisted_2, [
              _cache[1] || (_cache[1] = createTextVNode("保存中")),
              createBaseVNode("div", _hoisted_3, toDisplayString(loadingText.value), 1)
            ])) : createCommentVNode("", true)
          ]),
          createBaseVNode("div", _hoisted_4, [
            createBaseVNode("div", _hoisted_5, [
              createVNode(QrCode, {
                text: qrcodeText.value,
                options: {
                  margin: 0,
                  errorCorrectionLevel: "H"
                }
              }, null, 8, ["text"])
            ]),
            _cache[5] || (_cache[5] = createBaseVNode("div", { class: "save-blackboard-text" }, "手机扫一扫，保存至手机", -1)),
            qrcodeLoading.value ? (openBlock(), createElementBlock("div", _hoisted_6, [
              _cache[4] || (_cache[4] = createTextVNode("生成中")),
              createBaseVNode("div", _hoisted_7, toDisplayString(loadingText.value), 1)
            ])) : createCommentVNode("", true)
          ])
        ]),
        props.showSync ? (openBlock(), createElementBlock("div", _hoisted_8, [
          createVNode(Radio, {
            "model-value": isSync.value,
            onClick: onClickSyncBtn
          }, null, 8, ["model-value"]),
          createBaseVNode("span", { onClick: onClickSyncBtn }, "同步云存储至备课中心素材")
        ])) : createCommentVNode("", true),
        props.showSync && props.showExit ? (openBlock(), createElementBlock("div", _hoisted_9)) : createCommentVNode("", true),
        props.showExit ? (openBlock(), createElementBlock("div", _hoisted_10, [
          createBaseVNode("div", {
            class: "save-blackboard-btn",
            onClick: _cache[0] || (_cache[0] = ($event) => emits("exit"))
          }, [
            _cache[6] || (_cache[6] = createTextVNode(" 不用保存，直接离开")),
            createVNode(_component_SvgIcon, { "icon-class": "tuichu" })
          ])
        ])) : createCommentVNode("", true)
      ], 64);
    };
  }
});
const SaveBlackBoard_vue_vue_type_style_index_0_scoped_0eb1758c_lang = "";
const SaveBlackBoard = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-0eb1758c"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const urlParams = getWindowUrlParams();
    logger.info("【保存板书】", "显示", urlParams);
    const currentResource = ref(null);
    const currentResourceIndex = ref(0);
    const whiteboardWidth = ref(1920);
    const whiteboardHeight = ref(1080);
    const saveBlackBoardDialogVisible = ref(true);
    const blackBoardPositionStyle = ref();
    const saveBlackBoardRef = ref();
    const classTeacher = ref();
    const themeStore = useThemeStore();
    const hasTraceList = ref([]);
    const isCanSyncBlackBoard = computed(() => {
      const _currentResource = currentResource.value;
      if (!_currentResource) {
        return false;
      }
      if (![ResourceTypeEnum.PPT, ResourceTypeEnum.PDF].includes(_currentResource.type)) {
        return false;
      }
      const { teachPlanId, directoryId } = _currentResource.info;
      return !!(teachPlanId && directoryId);
    });
    if (!urlParams.showBlackBoardExit) {
      const scale = themeStore.scale;
      let left = (Number(urlParams.left) || 0) - 175 * scale;
      let top = (Number(urlParams.top) || 0) - 125 * scale;
      const maxLeft = document.body.clientWidth - 350 * scale;
      const maxTop = document.body.clientHeight - 250 * scale;
      if (left < 0) {
        left = 0;
      }
      if (top < 0) {
        top = 0;
      }
      if (left > maxLeft) {
        left = maxLeft;
      }
      if (top > maxTop) {
        top = maxTop;
      }
      blackBoardPositionStyle.value = {
        position: "absolute",
        top: top + "px",
        left: left + "px"
      };
    }
    const onExitHome = () => {
      Bridge.getInstance().callVoid(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_EXIT);
    };
    const onClose = () => {
      Bridge.getInstance().callVoid("close");
    };
    const init = async () => {
      try {
        if (!saveBlackBoardRef.value) {
          logger.warn("【SaveBlackBoardDialog】", "SaveBlackBoardDialog组件未渲染");
          return;
        }
        const res = await BridgeZmqUtils.callEelectronRenderer(
          CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS
        );
        classTeacher.value = res.teacher || {};
        currentResource.value = res.currentResource || {};
        currentResourceIndex.value = res.currentResourceIndex || 0;
        const data = await Bridge.getInstance().call(
          QT_CEF_MESSAGE_TYPE.QT_GET_CURRENT_RESOURCE_TRACE_LIST,
          {
            resourceId: currentResource.value?.id
          }
        );
        let keys = data?.keys;
        if (!Array.isArray(keys)) {
          keys = [];
        }
        const list = [];
        keys.forEach((item) => {
          const res2 = String(item).match(/([^_]+)_(\d+)/);
          if (res2) {
            list.push({
              index: Number(res2[2]),
              key: item
            });
          }
        });
        hasTraceList.value = list;
        logger.info("【qt.getCurrentResourceTraceList】", "有轨迹索引", data);
        await nextTick();
        saveBlackBoardRef.value.startGen(isCanSyncBlackBoard.value);
      } catch (e) {
        logger.error("[SaveBlackBoardDialog]", "获取保存板书参数失败", e);
      }
    };
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
    });
    onMounted(() => {
      init();
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher2 = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher2.saasSchoolId,
          campusId: classTeacher2.saasCampusId,
          classId: classTeacher2.saasClassId,
          className: classTeacher2.saasClassName,
          subjectCode: classTeacher2.saasSubjectCode,
          subjectName: classTeacher2.saasSubjectName,
          userId: classTeacher2.saasUserId
        });
      }).catch((e) => {
        logger.error("【保存板书】", "获取当前上课信息失败", e);
      });
    });
    return (_ctx, _cache) => {
      const _component_Dialog = Dialog;
      return openBlock(), createBlock(_component_Dialog, {
        class: normalizeClass(["save-black-board-dialog", {
          "positino-right": !!unref(urlParams).showBlackBoardExit
        }]),
        "container-style": blackBoardPositionStyle.value,
        title: "是否需要保存板书",
        visible: saveBlackBoardDialogVisible.value,
        "show-close": "",
        onClose
      }, {
        default: withCtx(() => [
          createVNode(SaveBlackBoard, {
            ref_key: "saveBlackBoardRef",
            ref: saveBlackBoardRef,
            "class-teacher": classTeacher.value,
            resource: currentResource.value,
            width: whiteboardWidth.value,
            height: whiteboardHeight.value,
            "show-exit": !!unref(urlParams).showBlackBoardExit,
            "show-sync": isCanSyncBlackBoard.value,
            "has-trace-list": hasTraceList.value,
            "current-resource-index": currentResourceIndex.value,
            onExit: onExitHome,
            onClose
          }, null, 8, ["class-teacher", "resource", "width", "height", "show-exit", "show-sync", "has-trace-list", "current-resource-index"])
        ]),
        _: 1
      }, 8, ["class", "container-style", "visible"]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_8e9c2de9_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-8e9c2de9"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
