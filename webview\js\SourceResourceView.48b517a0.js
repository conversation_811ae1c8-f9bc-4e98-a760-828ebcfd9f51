import { h as getWindowUrlParams, B as BridgeZmqUtils, c as setBusinessInfoWidget, l as logger, _ as _export_sfc, d as initLoggerWidget, p as pinia } from "./index.2e7c5603.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, z as onMounted, b as openBlock, m as createElementBlock, u as unref, j as createBaseVNode, G as createTextVNode, ad as createApp } from "./bootstrap.ab073eb8.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import { R as ResourceTypeEnum } from "./IResource.516d6004.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
const _hoisted_1 = { class: "source-resource-item" };
const _hoisted_2 = ["src"];
const _hoisted_3 = {
  key: 1,
  class: "source-audio-wrap"
};
const _hoisted_4 = {
  controls: "",
  class: "source-audio"
};
const _hoisted_5 = ["src"];
const _hoisted_6 = {
  key: 2,
  class: "source-video",
  controls: ""
};
const _hoisted_7 = ["src"];
const _hoisted_8 = { key: 3 };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const urlParams = getWindowUrlParams();
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
    });
    onMounted(() => {
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
      }).catch((e) => {
        logger.error("【媒体预览】", "获取当前上课信息失败", e);
      });
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        unref(urlParams).type === unref(ResourceTypeEnum).IMAGE ? (openBlock(), createElementBlock("img", {
          key: 0,
          class: "el-image",
          src: unref(urlParams).url,
          style: { "object-fit": "scale-down" }
        }, null, 8, _hoisted_2)) : unref(urlParams).type === unref(ResourceTypeEnum).AUDIO ? (openBlock(), createElementBlock("div", _hoisted_3, [
          createBaseVNode("audio", _hoisted_4, [
            createBaseVNode("source", {
              src: unref(urlParams).url,
              type: "audio/mp3"
            }, null, 8, _hoisted_5),
            _cache[0] || (_cache[0] = createTextVNode(" 您的浏览器不支持音频播放。 "))
          ])
        ])) : unref(urlParams).type === unref(ResourceTypeEnum).VIDEO ? (openBlock(), createElementBlock("video", _hoisted_6, [
          createBaseVNode("source", {
            src: unref(urlParams).url,
            type: "video/mp4"
          }, null, 8, _hoisted_7),
          _cache[1] || (_cache[1] = createTextVNode(" 您的浏览器不支持视频播放。 "))
        ])) : (openBlock(), createElementBlock("div", _hoisted_8, "未知文件类型无法播放"))
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_7b65b8c6_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-7b65b8c6"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
