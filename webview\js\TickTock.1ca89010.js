import { _ as _export_sfc, l as logger, a as Bridge, B as BridgeZmqUtils, c as setBusinessInfoWidget, d as initLoggerWidget, p as pinia } from "./index.2e7c5603.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, r as ref, w as watch, c as computed, o as onBeforeUnmount, b as openBlock, m as createElementBlock, D as createCommentVNode, j as createBaseVNode, H as toDisplayString, u as unref, h as withDirectives, F as Fragment, Q as resolveDirective, p as createVNode, k as normalizeClass, z as onMounted, $ as onUnmounted, v as vShow, R as renderList, l as normalizeStyle, e as createBlock, G as createTextVNode, n as nextTick, ad as createApp } from "./bootstrap.ab073eb8.js";
import { S as SvgIcon } from "./index.e0df5fdb.js";
import { a as showError } from "./toastWidget.2e2ba590.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import { s as stopDrag } from "./stopDrag.9e5623d9.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
const _imports_0 = "data:image/webp;base64,UklGRhAHAABXRUJQVlA4WAoAAAAQAAAA7wAAuwAAQUxQSPQBAAABkKNs25NZr9XH14qtWM7ZhZ3d17iKycGNCFq7CFt7F6BgGUCwE8Ryku9EI/ic/EfEBNDahnMTJnndPfLhfezqPAlvHIN2LJWfjXy4x8xXcheaHQ182IfI1rYmzks++OW52JL0Wgaw9eRmVMUgVmobVsxAxtYG3J6h7N21RMBwBmIVPWVAU30Fs2BIC3OxHw2D2pwtZDYMa2MuohcMbKEvIFKGNhXzAgY3mOUyvO4Mq8ent6bFDHA8STHEaoKsMarkdx6D7H0jWpRa8dU5w3z+hVbiVGqfbAba/hQhFRGRHJAaJJFiqBWRj5VPlGGVkTFiNRoOg+******************************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";
const _imports_1 = "data:image/webp;base64,UklGRloHAABXRUJQVlA4WAoAAAAQAAAA7wAAuwAAQUxQSPQBAAABkKNs25NZr9XH14qtWM7ZhZ3d17iKycGNCFq7CFt7F6BgGUCwE8Ryku9EI/ic/EfEBNDahnMTJnndPfLhfezqPAlvHIN2LJWfjXy4x8xXcheaHQ182IfI1rYmzks++OW52JL0Wgaw9eRmVMUgVmobVsxAxtYG3J6h7N21RMBwBmIVPWVAU30Fs2BIC3OxHw2D2pwtZDYMa2MuohcMbKEvIFKGNhXzAgY3mOUyvO4Mq8ent6bFDHA8STHEaoKsMarkdx6D7H0jWpRa8dU5w3z+hVbiVGqfbAba/hQhFRGRHJAaJJFiqBWRj5VPlGGVkTFiNRoOg+******************************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";
const _imports_2 = "data:image/webp;base64,UklGRvwJAABXRUJQVlA4WAoAAAAQAAAA7wAAuwAAQUxQSPQBAAABkKNs25NZr9XH14qtWM7ZhZ3d17iKycGNCFq7CFt7F6BgGUCwE8Ryku9EI/ic/EfEBNDahnMTJnndPfLhfezqPAlvHIN2LJWfjXy4x8xXcheaHQ182IfI1rYmzks++OW52JL0Wgaw9eRmVMUgVmobVsxAxtYG3J6h7N21RMBwBmIVPWVAU30Fs2BIC3OxHw2D2pwtZDYMa2MuohcMbKEvIFKGNhXzAgY3mOUyvO4Mq8ent6bFDHA8STHEaoKsMarkdx6D7H0jWpRa8dU5w3z+hVbiVGqfbAba/hQhFRGRHJAaJJFiqBWRj5VPlGGVkTFiNRoOg+******************************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";
var TickTockStatusEnum = /* @__PURE__ */ ((TickTockStatusEnum2) => {
  TickTockStatusEnum2["IDLE"] = "idle";
  TickTockStatusEnum2["RUNNING"] = "running";
  TickTockStatusEnum2["PAUSED"] = "paused";
  TickTockStatusEnum2["END"] = "end";
  return TickTockStatusEnum2;
})(TickTockStatusEnum || {});
const _hoisted_1$2 = {
  key: 0,
  class: "timer-title"
};
const _hoisted_2$2 = { class: "timer-container flex flex-ac" };
const _hoisted_3$2 = {
  key: 0,
  class: "timer-hour flex"
};
const _hoisted_4$1 = {
  key: 0,
  class: "timer-text"
};
const _hoisted_5$1 = { class: "timer-text" };
const _hoisted_6$1 = {
  key: 1,
  class: "count-dot-colon"
};
const _hoisted_7$1 = { class: "timer-minute flex" };
const _hoisted_8$1 = { class: "timer-text" };
const _hoisted_9$1 = { class: "timer-text" };
const _hoisted_10$1 = { class: "timer-second flex" };
const _hoisted_11$1 = { class: "timer-text" };
const _hoisted_12$1 = { class: "timer-text" };
const _hoisted_13$1 = { class: "timer-controller flex flex-ac flex-jc" };
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "Timer",
  props: {
    isFullScreen: {
      type: Boolean,
      default: false
    }
  },
  emits: ["status-change", "open-fullscreen"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const status = ref(TickTockStatusEnum.IDLE);
    const startTime = ref(null);
    const elapsedMs = ref(0);
    const timer = ref(null);
    let isAutoFullScreen = true;
    watch(status, (newStatus) => {
      emit("status-change", newStatus);
    });
    function tick() {
      if (!startTime.value)
        return;
      const now = Date.now();
      elapsedMs.value = now - startTime.value;
      if (elapsedMs.value >= 3e3 && isAutoFullScreen) {
        emit("open-fullscreen");
        isAutoFullScreen = false;
      }
      if (elapsedMs.value >= 864e5 || elapsedMs.value < 0) {
        elapsedMs.value = 0;
        startTime.value = Date.now();
      }
      timer.value = requestAnimationFrame(tick);
    }
    function start() {
      if (status.value !== TickTockStatusEnum.IDLE)
        return;
      status.value = TickTockStatusEnum.RUNNING;
      elapsedMs.value = 0;
      startTime.value = Date.now();
      isAutoFullScreen = true;
      tick();
    }
    function pause() {
      if (status.value !== TickTockStatusEnum.RUNNING)
        return;
      status.value = TickTockStatusEnum.PAUSED;
      if (timer.value) {
        cancelAnimationFrame(timer.value);
        timer.value = null;
      }
    }
    function resume() {
      if (status.value !== TickTockStatusEnum.PAUSED)
        return;
      status.value = TickTockStatusEnum.RUNNING;
      startTime.value = Date.now() - elapsedMs.value;
      tick();
    }
    function reset() {
      status.value = TickTockStatusEnum.IDLE;
      elapsedMs.value = 0;
      startTime.value = null;
      if (timer.value) {
        cancelAnimationFrame(timer.value);
        timer.value = null;
      }
    }
    const hour = computed(() => Math.floor(elapsedMs.value / 1e3 / 60 / 60));
    const minute = computed(() => Math.floor(elapsedMs.value / 1e3 / 60) % 60);
    const second = computed(() => Math.floor(elapsedMs.value / 1e3) % 60);
    const hourTens = computed(() => Math.floor(hour.value / 10));
    const hourOnes = computed(() => hour.value % 10);
    const minuteTens = computed(() => Math.floor(minute.value / 10));
    const minuteOnes = computed(() => minute.value % 10);
    const secondTens = computed(() => Math.floor(second.value / 10));
    const secondOnes = computed(() => second.value % 10);
    const showHour = computed(() => hour.value > 0);
    const showHourTens = computed(() => hour.value >= 10);
    onBeforeUnmount(() => {
      if (timer.value)
        cancelAnimationFrame(timer.value);
    });
    return (_ctx, _cache) => {
      const _directive_stop_drag = resolveDirective("stop-drag");
      return openBlock(), createElementBlock(Fragment, null, [
        props.isFullScreen ? (openBlock(), createElementBlock("div", _hoisted_1$2, "计时器")) : createCommentVNode("", true),
        createBaseVNode("div", _hoisted_2$2, [
          showHour.value ? (openBlock(), createElementBlock("div", _hoisted_3$2, [
            showHourTens.value ? (openBlock(), createElementBlock("div", _hoisted_4$1, toDisplayString(hourTens.value), 1)) : createCommentVNode("", true),
            createBaseVNode("div", _hoisted_5$1, toDisplayString(hourOnes.value), 1)
          ])) : createCommentVNode("", true),
          showHour.value ? (openBlock(), createElementBlock("div", _hoisted_6$1, _cache[0] || (_cache[0] = [
            createBaseVNode("span", { class: "dot" }, null, -1),
            createBaseVNode("span", { class: "dot" }, null, -1)
          ]))) : createCommentVNode("", true),
          createBaseVNode("div", _hoisted_7$1, [
            createBaseVNode("div", _hoisted_8$1, toDisplayString(minuteTens.value), 1),
            createBaseVNode("div", _hoisted_9$1, toDisplayString(minuteOnes.value), 1)
          ]),
          _cache[1] || (_cache[1] = createBaseVNode("div", { class: "count-dot-colon" }, [
            createBaseVNode("span", { class: "dot" }),
            createBaseVNode("span", { class: "dot" })
          ], -1)),
          createBaseVNode("div", _hoisted_10$1, [
            createBaseVNode("div", _hoisted_11$1, toDisplayString(secondTens.value), 1),
            createBaseVNode("div", _hoisted_12$1, toDisplayString(secondOnes.value), 1)
          ])
        ]),
        createBaseVNode("div", _hoisted_13$1, [
          status.value === unref(TickTockStatusEnum).IDLE ? withDirectives((openBlock(), createElementBlock("img", {
            key: 0,
            class: "timer-button active-scale hl-sls-track",
            src: _imports_0,
            alt: "start",
            "data-text": "开始计时",
            "data-module": "星讲台-计时器",
            onClick: start
          }, null, 512)), [
            [_directive_stop_drag]
          ]) : createCommentVNode("", true),
          status.value === unref(TickTockStatusEnum).RUNNING ? withDirectives((openBlock(), createElementBlock("img", {
            key: 1,
            class: "timer-button active-scale",
            src: _imports_1,
            alt: "pause",
            onClick: pause
          }, null, 512)), [
            [_directive_stop_drag]
          ]) : createCommentVNode("", true),
          status.value === unref(TickTockStatusEnum).PAUSED ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
            withDirectives(createBaseVNode("img", {
              class: "timer-button active-scale",
              src: _imports_2,
              alt: "restart",
              onClick: reset
            }, null, 512), [
              [_directive_stop_drag]
            ]),
            withDirectives(createBaseVNode("img", {
              class: "timer-button active-scale",
              src: _imports_0,
              alt: "resume",
              onClick: resume
            }, null, 512), [
              [_directive_stop_drag]
            ])
          ], 64)) : createCommentVNode("", true)
        ])
      ], 64);
    };
  }
});
const Timer_vue_vue_type_style_index_0_scoped_4bc8681e_lang = "";
const Timer = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-4bc8681e"]]);
const alarm = "" + new URL("../mp3/countdown.2b98fc28.mp3", import.meta.url).href;
const _hoisted_1$1 = {
  key: 0,
  class: "count-title"
};
const _hoisted_2$1 = {
  key: 1,
  class: "count-setting-container"
};
const _hoisted_3$1 = { class: "count-common-setting flex flex-ac" };
const _hoisted_4 = { class: "count-setting-body flex flex-ac" };
const _hoisted_5 = { class: "count-setting-item" };
const _hoisted_6 = { class: "count-number" };
const _hoisted_7 = { class: "count-setting-item" };
const _hoisted_8 = { class: "count-number" };
const _hoisted_9 = { class: "count-setting-item" };
const _hoisted_10 = { class: "count-number" };
const _hoisted_11 = { class: "font-number" };
const _hoisted_12 = { class: "font-number" };
const _hoisted_13 = { class: "font-number" };
const _hoisted_14 = { class: "timer-controller flex flex-ac flex-jc" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "Countdown",
  props: {
    isFullScreen: {
      type: Boolean,
      default: false
    }
  },
  emits: ["status-change", "open-fullscreen", "close-fullscreen"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const status = ref(TickTockStatusEnum.IDLE);
    watch(status, (newStatus) => {
      emit("status-change", newStatus);
    });
    const hour = ref(0);
    const minute = ref(0);
    const second = ref(0);
    const initialHour = ref(0);
    const initialMinute = ref(0);
    const initialSecond = ref(0);
    const timer = ref(null);
    const startTimestamp = ref(null);
    const remainingMs = ref(0);
    let isAutoFullScreen = true;
    function setCommonTime(minutes) {
      hour.value = 0;
      minute.value = minutes;
      second.value = 0;
    }
    function addHour() {
      if (hour.value < 23) {
        hour.value++;
      } else {
        hour.value = 0;
      }
    }
    function subHour() {
      if (hour.value > 0) {
        hour.value--;
      } else {
        hour.value = 23;
      }
    }
    function addMinute() {
      if (minute.value < 59) {
        minute.value++;
      } else {
        minute.value = 0;
      }
    }
    function subMinute() {
      if (minute.value > 0) {
        minute.value--;
      } else {
        minute.value = 59;
      }
    }
    function addSecond() {
      if (second.value < 59) {
        second.value++;
      } else {
        second.value = 0;
      }
    }
    function subSecond() {
      if (second.value > 0) {
        second.value--;
      } else {
        second.value = 59;
      }
    }
    function pad(n) {
      return n.toString().padStart(2, "0");
    }
    function getTotalMs() {
      return (hour.value * 3600 + minute.value * 60 + second.value) * 1e3;
    }
    const alarmAudio = new Audio(alarm);
    alarmAudio.loop = false;
    const preEndAudioPlayed = ref(false);
    const alarmAudioProgress = ref(0);
    function tick() {
      if (!startTimestamp.value)
        return;
      const now = Date.now();
      const elapsed = now - startTimestamp.value;
      const left = remainingMs.value - elapsed;
      if (elapsed >= 3e3 && isAutoFullScreen) {
        emit("open-fullscreen");
        isAutoFullScreen = false;
      }
      if (left <= 2e3 && left > 0 && !preEndAudioPlayed.value) {
        preEndAudioPlayed.value = true;
        alarmAudio.currentTime = alarmAudioProgress.value || 0;
        alarmAudio.play().catch((err) => {
          logger.warn("【Countdown】", "预结束音效播放失败:", err);
        });
      }
      if (left <= 0) {
        updateTimeDisplay(0);
        end();
        return;
      }
      updateTimeDisplay(left);
      timer.value = setTimeout(tick, 50);
    }
    function updateTimeDisplay(ms) {
      const totalSeconds = Math.ceil(ms / 1e3);
      hour.value = Math.floor(totalSeconds / 3600);
      minute.value = Math.floor(totalSeconds % 3600 / 60);
      second.value = totalSeconds % 60;
    }
    function start() {
      if (status.value !== TickTockStatusEnum.IDLE)
        return;
      const total = getTotalMs();
      if (total <= 0) {
        showError("倒计时时间不能为 0");
        return;
      }
      status.value = TickTockStatusEnum.RUNNING;
      isAutoFullScreen = true;
      initialHour.value = hour.value;
      initialMinute.value = minute.value;
      initialSecond.value = second.value;
      remainingMs.value = total;
      startTimestamp.value = Date.now();
      preEndAudioPlayed.value = false;
      tick();
    }
    function pause() {
      if (status.value !== TickTockStatusEnum.RUNNING)
        return;
      status.value = TickTockStatusEnum.PAUSED;
      if (timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
      if (startTimestamp.value) {
        const now = Date.now();
        remainingMs.value -= now - startTimestamp.value;
      }
      alarmAudioProgress.value = alarmAudio.currentTime;
      alarmAudio.pause();
    }
    function resume() {
      if (status.value !== TickTockStatusEnum.PAUSED)
        return;
      status.value = TickTockStatusEnum.RUNNING;
      startTimestamp.value = Date.now();
      if (preEndAudioPlayed.value && alarmAudioProgress.value < alarmAudio.duration) {
        alarmAudio.currentTime = alarmAudioProgress.value;
        alarmAudio.play().catch(logger.warn);
      }
      tick();
    }
    function reset() {
      emit("close-fullscreen");
      status.value = TickTockStatusEnum.IDLE;
      if (timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
      startTimestamp.value = null;
      remainingMs.value = 0;
      hour.value = initialHour.value;
      minute.value = initialMinute.value;
      second.value = initialSecond.value;
      alarmAudio.pause();
      alarmAudio.currentTime = 0;
      alarmAudioProgress.value = 0;
      preEndAudioPlayed.value = false;
    }
    function end() {
      status.value = TickTockStatusEnum.END;
      if (timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
      startTimestamp.value = null;
      remainingMs.value = 0;
    }
    onBeforeUnmount(() => {
      if (timer.value)
        clearTimeout(timer.value);
    });
    return (_ctx, _cache) => {
      const _directive_stop_drag = resolveDirective("stop-drag");
      return openBlock(), createElementBlock(Fragment, null, [
        props.isFullScreen ? (openBlock(), createElementBlock("div", _hoisted_1$1, "倒计时")) : createCommentVNode("", true),
        status.value === unref(TickTockStatusEnum).IDLE ? withDirectives((openBlock(), createElementBlock("div", _hoisted_2$1, [
          createBaseVNode("div", _hoisted_3$1, [
            createBaseVNode("div", {
              class: "shortcut-key",
              onClick: _cache[0] || (_cache[0] = ($event) => setCommonTime(1))
            }, "1分钟"),
            createBaseVNode("div", {
              class: "shortcut-key",
              onClick: _cache[1] || (_cache[1] = ($event) => setCommonTime(3))
            }, "3分钟"),
            createBaseVNode("div", {
              class: "shortcut-key",
              onClick: _cache[2] || (_cache[2] = ($event) => setCommonTime(5))
            }, "5分钟")
          ]),
          createBaseVNode("div", _hoisted_4, [
            createBaseVNode("div", _hoisted_5, [
              createBaseVNode("div", {
                class: "operator-button",
                onClick: subHour
              }, [
                createVNode(SvgIcon, { "icon-class": "minus" })
              ]),
              createBaseVNode("div", _hoisted_6, toDisplayString(hour.value), 1),
              createBaseVNode("div", {
                class: "operator-button",
                onClick: addHour
              }, [
                createVNode(SvgIcon, { "icon-class": "plus" })
              ])
            ]),
            _cache[3] || (_cache[3] = createBaseVNode("div", { class: "count-unit" }, "小时", -1)),
            createBaseVNode("div", _hoisted_7, [
              createBaseVNode("div", {
                class: "operator-button",
                onClick: subMinute
              }, [
                createVNode(SvgIcon, { "icon-class": "minus" })
              ]),
              createBaseVNode("div", _hoisted_8, toDisplayString(minute.value), 1),
              createBaseVNode("div", {
                class: "operator-button",
                onClick: addMinute
              }, [
                createVNode(SvgIcon, { "icon-class": "plus" })
              ])
            ]),
            _cache[4] || (_cache[4] = createBaseVNode("div", { class: "count-unit" }, "分钟", -1)),
            createBaseVNode("div", _hoisted_9, [
              createBaseVNode("div", {
                class: "operator-button",
                onClick: subSecond
              }, [
                createVNode(SvgIcon, { "icon-class": "minus" })
              ]),
              createBaseVNode("div", _hoisted_10, toDisplayString(second.value), 1),
              createBaseVNode("div", {
                class: "operator-button",
                onClick: addSecond
              }, [
                createVNode(SvgIcon, { "icon-class": "plus" })
              ])
            ]),
            _cache[5] || (_cache[5] = createBaseVNode("div", { class: "count-unit" }, "秒", -1))
          ])
        ])), [
          [_directive_stop_drag]
        ]) : (openBlock(), createElementBlock("div", {
          key: 2,
          class: normalizeClass(["count-running-container flex flex-ac flex-jc", { "show-hour": hour.value > 0 }])
        }, [
          hour.value > 0 ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
            createBaseVNode("div", _hoisted_11, toDisplayString(hour.value), 1),
            _cache[6] || (_cache[6] = createBaseVNode("div", { class: "count-dot-colon" }, [
              createBaseVNode("span", { class: "dot" }),
              createBaseVNode("span", { class: "dot" })
            ], -1))
          ], 64)) : createCommentVNode("", true),
          createBaseVNode("div", _hoisted_12, toDisplayString(pad(minute.value)), 1),
          _cache[7] || (_cache[7] = createBaseVNode("div", { class: "count-dot-colon" }, [
            createBaseVNode("span", { class: "dot" }),
            createBaseVNode("span", { class: "dot" })
          ], -1)),
          createBaseVNode("div", _hoisted_13, toDisplayString(pad(second.value)), 1)
        ], 2)),
        createBaseVNode("div", _hoisted_14, [
          status.value === unref(TickTockStatusEnum).IDLE ? withDirectives((openBlock(), createElementBlock("img", {
            key: 0,
            class: "timer-button active-scale hl-sls-track",
            src: _imports_0,
            alt: "start",
            "data-text": "开始计时",
            "data-module": "星讲台-倒计时",
            onClick: start
          }, null, 512)), [
            [_directive_stop_drag]
          ]) : createCommentVNode("", true),
          status.value === unref(TickTockStatusEnum).RUNNING ? withDirectives((openBlock(), createElementBlock("img", {
            key: 1,
            class: "timer-button active-scale",
            src: _imports_1,
            alt: "pause",
            onClick: pause
          }, null, 512)), [
            [_directive_stop_drag]
          ]) : createCommentVNode("", true),
          status.value === unref(TickTockStatusEnum).PAUSED || status.value === unref(TickTockStatusEnum).END ? (openBlock(), createElementBlock(Fragment, { key: 2 }, [
            withDirectives(createBaseVNode("img", {
              class: "timer-button active-scale",
              src: _imports_2,
              alt: "restart",
              onClick: reset
            }, null, 512), [
              [_directive_stop_drag]
            ]),
            status.value !== unref(TickTockStatusEnum).END ? withDirectives((openBlock(), createElementBlock("img", {
              key: 0,
              class: "timer-button active-scale",
              src: _imports_0,
              alt: "resume",
              onClick: resume
            }, null, 512)), [
              [_directive_stop_drag]
            ]) : createCommentVNode("", true)
          ], 64)) : createCommentVNode("", true)
        ])
      ], 64);
    };
  }
});
const Countdown_vue_vue_type_style_index_0_scoped_d62c18e5_lang = "";
const Countdown = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-d62c18e5"]]);
const _hoisted_1 = { class: "tick-tock-body flex flex-v flex-ac" };
const _hoisted_2 = {
  key: 0,
  class: "tick-tock-tabs flex flex-ac flex-jc"
};
const _hoisted_3 = ["onClick"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  emits: ["close", "open-fullscreen", "close-fullscreen"],
  setup(__props, { emit: __emit }) {
    const isFullScreen = ref(false);
    const activeIndex = ref(0);
    const underlineLeft = ref(0);
    const tabRefs = ref([]);
    const currentStatus = ref(TickTockStatusEnum.IDLE);
    const isShowFullScreen = computed(() => {
      return activeIndex.value !== 1 || currentStatus.value !== TickTockStatusEnum.IDLE;
    });
    watch(
      isShowFullScreen,
      (value) => {
        try {
          const bridge = Bridge.getInstance();
          if (value) {
            bridge.callVoid("showFullscreenButton");
          } else {
            bridge.callVoid("hideFullscreenButton");
          }
        } catch (e) {
          logger.warn("【TickTock】", "切换显示全屏状态失败", value, e);
        }
      },
      {
        immediate: true
      }
    );
    const handleCurrentChange = (index) => {
      if (currentStatus.value === TickTockStatusEnum.RUNNING)
        return;
      currentStatus.value = TickTockStatusEnum.IDLE;
      activeIndex.value = index;
      nextTick(() => {
        updateUnderline();
      });
    };
    function updateUnderline() {
      const tab = tabRefs.value[activeIndex.value];
      if (tab) {
        const rect = tab.getBoundingClientRect();
        const parentRect = tab.parentElement?.getBoundingClientRect();
        if (parentRect) {
          underlineLeft.value = rect.left - parentRect.left + rect.width / 2;
        }
      }
    }
    const handleStatusChange = (status) => {
      if (status) {
        currentStatus.value = status;
      }
    };
    const deferShow = ref(true);
    let hideTimeoutIndex = null;
    const defer = () => {
      if (hideTimeoutIndex) {
        return;
      }
      deferShow.value = false;
      hideTimeoutIndex = setTimeout(() => {
        deferShow.value = true;
        hideTimeoutIndex = null;
      }, 300);
    };
    const openFullScreen = async () => {
      if (isFullScreen.value)
        return;
      try {
        await Bridge.getInstance().callVoid("enterFullScreen");
      } catch (e) {
        logger.error("【计时器】", "关闭全屏失败", e);
      }
      isFullScreen.value = true;
      defer();
    };
    const closeFullScreen = async () => {
      logger.info("【计时器】", "关闭全屏");
      try {
        await Bridge.getInstance().callVoid("exitFullScreen");
      } catch (e) {
        logger.error("【计时器】", "关闭全屏失败", e);
      }
      defer();
      isFullScreen.value = false;
    };
    const onEnterFullScreen = () => {
      defer();
      isFullScreen.value = true;
    };
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
      event.target && event.target.dispatchEvent(new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
        clientX: event.clientX,
        clientY: event.clientY
      }));
    });
    onMounted(() => {
      updateUnderline();
      const bridge = Bridge.getInstance();
      bridge.on("enterFullScreen", onEnterFullScreen);
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
      }).catch((e) => {
        logger.error("【计时器】", "获取当前上课信息失败", e);
      });
    });
    onUnmounted(() => {
      const bridge = Bridge.getInstance();
      bridge.off("enterFullScreen", onEnterFullScreen);
    });
    return (_ctx, _cache) => {
      const _directive_stop_drag = resolveDirective("stop-drag");
      return withDirectives((openBlock(), createElementBlock("div", {
        class: normalizeClass(["tick-tock-container", { fullscreen: isFullScreen.value }])
      }, [
        createBaseVNode("div", _hoisted_1, [
          !isFullScreen.value ? (openBlock(), createElementBlock("div", _hoisted_2, [
            (openBlock(), createElementBlock(Fragment, null, renderList(["计时器", "倒计时"], (tab, index) => {
              return withDirectives(createBaseVNode("div", {
                ref_for: true,
                ref_key: "tabRefs",
                ref: tabRefs,
                key: index,
                class: normalizeClass(["tick-tock-tabs_item", { active: activeIndex.value === index }]),
                onClick: ($event) => handleCurrentChange(index)
              }, [
                createTextVNode(toDisplayString(tab), 1)
              ], 10, _hoisted_3), [
                [_directive_stop_drag]
              ]);
            }), 64)),
            createVNode(SvgIcon, {
              class: "tick-tock-tabs_under",
              "icon-class": "underscore",
              style: normalizeStyle({
                transform: `translateX(${underlineLeft.value}px) translateX(-50%)`
              })
            }, null, 8, ["style"])
          ])) : createCommentVNode("", true),
          activeIndex.value === 0 ? (openBlock(), createBlock(Timer, {
            key: 1,
            "is-full-screen": isFullScreen.value,
            onStatusChange: handleStatusChange,
            onOpenFullscreen: openFullScreen
          }, null, 8, ["is-full-screen"])) : createCommentVNode("", true),
          activeIndex.value === 1 ? (openBlock(), createBlock(Countdown, {
            key: 2,
            "is-full-screen": isFullScreen.value,
            onStatusChange: handleStatusChange,
            onOpenFullscreen: openFullScreen,
            onCloseFullscreen: closeFullScreen
          }, null, 8, ["is-full-screen"])) : createCommentVNode("", true)
        ]),
        isFullScreen.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: "fullscreen-exit",
          onClick: closeFullScreen
        }, "退出")) : createCommentVNode("", true)
      ], 2)), [
        [vShow, deferShow.value]
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_6c36e769_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-6c36e769"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.directive("stop-drag", stopDrag);
app.use(pinia).mount("#app");
