const stopDrag = {
  mounted(el) {
    const handler = (e) => {
      e.stopPropagation();
    };
    el.__stopEventHandler__ = handler;
    el.addEventListener("mousedown", handler);
    el.addEventListener("touchstart", handler);
    el.addEventListener("pointerdown", handler);
  },
  beforeUnmount(el) {
    const handler = el.__stopEventHandler__;
    if (handler) {
      el.removeEventListener("mousedown", handler);
      el.removeEventListener("touchstart", handler);
      el.removeEventListener("pointerdown", handler);
    }
    delete el.__stopEventHandler__;
  }
};
export {
  stopDrag as s
};
