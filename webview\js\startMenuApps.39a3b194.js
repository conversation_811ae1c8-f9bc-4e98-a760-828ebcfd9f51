import { l as logger } from "./index.2e7c5603.js";
import { g as getStartMenuApps } from "./addon.60a72a7d.js";
import { r as ref } from "./bootstrap.ab073eb8.js";
const apps = ref([]);
function useStartMenuApps(flush = false) {
  const refresh = async () => {
    try {
      const res = await getStartMenuApps();
      if (Array.isArray(res)) {
        const list = [];
        res.forEach((item) => {
          const { id, name, data, order } = item || {};
          if (!data.icon || !data.target) {
            return;
          }
          const appItem = {
            app: {
              id,
              name,
              order,
              data
            }
          };
          list.push(appItem);
        });
        apps.value = list;
      }
    } catch (e) {
      logger.warn("【addon】获取开始菜单应用失败: ", e);
    }
  };
  if (flush || apps.value.length === 0) {
    refresh();
  }
  return {
    apps,
    refresh
  };
}
export {
  useStartMenuApps as u
};
