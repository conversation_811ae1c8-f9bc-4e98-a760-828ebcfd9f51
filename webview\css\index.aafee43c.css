.source-resource-item[data-v-7b65b8c6] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}
.source-resource-item .el-image[data-v-7b65b8c6] {
  width: 100%;
  height: 100%;
}
.source-resource-item .source-audio-wrap[data-v-7b65b8c6] {
  width: 100%;
  height: 100%;
  background: #f1f3f4 url('../webp/<EMAIL>') no-repeat top center;
  background-size: contain;
  position: relative;
  border-radius: 8.33333rem 8.33333rem 0 0;
  overflow: hidden;
}
.source-resource-item .source-audio[data-v-7b65b8c6] {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #f1f3f4;
}
.source-resource-item .source-video[data-v-7b65b8c6] {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.source-resource-item .custom-dialog-close-btn[data-v-7b65b8c6] {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto -4.66667rem;
}
.source-resource-item.vdr[data-v-7b65b8c6] {
  border: none;
}
