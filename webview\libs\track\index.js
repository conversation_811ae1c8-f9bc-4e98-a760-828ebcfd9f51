!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).WebTrack=t()}(this,(function(){"use strict";var extendStatics=function(e,t){return extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},extendStatics(e,t)};function __extends$b(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}extendStatics(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var __assign$4=function(){return __assign$4=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign$4.apply(this,arguments)};function __awaiter$3(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))}function __generator$3(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}}var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t,e,i=function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};!function(e){e[e.burialPoint=1]="burialPoint",e[e.monitor=2]="monitor",e[e.log=3]="log",e[e.full_buried_point=4]="full_buried_point"}(t||(t={})),function(e){e.info="INFO",e.warn="WARN",e.error="ERROR",e.debug="DEBUG"}(e||(e={}));var o=function(){function r(e){this.globalData={},this.filterRule=[],this.env="production",this.uuid="",this.title="",this.productId="",this.staffId="",this.terminal=e.terminal||"web",this.env=e.env||"production",this.globalData=e.globalData||{},this.filterRule=e.filterRule||[],this.uuid=e.uuid,this.title=e.title||"",this.productId=e.productId||"",this.terminalInfo=e.terminalInfo||{},this.userId=e.userId,this.track=e.track}return r.prototype.filter=function(e){if("function"==typeof this.filterRule)return this.filterRule(e);for(var t=!1,r=this.filterRule.length;r>=0;r--)if(this.contain(this.filterRule[r],e)){t=!0;break}return t},r.prototype.contain=function(e,t){var r=!0;switch(this.getVariableType(e)){case"Array":for(var n=0,o=e.length;n<o;n++)if(!this.contain(e[n],t[n])){r=!1;break}break;case"Object":for(var i in e)if(!this.contain(e[i],t[i])){r=!1;break}break;case"RegExp":r=e.test(t);break;default:r=e===t}return r},r.prototype.getVariableType=function(e){var t=Object.prototype.toString.call(e);return t.substr(8,t.length-9)},r.prototype.setStaffId=function(e){this.staffId=e},r.prototype.browserVersions=function(){if(!navigator||!navigator.userAgent)return{};var e,t=navigator.userAgent,r=t.toLowerCase(),n=!!window.ActiveXObject||"ActiveXObject"in window,o=!!t.match(/AppleWebKit.*Mobile.*/),a=!!t.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),s={browserEquipment:o?a?"ios":"android":"pc"};if(n)return i({browserName:"IE"},s);if(t.indexOf("Opera")>-1){var u=(e=r.match(/opera.([d.]+)/))?e[1]:"";return i({browserName:"Opera",browserVersion:u},s)}return t.indexOf("Firefox")>-1?(u=(e=r.match(/firefox\/([\d.]+)/))?e[1]:"",i({browserName:"Firefox",browserVersion:u},s)):t.indexOf("Chrome")>-1?(u=(e=r.match(/chrome\/([\d.]+)/))?e[1]:"",i({browserName:"Chrome",browserVersion:u},s)):t.indexOf("Safari")>-1?(u=(e=r.match(/version\/([\d.]+).*safari/))?e[1]:"",i({browserName:"Safari",browserVersion:u},s)):i({browserName:""},s)},r.prototype.formatSendData=function(e){var r,n="string"==typeof e?{topic:e}:i({},e),o=(null===document||void 0===document?void 0:document.title)||this.title||"",a=i(i(i({},this.browserVersions()),{ua:(null===navigator||void 0===navigator?void 0:navigator.userAgent)||"",source:(null===document||void 0===document?void 0:document.referrer)||"",type:(null===(r=null===window||void 0===window?void 0:window.performance)||void 0===r?void 0:r.navigation.type)||""}),this.terminalInfo),s={_terminal_:this.terminal,_type_:t.burialPoint,_title_:o,_href_:(null===location||void 0===location?void 0:location.href)||"",_height_:(null===window||void 0===window?void 0:window.innerHeight)||"",_width_:(null===window||void 0===window?void 0:window.innerWidth)||"",_terminal_info_:a,_ua_:(null===navigator||void 0===navigator?void 0:navigator.userAgent)||""};s._business_info_=this.businessInfo||"",s._userId_=this.userId||"",s._uuid_=this.uuid,s._product_id_=this.productId||"",s._time_=Date.now(),s._staffId_=this.staffId,n.event&&(s._event_code_=n.event.code||"",s._event_info_=n.event.info||"",delete n.event);var u=i(i(i({_level_:n._level_||"INFO"},s),this.globalData),n);for(var c in u)"Function"===this.getVariableType(u[c])&&(u[c]=u[c]());return u},r.prototype.trackSend=function(e){if(this.track){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):this.track.send(t)}},r.prototype.trackSendImmediate=function(e){if(this.track){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):this.track.sendImmediate(t)}},r.prototype.trackSendBatchLogs=function(e){if(this.track){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):this.track.sendBatchLogs(t)}},r.prototype.trackSendBatchLogsImmediate=function(e){if(this.track){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):this.track.sendBatchLogsImmediate(t)}},r.prototype.sendTrackEvent=function(e){var t=e.code,r=e.eventInfo,n=e.businessInfo,o=this.formatSendData({});t&&(o._event_code_=t),r&&(o._event_info_=JSON.stringify(r)),n&&(o._business_info_=JSON.stringify(n)),this.filter(o)?console.log("已过滤",o):this.track.sendImmediate(o)},r.prototype.sendCustomerEvent=function(r){var n=r.code,o=r.info,i=r.level,a=void 0===i?e.warn:i,s=r.type,u=void 0===s?t.monitor:s,c=this.formatSendData({});n&&(c._event_code_=n),o&&(c._event_info_=JSON.stringify(o)),c._level_=a,c._type_=u,this.filter(c)?console.log("已过滤",c):this.track.sendImmediate(c)},r.prototype.sendTrackLog=function(r,n){void 0===n&&(n=e.debug);var o=this.formatSendData({});o._event_code_="LOG_PRINT",r&&(o._event_info_=JSON.stringify(r)),o._level_=n,o._type_=t.log,this.filter(o)?console.log("已过滤",o):n===e.error?this.track.sendImmediate(o):this.track.send(o)},r.prototype.setTerminalInfo=function(e){e&&(this.terminalInfo=e)},r.prototype.add=function(e){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):t._level_&&["ERROR"].includes(t._level_)?this.track.sendImmediate(t):this.track.send(t)},r.prototype.addImmediate=function(e){var t=this.formatSendData(e);this.filter(t)?console.log("已过滤",t):this.track.sendImmediate(t)},r.prototype.setBusinessInfo=function(e){this.businessInfo=e},r.prototype.setUserId=function(e){this.userId=e},r}(),dist=o,Track=getDefaultExportFromCjs(dist),webBase_cjs={},webTypes_cjs={};const SLS_CLIENT_NAME="SLS_CLIENT";var WebEventType=(e=>(e.ERROR="error",e.LOG="log",e.LOCATION="pv",e.API="api",e.RESOURCE="res",e.RESOURCE_ERROR="res_err",e.PERF="perf",e.CONSOLE_LOG="console",e.DOM_CLICK="dom_click",e))(WebEventType||{}),InternalPlugin=(e=>(e.LOG_SEND="LOG_SEND",e.BROWSER_SEND="BROWSER_SEND",e.BASE_TRANSFORM="BASE_TRANSFORM",e.BROWSER_BASE_TRANSFORM="BROWSER_BASE_TRANSFORM",e.BROWSER_FETCH="BROWSER_FETCH",e.BROWSER_XHR="BROWSER_XHR",e.BROWSER_DOM="BROWSER_DOM",e.BROWSER_LOCATION="BROWSER_LOCATION",e.BROWSER_RUNTIME_ERROR="BROWSER_RUNTIME_ERROR",e.BROWSER_CUSTOM_ERROR="BROWSER_CUSTOM_ERROR",e.BROWSER_RESOURCE_ERROR="BROWSER_RESOURCE_ERROR",e.BROWSER_CONSOLE="BROWSER_CONSOLE",e.BROWSER_PERF="BROWSER_PERF",e.BROWSER_RESOURSE="BROWSER_RESOURSE",e.MINI_SEND="MINI_SEND",e.MINI_REQUEST="MINI_REQUEST",e.MINI_BASE_TRANSFORM="MINI_BASE_TRANSFORM",e.MINI_ROUTE="MINI_ROUTE",e))(InternalPlugin||{});const InternalPluginPriority={BROWSER_SEND:-1,MINI_SEND:-2,BASE_TRANSFORM:100,BROWSER_BASE_TRANSFORM:200,MINI_BASE_TRANSFORM:201,BROWSER_PERF:301,BROWSER_FETCH:400,BROWSER_XHR:401,MINI_REQUEST:402,BROWSER_RUNTIME_ERROR:500},SLS_TRACE_UID_KEY="sls-trace-uid";var OTStatusCode=(e=>(e.OK="OK",e.ERROR="ERROR",e.UNSET="UNSET",e))(OTStatusCode||{});webTypes_cjs.InternalPlugin=InternalPlugin,webTypes_cjs.InternalPluginPriority=InternalPluginPriority,webTypes_cjs.OTStatusCode=OTStatusCode,webTypes_cjs.SLS_CLIENT_NAME=SLS_CLIENT_NAME,webTypes_cjs.SLS_TRACE_UID_KEY=SLS_TRACE_UID_KEY,webTypes_cjs.WebEventType=WebEventType;var webTypes=webTypes_cjs;function defineGlobalValue(e,t,r){Object.defineProperty(e,t,{writable:!0,enumerable:!0,configurable:!0,value:r})}function defineGlobal(e,t,r,n){n?n((()=>{defineGlobalValue(e,t,r)})):defineGlobalValue(e,t,r)}class Observable{constructor(e){this.onFirstSubscribe=e,this.observers=[]}subscribe(e){return!this.observers.length&&this.onFirstSubscribe&&(this.onLastUnsubscribe=this.onFirstSubscribe()||void 0),this.observers.push(e),{unsubscribe:()=>{this.observers=this.observers.filter((t=>e!==t)),!this.observers.length&&this.onLastUnsubscribe&&this.onLastUnsubscribe()}}}notify(e){this.observers.forEach((t=>t(e)))}}function callMonitored(e,t,r){try{return e.apply(t,r)}catch(e){return}}function monitor(e){return function(){return callMonitored(e,this,arguments)}}let isConsoleLoging=!1;function getConsoleLogStatus(){return isConsoleLoging}function debugLog(...e){}var __defProp$3=Object.defineProperty,__defProps$2=Object.defineProperties,__getOwnPropDescs$2=Object.getOwnPropertyDescriptors,__getOwnPropSymbols$3=Object.getOwnPropertySymbols,__hasOwnProp$3=Object.prototype.hasOwnProperty,__propIsEnum$3=Object.prototype.propertyIsEnumerable,__defNormalProp$3=(e,t,r)=>t in e?__defProp$3(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues$3=(e,t)=>{for(var r in t||(t={}))__hasOwnProp$3.call(t,r)&&__defNormalProp$3(e,r,t[r]);if(__getOwnPropSymbols$3)for(var r of __getOwnPropSymbols$3(t))__propIsEnum$3.call(t,r)&&__defNormalProp$3(e,r,t[r]);return e},__spreadProps$2=(e,t)=>__defProps$2(e,__getOwnPropDescs$2(t));const ONE_SECOND=1e3,ONE_MINUTE=60*ONE_SECOND,ONE_HOUR=60*ONE_MINUTE,ONE_DAY=24*ONE_HOUR,ONE_YEAR=365*ONE_DAY,ONE_KILO_BYTE=1024,SDK_VERSION="0.2.0";function noop$1(){}function isNumber(e){return"number"==typeof e}function isString(e){return"[object String]"===Object.prototype.toString.call(e)}function isError(e){return"[object Error]"===Object.prototype.toString.call(e)}function round(e,t){return+e.toFixed(t)}function includes(e,t){return-1!==e.indexOf(t)}function matchWithDefault(e,t){return null==e?t:!!e}function generateUUID(e){return e?(parseInt(e,10)^16*Math.random()>>parseInt(e,10)/4).toString(16):"10000000-1000-4000-8000-100000000000".replace(/[018]/g,generateUUID)}function genUid(e=20){var t,r;const n=new Array(e),o=Date.now().toString(36).split("");for(;e-- >0;)r=(t=36*Math.random()|0).toString(36),n[e]=t%3?r:r.toUpperCase();for(var i=0;i<8;i++)n.splice(3*i+2,0,o[i]);return n.join("")}function matchSample(e){return 0!==e&&Math.random()<=e}function catchUserErrors(e,t){return(...r)=>{try{return e(...r)}catch(e){console.error(t,e)}}}function getTrackUrl(e){return"https://"+e.project+"."+e.host+"/logstores/"+e.logstore+"/track"}function isInternalUrl(e,t){return t?e.includes(getTrackUrl(t)):e.includes("/track?APIVersion=0.6.0")}const REGEX=/^(?:([^:\/?#]+):\/\/)?((?:([^\/?#@]*)@)?([^\/?#:]*)(?:\:(\d*))?)?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n)*))?/i;function decode(e){try{return decodeURIComponent(e)}catch(t){return unescape(e)}}function isURL(e){return"string"==typeof e&&REGEX.test(e)}function parseUri(e){var t=decode(e||"").match(REGEX);if(null==t)return null;var r=(t[3]||"").split(":"),n=r.length?(t[2]||"").replace(/(.*\@)/,""):t[2];return{uri:t[0],protocol:t[1],host:n,hostname:t[4],port:t[5],auth:t[3],user:r[0],password:r[1],path:t[6],search:t[7],hash:t[8]}}function buildHttpInfo(e,t,r,n){const o={url:e,method:t,status_code:r};return null==n?o:__spreadProps$2(__spreadValues$3({},o),{host:n.host,scheme:n.protocol})}function matchCondition(e,t,...r){return!(null==t||!Array.isArray(t))&&t.some((t=>{if("function"==typeof t)try{return t(e,...r)}catch(e){return console.error("user function callback threw an error:",e),!1}return"string"==typeof t?e.includes(t):"[object RegExp]"===Object.prototype.toString.call(t)&&t.test(e)}))}function shouldTrackBody(e,t,r){return!1!==e&&(!0===e||("error"===e&&!r||"function"==typeof e&&catchUserErrors(e,"call shouldTrackBody failed")(t)))}function throttle(e,t){let r=!0;return function(...n){r&&(e.apply(this,n),r=!1,setTimeout((()=>{r=!0}),t))}}function relativeToClocks(e){return{relative:e,timeStamp:getCorrectedTimeStamp(e)}}function getCorrectedTimeStamp(e){const t=Date.now()-performance.now();return t>getNavigationStart()?Math.round(t+e):getTimeStamp(e)}function currentDrift(){return Math.round(Date.now()-(getNavigationStart()+performance.now()))}function toServerDuration(e){return isNumber(e)?round(1e6*e,0):e}function timeStampNow(){return Date.now()}function relativeNow(){return performance.now()}function clocksNow(){return{relative:relativeNow(),timeStamp:timeStampNow()}}function clocksOrigin(){return{relative:0,timeStamp:getNavigationStart()}}function elapsed(e,t){return t-e}function getRelativeTime(e){return e-getNavigationStart()}function getTimeStamp(e){return Math.round(getNavigationStart()+e)}function looksLikeRelativeTime(e){return e<ONE_YEAR}let navigationStart;function getNavigationStart(){return void 0===navigationStart&&(navigationStart=performance.timing.navigationStart),navigationStart}function resetNavigationStart(){navigationStart=void 0}const UNKNOWN_FUNCTION="?";function computeStackTrace(e,t){let r;const n=void 0===t?0:+t;try{if(r=computeStackTraceFromStacktraceProp(e),r)return r}catch(e){}try{if(r=computeStackTraceFromStackProp(e),r)return r}catch(e){}try{if(r=computeStackTraceFromOperaMultiLineMessage(e),r)return r}catch(e){}try{if(r=computeStackTraceByWalkingCallerChain(e,n+1),r)return r}catch(e){}return{message:tryToGetString(e,"message"),name:tryToGetString(e,"name"),stack:[]}}function computeStackTraceFromStackProp(e){const t=tryToGetString(e,"stack");if(!t)return;const r=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,n=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,o=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;let i;const a=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,s=/\((\S*)(?::(\d+))(?::(\d+))\)/,u=t.split("\n"),c=[];let l,p,f;for(let t=0,d=u.length;t<d;t+=1){if(r.exec(u[t])){p=r.exec(u[t]);const e=p[2]&&0===p[2].indexOf("native");i=p[2]&&0===p[2].indexOf("eval"),l=s.exec(p[2]),i&&l&&(p[2]=l[1],p[3]=l[2],p[4]=l[3]),f={args:e?[p[2]]:[],column:p[4]?+p[4]:void 0,func:p[1]||UNKNOWN_FUNCTION,line:p[3]?+p[3]:void 0,url:e?void 0:p[2]}}else if(o.exec(u[t]))p=o.exec(u[t]),f={args:[],column:p[4]?+p[4]:void 0,func:p[1]||UNKNOWN_FUNCTION,line:+p[3],url:p[2]};else{if(!n.exec(u[t]))continue;p=n.exec(u[t]),i=p[3]&&p[3].indexOf(" > eval")>-1,l=a.exec(p[3]),i&&l?(p[3]=l[1],p[4]=l[2],p[5]=void 0):0!==t||p[5]||isUndefined(e.columnNumber)||(c[0].column=e.columnNumber+1),f={args:p[2]?p[2].split(","):[],column:p[5]?+p[5]:void 0,func:p[1]||UNKNOWN_FUNCTION,line:p[4]?+p[4]:void 0,url:p[3]}}!f.func&&f.line&&(f.func=UNKNOWN_FUNCTION),c.push(f)}return c.length?{stack:c,message:tryToGetString(e,"message"),name:tryToGetString(e,"name")}:void 0}function computeStackTraceFromStacktraceProp(e){const t=tryToGetString(e,"stacktrace");if(!t)return;const r=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,n=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\((.*)\))? in (.*):\s*$/i,o=t.split("\n"),i=[];let a;for(let e=0;e<o.length;e+=2){let t;r.exec(o[e])?(a=r.exec(o[e]),t={args:[],column:void 0,func:a[3],line:+a[1],url:a[2]}):n.exec(o[e])&&(a=n.exec(o[e]),t={args:a[5]?a[5].split(","):[],column:+a[2],func:a[3]||a[4],line:+a[1],url:a[6]}),t&&(!t.func&&t.line&&(t.func=UNKNOWN_FUNCTION),t.context=[o[e+1]],i.push(t))}return i.length?{stack:i,message:tryToGetString(e,"message"),name:tryToGetString(e,"name")}:void 0}function computeStackTraceFromOperaMultiLineMessage(e){const t=tryToGetString(e,"message");if(!t)return;const r=t.split("\n");if(r.length<4)return;const n=/^\s*Line (\d+) of linked script ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,o=/^\s*Line (\d+) of inline#(\d+) script in ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,i=/^\s*Line (\d+) of function script\s*$/i,a=[],s=window&&window.document&&window.document.getElementsByTagName("script"),u=[];let c;for(const e in s)has(s,e)&&!s[e].src&&u.push(s[e]);for(let e=2;e<r.length;e+=2){let t;if(n.exec(r[e]))c=n.exec(r[e]),t={args:[],column:void 0,func:c[3],line:+c[1],url:c[2]};else if(o.exec(r[e]))c=o.exec(r[e]),t={args:[],column:void 0,func:c[4],line:+c[1],url:c[3]};else if(i.exec(r[e])){c=i.exec(r[e]);t={url:window.location.href.replace(/#.*$/,""),args:[],column:void 0,func:"",line:+c[1]}}t&&(t.func||(t.func=UNKNOWN_FUNCTION),t.context=[r[e+1]],a.push(t))}return a.length?{stack:a,message:r[0],name:tryToGetString(e,"name")}:void 0}function augmentStackTraceWithInitialElement(e,t,r){const n={url:t,line:r?+r:void 0};if(n.url&&n.line){e.incomplete=!1;const t=e.stack;if(t.length>0&&t[0].url===n.url){if(t[0].line===n.line)return!1;if(!t[0].line&&t[0].func===n.func)return t[0].line=n.line,t[0].context=n.context,!1}return t.unshift(n),e.partial=!0,!0}return e.incomplete=!0,!1}function computeStackTraceByWalkingCallerChain(e,t){const r=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,n=[],o={};let i,a,s=!1;for(let e=computeStackTraceByWalkingCallerChain.caller;e&&!s;e=e.caller)e!==computeStackTrace&&(a={args:[],column:void 0,func:UNKNOWN_FUNCTION,line:void 0,url:void 0},i=r.exec(e.toString()),e.name?a.func=e.name:i&&(a.func=i[1]),void 0===a.func&&(a.func=i?i.input.substring(0,i.input.indexOf("{")):void 0),o[e.toString()]?s=!0:o[e.toString()]=!0,n.push(a));t&&n.splice(0,t);const u={stack:n,message:tryToGetString(e,"message"),name:tryToGetString(e,"name")};return augmentStackTraceWithInitialElement(u,tryToGetString(e,"sourceURL")||tryToGetString(e,"fileName"),tryToGetString(e,"line")||tryToGetString(e,"lineNumber")),u}function tryToGetString(e,t){if("object"!=typeof e||!e||!(t in e))return;const r=e[t];return"string"==typeof r?r:void 0}function has(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function isUndefined(e){return void 0===e}function instrumentMethod(e,t,r){const n=e[t];let o=r(n);const i=function(){return o.apply(this,arguments)};return e[t]=i,{stop:()=>{e[t]===i?e[t]=n:o=n}}}function instrumentMethodAndCallOriginal(e,t,{before:r,after:n}){return instrumentMethod(e,t,(e=>function(){const t=arguments;let o;return r&&callMonitored(r,this,t),"function"==typeof e&&(o=e.apply(this,t)),n&&callMonitored(n,this,t),o}))}const ERROR_TYPES_RE=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;function startUnhandledErrorCollection(e){const{stop:t}=instrumentOnError(e),{stop:r}=instrumentUnhandledRejection(e);return{stop:()=>{t(),r()}}}function instrumentOnError(e){return instrumentMethodAndCallOriginal(window,"onerror",{before(t,r,n,o,i){let a;if(i)a=computeStackTrace(i),e(a,i);else{const i={url:r,column:o,line:n};let s,u=t;if("[object String]"==={}.toString.call(t)){const e=ERROR_TYPES_RE.exec(u);e&&(s=e[1],u=e[2])}a={name:s,message:"string"==typeof u?u:void 0,stack:[i]},e(a,t)}}})}function instrumentUnhandledRejection(e){return instrumentMethodAndCallOriginal(window,"onunhandledrejection",{before(t){const r=t.reason||"Empty reason",n=computeStackTrace(r);e(n,r)}})}function formatErrorMessage(e){return`${e.name||"Error"}: ${e.message}`}function filterRumStack(e){return e.filter((e=>null==e.url||!e.url.includes("sls-rum.js")))}function toStackTraceString(e){let t=formatErrorMessage(e);return e.stack.forEach((e=>{const r="?"===e.func?"<anonymous>":e.func,n=e.args&&e.args.length>0?`(${e.args.join(", ")})`:"",o=e.line?`:${e.line}`:"",i=e.line&&e.column?`:${e.column}`:"";t+=`\n  at ${r}${n} @ ${e.url}${o}${i}`})),encodeURIComponent(t)}function formatUnknownError(e,t,r,n=!0){var o;if(!e||void 0===e.message&&!(t instanceof Error))return{message:`${r} ${JSON.stringify(t)}`,stacktrace:"",type:e&&e.name,id:generateUUID()};(null==e?void 0:e.stack)&&n&&(e.stack=filterRumStack(e.stack));const i=null==(o=null==e?void 0:e.stack)?void 0:o[0];return{message:encodeURIComponent(e.message||"Empty message"),stacktrace:toStackTraceString(e),type:e.name,col:i?i.column:void 0,line:i?i.line:void 0,file:encodeURIComponent(i&&i.url?i.url:""),id:generateUUID()}}function createHandlingStack(){const e=new Error;let t;return callMonitored((()=>{const r=computeStackTrace(e);(null==r?void 0:r.stack)&&(r.stack=filterRumStack(r.stack)),t=toStackTraceString(r)})),t}const SPAN_ID_BYTES$1=8,TRACE_ID_BYTES$1=16,B3_CONTEXT_HEADER="b3",TRACE_PARENT_HEADER$1="traceparent",UBER_TRACE_ID_HEADER="uber-trace-id",SHARED_CHAR_CODES_ARRAY$1=Array(32);function generateId(e){for(let t=0;t<2*e;t++)SHARED_CHAR_CODES_ARRAY$1[t]=Math.floor(16*Math.random())+48,SHARED_CHAR_CODES_ARRAY$1[t]>=58&&(SHARED_CHAR_CODES_ARRAY$1[t]+=39);return String.fromCharCode.apply(null,SHARED_CHAR_CODES_ARRAY$1.slice(0,2*e))}function generateTraceId(){return generateId(TRACE_ID_BYTES$1)}function generateSpanId(){return generateId(SPAN_ID_BYTES$1)}function getB3Header(e,t){return`${e}-${t}-1`}function getW3CTraceHeader(e,t){return`00-${e}-${t}-01`}function getJaegerTraceHeaderr(e,t){return`${e}:${t}:0:1`}const TRACE_HEADER={b3:{name:B3_CONTEXT_HEADER,getter:getB3Header},traceparent:{name:TRACE_PARENT_HEADER$1,getter:getW3CTraceHeader},uber:{name:UBER_TRACE_ID_HEADER,getter:getJaegerTraceHeaderr}};function patchHeaderOnRequest(e,t,r,n){var o;const i=null!=n?n:{};i.headers=null!=(o=i.headers)?o:{},i.headers[B3_CONTEXT_HEADER]=getB3Header(e,t),i.headers[TRACE_PARENT_HEADER$1]=getW3CTraceHeader(e,t),i.headers[UBER_TRACE_ID_HEADER]=getJaegerTraceHeaderr(e,t);try{if(r)for(const[n,o]of Object.entries(r))TRACE_HEADER[o]&&(i.headers[n]=TRACE_HEADER[o].getter(e,t))}catch(e){}return i}function patchHeaderOnXhr(e,t,r,n){e.setRequestHeader(B3_CONTEXT_HEADER,getB3Header(t,r)),e.setRequestHeader(TRACE_PARENT_HEADER$1,getW3CTraceHeader(t,r)),e.setRequestHeader(UBER_TRACE_ID_HEADER,getJaegerTraceHeaderr(t,r));try{if(n)for(const[o,i]of Object.entries(n))TRACE_HEADER[i]&&e.setRequestHeader(o,TRACE_HEADER[i].getter(t,r))}catch(e){}}function assembleOTAttribute(e,t,r){null!=e&&Object.keys(e).map((n=>{null==r||""==r?t[n]=e[n]:t[`${r}.${n}`]=e[n]}))}var __defProp$2=Object.defineProperty,__defProps$1=Object.defineProperties,__getOwnPropDescs$1=Object.getOwnPropertyDescriptors,__getOwnPropSymbols$2=Object.getOwnPropertySymbols,__hasOwnProp$2=Object.prototype.hasOwnProperty,__propIsEnum$2=Object.prototype.propertyIsEnumerable,__defNormalProp$2=(e,t,r)=>t in e?__defProp$2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues$2=(e,t)=>{for(var r in t||(t={}))__hasOwnProp$2.call(t,r)&&__defNormalProp$2(e,r,t[r]);if(__getOwnPropSymbols$2)for(var r of __getOwnPropSymbols$2(t))__propIsEnum$2.call(t,r)&&__defNormalProp$2(e,r,t[r]);return e},__spreadProps$1=(e,t)=>__defProps$1(e,__getOwnPropDescs$1(t));function createBaseTransformPlugin(){return{name:webTypes.InternalPlugin.BASE_TRANSFORM,run:function(){this.subscribe("*",((e,t)=>{const{otBase:r,extra:n}=e;r.service=this.options.service,r.attribute=__spreadProps$1(__spreadValues$2({},r.attribute),{sid:this.session.getSessionId(),pid:this.session.getPageId(),uid:this.options.uid}),this.options.nickname&&(r.attribute.nickname=this.options.nickname),assembleOTAttribute(this.options.custom,r.attribute,"custom"),assembleOTAttribute({version:this.options.version},r.attribute),assembleOTAttribute({sdk_version:SDK_VERSION,workspace:this.options.workspace,"deployment.environment":this.options.env},r.resource);const o={otBase:r,extra:n};debugLog(webTypes.InternalPlugin.BASE_TRANSFORM,o),t(o)}),webTypes.InternalPluginPriority[webTypes.InternalPlugin.BASE_TRANSFORM])}}}function createLogPlugin(){return{name:webTypes.InternalPlugin.LOG_SEND,run:function(){}}}function notifyDep(e,t,r){if(t<0)return;const n=r=>{notifyDep(e,t-1,r)},o=e[t];catchUserErrors((()=>{o.callback.call(void 0,r,n)}),"plugin notify run error")()}class SubscribeChain{constructor(e){this.subscribeMap={},this.allNotifiers=[],this.allNotifiers=e}subscribeOne(e,t,r,n){var o;const i=null!=(o=this.subscribeMap[t])?o:[];this.subscribeMap[t]=i,i.push({name:e,priority:n,callback:r}),i.sort(((e,t)=>e.priority-t.priority))}notify(e,t){var r;const n=null!=(r=this.subscribeMap[e])?r:[];notifyDep(n,n.length-1,t)}subscribe(e,t,r,n){if("*"===t)for(let t=0;t<this.allNotifiers.length;t++){const o=this.allNotifiers[t];this.subscribeOne(e,o,r,n)}else this.subscribeOne(e,t,r,n)}getSubscribeMap(){return this.subscribeMap}}function createSession(){const e=generateTraceId();let t=generateSpanId();return{getSessionId:()=>e,getPageId:()=>t,refreshPageId:()=>{t=generateSpanId()}}}var __defProp$1=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols$1=Object.getOwnPropertySymbols,__hasOwnProp$1=Object.prototype.hasOwnProperty,__propIsEnum$1=Object.prototype.propertyIsEnumerable,__defNormalProp$1=(e,t,r)=>t in e?__defProp$1(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues$1=(e,t)=>{for(var r in t||(t={}))__hasOwnProp$1.call(t,r)&&__defNormalProp$1(e,r,t[r]);if(__getOwnPropSymbols$1)for(var r of __getOwnPropSymbols$1(t))__propIsEnum$1.call(t,r)&&__defNormalProp$1(e,r,t[r]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t));const mutableOptionKeys=["uid","nickname","env","service","version","custom"];class SLSClient{constructor(e){var t,r;this.isInit=!1,this.pendingPlugins=[],this.pluginMap={},this.session=createSession(),this.options=e,this.use(createLogPlugin()),this.use(createBaseTransformPlugin()),this.options.env=null!=(t=e.env)?t:"prod",this.options.version=null!=(r=e.version)?r:"-"}initPlugin(e){if(null==e||"object"!=typeof e)return void console.error("plugin is not a object");if(null==e.name||""===e.name)return void console.error("plugin name is required.");if(this.pluginMap[e.name])return void console.error(`plugin name: ${e.name} is conflict`);if("function"!=typeof e.run)return void console.error("plugin.run is not a function");const t=__spreadProps(__spreadValues$1({},this.context),{subscribe:(t,r,n)=>{this.sub.subscribe(e.name,t,r,n)},notify:t=>{this.sub.notify(e.name,t)}});catchUserErrors((()=>e.run.call(t)),`plugin ${e.name} init failed`)()}addLog(e){if(this.isInit){const t={t:webTypes.WebEventType.LOG};assembleOTAttribute(e,t,webTypes.WebEventType.LOG);const r={start:1e3*timeStampNow(),attribute:t,resource:{}};this.sub.notify(webTypes.InternalPlugin.LOG_SEND,{otBase:r,extra:{}})}else console.error("log should call after start")}setOptions(e){this.isInit?mutableOptionKeys.forEach((t=>{e[t]!==this.options[t]&&null!=e[t]&&""!=e[t]&&(this.options[t]=e[t],"uid"===t&&"function"==typeof this.setLocalStorage&&this.setLocalStorage(webTypes.SLS_TRACE_UID_KEY,e[t]))})):console.error("setOptions should call after start")}start(){if(!this.isInit){this.isInit=!0;let e=this.options.uid;null!=e&&""!==e||("function"==typeof this.getLocalStorage&&(e=this.getLocalStorage(webTypes.SLS_TRACE_UID_KEY)),null!=e&&""!==e||(e=genUid())),this.options.uid=e,"function"==typeof this.setLocalStorage&&this.setLocalStorage(webTypes.SLS_TRACE_UID_KEY,e);const t=this.pendingPlugins.map((e=>null==e?void 0:e.name)).filter((e=>null!=e));this.sub=new SubscribeChain(t),this.context={options:this.options,session:this.session};for(const e of this.pendingPlugins)this.initPlugin(e)}}use(e){this.isInit?console.error(`plugin: ${null==e?void 0:e.name} use should run before start`):this.pendingPlugins.push(e)}}var __defProp=Object.defineProperty,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,r)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues=(e,t)=>{for(var r in t||(t={}))__hasOwnProp.call(t,r)&&__defNormalProp(e,r,t[r]);if(__getOwnPropSymbols)for(var r of __getOwnPropSymbols(t))__propIsEnum.call(t,r)&&__defNormalProp(e,r,t[r]);return e};function makeWebPublicApi(e,t){var r;const n={current:void 0},o=[];let i={};return"function"==typeof t&&(i=null!=(r=t(n))?r:{}),__spreadValues({init:t=>{if(null!=n.current)return;n.current=e(t);const r=n.current;o.forEach((e=>{r.use(e)})),r.start()},use:e=>{o.push(e)},addLog:e=>{n.current.addLog(e)},onReady(e){e()},setOptions:e=>{n.current.setOptions(e)}},i)}class Subscribe{constructor(){this.dep=new Map}subscribe(e,t){const r=this.dep.get(e);r?this.dep.set(e,r.concat(t)):this.dep.set(e,[t])}notify(e,t){const r=this.dep.get(e);e&&r&&r.forEach((e=>{try{e(t)}catch(e){console.error(e)}}))}}webBase_cjs.ONE_DAY=ONE_DAY,webBase_cjs.ONE_HOUR=ONE_HOUR,webBase_cjs.ONE_KILO_BYTE=ONE_KILO_BYTE,webBase_cjs.ONE_MINUTE=ONE_MINUTE,webBase_cjs.ONE_SECOND=ONE_SECOND,webBase_cjs.ONE_YEAR=ONE_YEAR,webBase_cjs.Observable=Observable,webBase_cjs.SDK_VERSION=SDK_VERSION,webBase_cjs.SLSClient=SLSClient,webBase_cjs.Subscribe=Subscribe,webBase_cjs.assembleOTAttribute=assembleOTAttribute,webBase_cjs.buildHttpInfo=buildHttpInfo,webBase_cjs.callMonitored=callMonitored,webBase_cjs.catchUserErrors=catchUserErrors,webBase_cjs.clocksNow=clocksNow,webBase_cjs.clocksOrigin=clocksOrigin,webBase_cjs.computeStackTrace=computeStackTrace,webBase_cjs.createHandlingStack=createHandlingStack,webBase_cjs.currentDrift=currentDrift,webBase_cjs.debugLog=debugLog,webBase_cjs.defineGlobal=defineGlobal,webBase_cjs.defineGlobalValue=defineGlobalValue,webBase_cjs.elapsed=elapsed,webBase_cjs.formatErrorMessage=formatErrorMessage,webBase_cjs.formatUnknownError=formatUnknownError,webBase_cjs.genUid=genUid,webBase_cjs.generateSpanId=generateSpanId,webBase_cjs.generateTraceId=generateTraceId,webBase_cjs.generateUUID=generateUUID,webBase_cjs.getB3Header=getB3Header,webBase_cjs.getConsoleLogStatus=getConsoleLogStatus,webBase_cjs.getCorrectedTimeStamp=getCorrectedTimeStamp,webBase_cjs.getJaegerTraceHeaderr=getJaegerTraceHeaderr,webBase_cjs.getRelativeTime=getRelativeTime,webBase_cjs.getTimeStamp=getTimeStamp,webBase_cjs.getTrackUrl=getTrackUrl,webBase_cjs.getW3CTraceHeader=getW3CTraceHeader,webBase_cjs.includes=includes,webBase_cjs.instrumentMethod=instrumentMethod,webBase_cjs.instrumentMethodAndCallOriginal=instrumentMethodAndCallOriginal,webBase_cjs.isError=isError,webBase_cjs.isInternalUrl=isInternalUrl,webBase_cjs.isNumber=isNumber,webBase_cjs.isString=isString,webBase_cjs.isURL=isURL,webBase_cjs.looksLikeRelativeTime=looksLikeRelativeTime,webBase_cjs.makeWebPublicApi=makeWebPublicApi,webBase_cjs.matchCondition=matchCondition,webBase_cjs.matchSample=matchSample,webBase_cjs.matchWithDefault=matchWithDefault,webBase_cjs.monitor=monitor,webBase_cjs.noop=noop$1,webBase_cjs.parseUri=parseUri,webBase_cjs.patchHeaderOnRequest=patchHeaderOnRequest,webBase_cjs.patchHeaderOnXhr=patchHeaderOnXhr,webBase_cjs.relativeNow=relativeNow,webBase_cjs.relativeToClocks=relativeToClocks,webBase_cjs.resetNavigationStart=resetNavigationStart,webBase_cjs.round=round,webBase_cjs.shouldTrackBody=shouldTrackBody,webBase_cjs.startUnhandledErrorCollection=startUnhandledErrorCollection,webBase_cjs.throttle=throttle,webBase_cjs.timeStampNow=timeStampNow,webBase_cjs.toServerDuration=toServerDuration,webBase_cjs.toStackTraceString=toStackTraceString;let WebTracker$1=class{constructor(e){var t,r;this.timer=null,this.time=10,this.count=10,this.arr=[],this.time=null!=(t=e.time)?t:10,this.count=null!=(r=e.count)?r:10,this.url="https://"+e.project+"."+e.host+"/logstores/"+e.logstore+"/track",this.opt=e,e.installUnloadHook&&"function"==typeof e.installUnloadHook&&e.installUnloadHook((()=>{this.sendImmediateInner()}))}assemblePayload(e=this.arr){const t={__logs__:e};return this.opt.tags&&(t.__tags__=this.opt.tags),this.opt.topic&&(t.__topic__=this.opt.topic),this.opt.source&&(t.__source__=this.opt.source),JSON.stringify(t)}platformSend(){if(this.opt.sendPayload&&"function"==typeof this.opt.sendPayload){const e=this.assemblePayload();this.opt.sendPayload(this.url,e)}}transString(e){let t={};for(let r in e)"object"==typeof e[r]?t[r]=JSON.stringify(e[r]):t[r]=String(e[r]);return t}sendImmediateInner(){this.arr&&this.arr.length>0&&(this.platformSend(),null!=this.timer&&(clearTimeout(this.timer),this.timer=null),this.arr=[])}sendInner(){if(this.timer)this.arr.length>=this.count&&(clearTimeout(this.timer),this.timer=null,this.sendImmediateInner());else{const e=this;this.arr.length>=this.count||this.time<=0?this.sendImmediateInner():this.timer=setTimeout((function(){e.sendImmediateInner()}),1e3*this.time)}}send(e){const t=this.transString(e);this.arr.push(t),this.sendInner()}sendImmediate(e){const t=this.transString(e);this.arr.push(t),this.sendImmediateInner()}sendBatchLogs(e){const t=e.map((e=>this.transString(e)));this.arr.push(...t),this.sendInner()}sendBatchLogsImmediate(e){const t=e.map((e=>this.transString(e)));this.arr.push(...t),this.sendImmediateInner()}};var webTrackBase_cjs=WebTracker$1,webBase=webBase_cjs,WebTracker=webTrackBase_cjs;function sendBeacon(e,t){return!(!navigator||!navigator.sendBeacon)&&navigator.sendBeacon(`${e}?APIVersion=0.6.0`,t)}function sendUseXhr(e,t){const r=new window.XMLHttpRequest;r.open("POST",`${e}?APIVersion=0.6.0`,!0),r.send(t)}function send(e,t){try{sendBeacon(e,t)||sendUseXhr(e,t)}catch(t){window&&window.console&&"function"==typeof window.console.error&&(console.error("Failed to log to ali log service because of this exception:\n"+t),console.error("Failed log data:",e))}}class WebTrackerBrowser extends WebTracker{constructor(e){super(Object.assign({},e,{installUnloadHook:e=>{window.addEventListener("beforeunload",(()=>{e()}))},sendPayload:(e,t)=>{send(e,t)}}))}}webBase.defineGlobal(window,"SLS_Tracker",WebTrackerBrowser);var webTrackBrowser_cjs=WebTrackerBrowser,SlsTracker=getDefaultExportFromCjs(webTrackBrowser_cjs);let getRandomValues;const rnds8=new Uint8Array(16);function rng(){if(!getRandomValues&&(getRandomValues="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!getRandomValues))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return getRandomValues(rnds8)}const byteToHex=[];for(let e=0;e<256;++e)byteToHex.push((e+256).toString(16).slice(1));function unsafeStringify(e,t=0){return(byteToHex[e[t+0]]+byteToHex[e[t+1]]+byteToHex[e[t+2]]+byteToHex[e[t+3]]+"-"+byteToHex[e[t+4]]+byteToHex[e[t+5]]+"-"+byteToHex[e[t+6]]+byteToHex[e[t+7]]+"-"+byteToHex[e[t+8]]+byteToHex[e[t+9]]+"-"+byteToHex[e[t+10]]+byteToHex[e[t+11]]+byteToHex[e[t+12]]+byteToHex[e[t+13]]+byteToHex[e[t+14]]+byteToHex[e[t+15]]).toLowerCase()}const randomUUID="undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);var native={randomUUID:randomUUID};function v4(e,t,r){if(native.randomUUID&&!t&&!e)return native.randomUUID();const n=(e=e||{}).random||(e.rng||rng)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return unsafeStringify(n)}var Dep=function(){function e(){this.id=new Date,this.subs=[]}return e.prototype.defined=function(){e.watch.add(this)},e.prototype.notify=function(){this.subs.forEach((function(e){if("function"==typeof e.update)try{e.update.apply(e)}catch(e){console.log(e)}}))},e}();Dep.watch=null;var Watch=function(){function e(e,t){this.name=e,this.id=new Date,this.callBack=t}return e.prototype.add=function(e){e.subs.push(this)},e.prototype.update=function(){(0,this.callBack)(this.name)},e}(),addHistoryMethod=(historyDep=new Dep,function(e){if("historychange"===e)return function(e,t){var r=new Watch(e,t);Dep.watch=r,historyDep.defined(),Dep.watch=null};if("pushState"===e||"replaceState"===e){var t=history[e];return function(){t.apply(history,arguments),historyDep.notify()}}}),historyDep;window.addHistoryListener=addHistoryMethod("historychange"),history.pushState=addHistoryMethod("pushState"),history.replaceState=addHistoryMethod("replaceState");var nativeErrorMessageReg=/^(Uncaught\s)?(Syntax|Reference|Range|Type|URI)Error\:\s.*/;function isNativeErrorMessage(e){return nativeErrorMessageReg.test(e)}var _globalThis$2="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{},VERSION$4="1.9.0",re=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function _makeCompatibilityCheck(e){var t=new Set([e]),r=new Set,n=e.match(re);if(!n)return function(){return!1};var o=+n[1],i=+n[2],a=+n[3];if(null!=n[4])return function(t){return t===e};function s(e){return r.add(e),!1}function u(e){return t.add(e),!0}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var n=e.match(re);if(!n)return s(e);var c=+n[1],l=+n[2],p=+n[3];return null!=n[4]||o!==c?s(e):0===o?i===l&&a<=p?u(e):s(e):i<=l?u(e):s(e)}}var isCompatible=_makeCompatibilityCheck(VERSION$4),major=VERSION$4.split(".")[0],GLOBAL_OPENTELEMETRY_API_KEY=Symbol.for("opentelemetry.js.api."+major),_global$2=_globalThis$2;function registerGlobal(e,t,r,n){var o;void 0===n&&(n=!1);var i=_global$2[GLOBAL_OPENTELEMETRY_API_KEY]=null!==(o=_global$2[GLOBAL_OPENTELEMETRY_API_KEY])&&void 0!==o?o:{version:VERSION$4};if(!n&&i[e]){var a=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(i.version!==VERSION$4){a=new Error("@opentelemetry/api: Registration of version v"+i.version+" for "+e+" does not match previously registered API v"+VERSION$4);return r.error(a.stack||a.message),!1}return i[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+VERSION$4+"."),!0}function getGlobal(e){var t,r,n=null===(t=_global$2[GLOBAL_OPENTELEMETRY_API_KEY])||void 0===t?void 0:t.version;if(n&&isCompatible(n))return null===(r=_global$2[GLOBAL_OPENTELEMETRY_API_KEY])||void 0===r?void 0:r[e]}function unregisterGlobal(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+VERSION$4+".");var r=_global$2[GLOBAL_OPENTELEMETRY_API_KEY];r&&delete r[e]}var __read$i=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$8=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},DiagComponentLogger=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return logProxy("verbose",this._namespace,e)},e}(),DiagLogLevel;function logProxy(e,t,r){var n=getGlobal("diag");if(n)return r.unshift(t),n[e].apply(n,__spreadArray$8([],__read$i(r),!1))}function createLogLevelDiagLogger(e,t){function r(r,n){var o=t[r];return"function"==typeof o&&e>=n?o.bind(t):function(){}}return e<DiagLogLevel.NONE?e=DiagLogLevel.NONE:e>DiagLogLevel.ALL&&(e=DiagLogLevel.ALL),t=t||{},{error:r("error",DiagLogLevel.ERROR),warn:r("warn",DiagLogLevel.WARN),info:r("info",DiagLogLevel.INFO),debug:r("debug",DiagLogLevel.DEBUG),verbose:r("verbose",DiagLogLevel.VERBOSE)}}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(DiagLogLevel||(DiagLogLevel={}));var __read$h=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$7=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},API_NAME$4="diag",DiagAPI=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=getGlobal("diag");if(n)return n[e].apply(n,__spreadArray$7([],__read$h(t),!1))}}var t=this;t.setLogger=function(e,r){var n,o,i;if(void 0===r&&(r={logLevel:DiagLogLevel.INFO}),e===t){var a=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=a.stack)&&void 0!==n?n:a.message),!1}"number"==typeof r&&(r={logLevel:r});var s=getGlobal("diag"),u=createLogLevelDiagLogger(null!==(o=r.logLevel)&&void 0!==o?o:DiagLogLevel.INFO,e);if(s&&!r.suppressOverrideMessage){var c=null!==(i=(new Error).stack)&&void 0!==i?i:"<failed to generate stacktrace>";s.warn("Current logger will be overwritten from "+c),u.warn("Current logger will overwrite one already registered from "+c)}return registerGlobal("diag",u,t,!0)},t.disable=function(){unregisterGlobal(API_NAME$4,t)},t.createComponentLogger=function(e){return new DiagComponentLogger(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),__read$g=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__values$8=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},BaggageImpl=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map((function(e){var t=__read$g(e,2);return[t[0],t[1]]}))},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var i=new e(this._entries);try{for(var a=__values$8(n),s=a.next();!s.done;s=a.next()){var u=s.value;i._entries.delete(u)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return i},e.prototype.clear=function(){return new e},e}(),baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata"),diag$1=DiagAPI.instance();function createBaggage(e){return void 0===e&&(e={}),new BaggageImpl(new Map(Object.entries(e)))}function baggageEntryMetadataFromString(e){return"string"!=typeof e&&(diag$1.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:baggageEntryMetadataSymbol,toString:function(){return e}}}function createContextKey(e){return Symbol.for(e)}var BaseContext=function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,n){var o=new e(r._currentContext);return o._currentContext.set(t,n),o},r.deleteValue=function(t){var n=new e(r._currentContext);return n._currentContext.delete(t),n}},ROOT_CONTEXT=new BaseContext,__extends$a=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),NoopMeter=function(){function e(){}return e.prototype.createGauge=function(e,t){return NOOP_GAUGE_METRIC},e.prototype.createHistogram=function(e,t){return NOOP_HISTOGRAM_METRIC},e.prototype.createCounter=function(e,t){return NOOP_COUNTER_METRIC},e.prototype.createUpDownCounter=function(e,t){return NOOP_UP_DOWN_COUNTER_METRIC},e.prototype.createObservableGauge=function(e,t){return NOOP_OBSERVABLE_GAUGE_METRIC},e.prototype.createObservableCounter=function(e,t){return NOOP_OBSERVABLE_COUNTER_METRIC},e.prototype.createObservableUpDownCounter=function(e,t){return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),NoopMetric=function(){},NoopCounterMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t.prototype.add=function(e,t){},t}(NoopMetric),NoopUpDownCounterMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t.prototype.add=function(e,t){},t}(NoopMetric),NoopGaugeMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t.prototype.record=function(e,t){},t}(NoopMetric),NoopHistogramMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t.prototype.record=function(e,t){},t}(NoopMetric),NoopObservableMetric=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),NoopObservableCounterMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t}(NoopObservableMetric),NoopObservableGaugeMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t}(NoopObservableMetric),NoopObservableUpDownCounterMetric=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends$a(t,e),t}(NoopObservableMetric),NOOP_METER=new NoopMeter,NOOP_COUNTER_METRIC=new NoopCounterMetric,NOOP_GAUGE_METRIC=new NoopGaugeMetric,NOOP_HISTOGRAM_METRIC=new NoopHistogramMetric,NOOP_UP_DOWN_COUNTER_METRIC=new NoopUpDownCounterMetric,NOOP_OBSERVABLE_COUNTER_METRIC=new NoopObservableCounterMetric,NOOP_OBSERVABLE_GAUGE_METRIC=new NoopObservableGaugeMetric,NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new NoopObservableUpDownCounterMetric,defaultTextMapGetter={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},defaultTextMapSetter={set:function(e,t,r){null!=e&&(e[t]=r)}},__read$f=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$6=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},NoopContextManager=function(){function e(){}return e.prototype.active=function(){return ROOT_CONTEXT},e.prototype.with=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];return t.call.apply(t,__spreadArray$6([r],__read$f(n),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),__read$e=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$5=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},API_NAME$3="context",NOOP_CONTEXT_MANAGER=new NoopContextManager,ContextAPI=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return registerGlobal(API_NAME$3,e,DiagAPI.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var n,o=[],i=3;i<arguments.length;i++)o[i-3]=arguments[i];return(n=this._getContextManager()).with.apply(n,__spreadArray$5([e,t,r],__read$e(o),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return getGlobal(API_NAME$3)||NOOP_CONTEXT_MANAGER},e.prototype.disable=function(){this._getContextManager().disable(),unregisterGlobal(API_NAME$3,DiagAPI.instance())},e}(),TraceFlags;!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(TraceFlags||(TraceFlags={}));var INVALID_SPANID="0000000000000000",INVALID_TRACEID="00000000000000000000000000000000",INVALID_SPAN_CONTEXT={traceId:INVALID_TRACEID,spanId:INVALID_SPANID,traceFlags:TraceFlags.NONE},NonRecordingSpan=function(){function e(e){void 0===e&&(e=INVALID_SPAN_CONTEXT),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),SPAN_KEY=createContextKey("OpenTelemetry Context Key SPAN");function getSpan(e){return e.getValue(SPAN_KEY)||void 0}function getActiveSpan(){return getSpan(ContextAPI.getInstance().active())}function setSpan(e,t){return e.setValue(SPAN_KEY,t)}function deleteSpan(e){return e.deleteValue(SPAN_KEY)}function setSpanContext(e,t){return setSpan(e,new NonRecordingSpan(t))}function getSpanContext(e){var t;return null===(t=getSpan(e))||void 0===t?void 0:t.spanContext()}var VALID_TRACEID_REGEX=/^([0-9a-f]{32})$/i,VALID_SPANID_REGEX=/^[0-9a-f]{16}$/i;function isValidTraceId(e){return VALID_TRACEID_REGEX.test(e)&&e!==INVALID_TRACEID}function isValidSpanId(e){return VALID_SPANID_REGEX.test(e)&&e!==INVALID_SPANID}function isSpanContextValid(e){return isValidTraceId(e.traceId)&&isValidSpanId(e.spanId)}function wrapSpanContext(e){return new NonRecordingSpan(e)}var contextApi=ContextAPI.getInstance(),NoopTracer=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=contextApi.active()),Boolean(null==t?void 0:t.root))return new NonRecordingSpan;var n=r&&getSpanContext(r);return isSpanContext(n)&&isSpanContextValid(n)?new NonRecordingSpan(n):new NonRecordingSpan},e.prototype.startActiveSpan=function(e,t,r,n){var o,i,a;if(!(arguments.length<2)){2===arguments.length?a=t:3===arguments.length?(o=t,a=r):(o=t,i=r,a=n);var s=null!=i?i:contextApi.active(),u=this.startSpan(e,o,s),c=setSpan(s,u);return contextApi.with(c,a,void 0,u)}},e}();function isSpanContext(e){return"object"==typeof e&&"string"==typeof e.spanId&&"string"==typeof e.traceId&&"number"==typeof e.traceFlags}var NOOP_TRACER=new NoopTracer,ProxyTracer=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,n){var o=this._getTracer();return Reflect.apply(o.startActiveSpan,o,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):NOOP_TRACER},e}(),NoopTracerProvider=function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new NoopTracer},e}(),NOOP_TRACER_PROVIDER=new NoopTracerProvider,ProxyTracerProvider=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var n;return null!==(n=this.getDelegateTracer(e,t,r))&&void 0!==n?n:new ProxyTracer(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!==(e=this._delegate)&&void 0!==e?e:NOOP_TRACER_PROVIDER},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)},e}(),SamplingDecision$1,SpanKind,SpanStatusCode;!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(SamplingDecision$1||(SamplingDecision$1={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(SpanKind||(SpanKind={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(SpanStatusCode||(SpanStatusCode={}));var context=ContextAPI.getInstance(),diag=DiagAPI.instance(),NoopMeterProvider=function(){function e(){}return e.prototype.getMeter=function(e,t,r){return NOOP_METER},e}(),NOOP_METER_PROVIDER=new NoopMeterProvider,API_NAME$2="metrics",MetricsAPI=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return registerGlobal(API_NAME$2,e,DiagAPI.instance())},e.prototype.getMeterProvider=function(){return getGlobal(API_NAME$2)||NOOP_METER_PROVIDER},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){unregisterGlobal(API_NAME$2,DiagAPI.instance())},e}(),metrics=MetricsAPI.getInstance(),NoopTextMapPropagator=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),BAGGAGE_KEY=createContextKey("OpenTelemetry Baggage Key");function getBaggage(e){return e.getValue(BAGGAGE_KEY)||void 0}function getActiveBaggage(){return getBaggage(ContextAPI.getInstance().active())}function setBaggage(e,t){return e.setValue(BAGGAGE_KEY,t)}function deleteBaggage(e){return e.deleteValue(BAGGAGE_KEY)}var API_NAME$1="propagation",NOOP_TEXT_MAP_PROPAGATOR=new NoopTextMapPropagator,PropagationAPI=function(){function e(){this.createBaggage=createBaggage,this.getBaggage=getBaggage,this.getActiveBaggage=getActiveBaggage,this.setBaggage=setBaggage,this.deleteBaggage=deleteBaggage}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return registerGlobal(API_NAME$1,e,DiagAPI.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=defaultTextMapSetter),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=defaultTextMapGetter),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){unregisterGlobal(API_NAME$1,DiagAPI.instance())},e.prototype._getGlobalPropagator=function(){return getGlobal(API_NAME$1)||NOOP_TEXT_MAP_PROPAGATOR},e}(),propagation=PropagationAPI.getInstance(),API_NAME="trace",TraceAPI=function(){function e(){this._proxyTracerProvider=new ProxyTracerProvider,this.wrapSpanContext=wrapSpanContext,this.isSpanContextValid=isSpanContextValid,this.deleteSpan=deleteSpan,this.getSpan=getSpan,this.getActiveSpan=getActiveSpan,this.getSpanContext=getSpanContext,this.setSpan=setSpan,this.setSpanContext=setSpanContext}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=registerGlobal(API_NAME,this._proxyTracerProvider,DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return getGlobal(API_NAME)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){unregisterGlobal(API_NAME,DiagAPI.instance()),this._proxyTracerProvider=new ProxyTracerProvider},e}(),trace=TraceAPI.getInstance(),SUPPRESS_TRACING_KEY=createContextKey("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function suppressTracing(e){return e.setValue(SUPPRESS_TRACING_KEY,!0)}function isTracingSuppressed(e){return!0===e.getValue(SUPPRESS_TRACING_KEY)}var BAGGAGE_KEY_PAIR_SEPARATOR="=",BAGGAGE_PROPERTIES_SEPARATOR=";",BAGGAGE_ITEMS_SEPARATOR=",",BAGGAGE_HEADER="baggage",BAGGAGE_MAX_NAME_VALUE_PAIRS=180,BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096,BAGGAGE_MAX_TOTAL_LENGTH=8192,__read$d=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function serializeKeyPairs(e){return e.reduce((function(e,t){var r=""+e+(""!==e?BAGGAGE_ITEMS_SEPARATOR:"")+t;return r.length>BAGGAGE_MAX_TOTAL_LENGTH?e:r}),"")}function getKeyPairs(e){return e.getAllEntries().map((function(e){var t=__read$d(e,2),r=t[0],n=t[1],o=encodeURIComponent(r)+"="+encodeURIComponent(n.value);return void 0!==n.metadata&&(o+=BAGGAGE_PROPERTIES_SEPARATOR+n.metadata.toString()),o}))}function parsePairKeyValue(e){var t=e.split(BAGGAGE_PROPERTIES_SEPARATOR);if(!(t.length<=0)){var r=t.shift();if(r){var n=r.indexOf(BAGGAGE_KEY_PAIR_SEPARATOR);if(!(n<=0)){var o,i=decodeURIComponent(r.substring(0,n).trim()),a=decodeURIComponent(r.substring(n+1).trim());return t.length>0&&(o=baggageEntryMetadataFromString(t.join(BAGGAGE_PROPERTIES_SEPARATOR))),{key:i,value:a,metadata:o}}}}}var W3CBaggagePropagator=function(){function e(){}return e.prototype.inject=function(e,t,r){var n=propagation.getBaggage(e);if(n&&!isTracingSuppressed(e)){var o=serializeKeyPairs(getKeyPairs(n).filter((function(e){return e.length<=BAGGAGE_MAX_PER_NAME_VALUE_PAIRS})).slice(0,BAGGAGE_MAX_NAME_VALUE_PAIRS));o.length>0&&r.set(t,BAGGAGE_HEADER,o)}},e.prototype.extract=function(e,t,r){var n=r.get(t,BAGGAGE_HEADER),o=Array.isArray(n)?n.join(BAGGAGE_ITEMS_SEPARATOR):n;if(!o)return e;var i={};return 0===o.length?e:(o.split(BAGGAGE_ITEMS_SEPARATOR).forEach((function(e){var t=parsePairKeyValue(e);if(t){var r={value:t.value};t.metadata&&(r.metadata=t.metadata),i[t.key]=r}})),0===Object.entries(i).length?e:propagation.setBaggage(e,propagation.createBaggage(i)))},e.prototype.fields=function(){return[BAGGAGE_HEADER]},e}(),__values$7=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},__read$c=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function sanitizeAttributes(e){var t,r,n={};if("object"!=typeof e||null==e)return n;try{for(var o=__values$7(Object.entries(e)),i=o.next();!i.done;i=o.next()){var a=__read$c(i.value,2),s=a[0],u=a[1];isAttributeKey(s)?isAttributeValue(u)?Array.isArray(u)?n[s]=u.slice():n[s]=u:diag.warn("Invalid attribute value set for key: "+s):diag.warn("Invalid attribute key: "+s)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return n}function isAttributeKey(e){return"string"==typeof e&&e.length>0}function isAttributeValue(e){return null==e||(Array.isArray(e)?isHomogeneousAttributeValueArray(e):isValidPrimitiveAttributeValue(e))}function isHomogeneousAttributeValueArray(e){var t,r,n;try{for(var o=__values$7(e),i=o.next();!i.done;i=o.next()){var a=i.value;if(null!=a){if(!n){if(isValidPrimitiveAttributeValue(a)){n=typeof a;continue}return!1}if(typeof a!==n)return!1}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return!0}function isValidPrimitiveAttributeValue(e){switch(typeof e){case"number":case"boolean":case"string":return!0}return!1}function loggingErrorHandler(){return function(e){diag.error(stringifyException(e))}}function stringifyException(e){return"string"==typeof e?e:JSON.stringify(flattenException(e))}function flattenException(e){for(var t={},r=e;null!==r;)Object.getOwnPropertyNames(r).forEach((function(e){if(!t[e]){var n=r[e];n&&(t[e]=String(n))}})),r=Object.getPrototypeOf(r);return t}var delegateHandler=loggingErrorHandler(),TracesSamplerValues;function globalErrorHandler(e){try{delegateHandler(e)}catch(e){}}!function(e){e.AlwaysOff="always_off",e.AlwaysOn="always_on",e.ParentBasedAlwaysOff="parentbased_always_off",e.ParentBasedAlwaysOn="parentbased_always_on",e.ParentBasedTraceIdRatio="parentbased_traceidratio",e.TraceIdRatio="traceidratio"}(TracesSamplerValues||(TracesSamplerValues={}));var DEFAULT_LIST_SEPARATOR=",",ENVIRONMENT_BOOLEAN_KEYS=["OTEL_SDK_DISABLED"];function isEnvVarABoolean(e){return ENVIRONMENT_BOOLEAN_KEYS.indexOf(e)>-1}var ENVIRONMENT_NUMBERS_KEYS=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"];function isEnvVarANumber(e){return ENVIRONMENT_NUMBERS_KEYS.indexOf(e)>-1}var ENVIRONMENT_LISTS_KEYS=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"];function isEnvVarAList(e){return ENVIRONMENT_LISTS_KEYS.indexOf(e)>-1}var DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=1/0,DEFAULT_ATTRIBUTE_COUNT_LIMIT=128,DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=128,DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=128,DEFAULT_ENVIRONMENT={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:DiagLogLevel.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_ATTRIBUTE_COUNT_LIMIT:DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:TracesSamplerValues.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]};function parseBoolean(e,t,r){if(void 0!==r[e]){var n=String(r[e]);t[e]="true"===n.toLowerCase()}}function parseNumber(e,t,r,n,o){if(void 0===n&&(n=-1/0),void 0===o&&(o=1/0),void 0!==r[e]){var i=Number(r[e]);isNaN(i)||(t[e]=i<n?n:i>o?o:i)}}function parseStringList(e,t,r,n){void 0===n&&(n=DEFAULT_LIST_SEPARATOR);var o=r[e];"string"==typeof o&&(t[e]=o.split(n).map((function(e){return e.trim()})))}var logLevelMap={ALL:DiagLogLevel.ALL,VERBOSE:DiagLogLevel.VERBOSE,DEBUG:DiagLogLevel.DEBUG,INFO:DiagLogLevel.INFO,WARN:DiagLogLevel.WARN,ERROR:DiagLogLevel.ERROR,NONE:DiagLogLevel.NONE};function setLogLevelFromEnv(e,t,r){var n=r[e];if("string"==typeof n){var o=logLevelMap[n.toUpperCase()];null!=o&&(t[e]=o)}}function parseEnvironment(e){var t={};for(var r in DEFAULT_ENVIRONMENT){var n=r;if("OTEL_LOG_LEVEL"===n)setLogLevelFromEnv(n,t,e);else if(isEnvVarABoolean(n))parseBoolean(n,t,e);else if(isEnvVarANumber(n))parseNumber(n,t,e);else if(isEnvVarAList(n))parseStringList(n,t,e);else{var o=e[n];null!=o&&(t[n]=String(o))}}return t}var _globalThis$1="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{};function getEnv(){var e=parseEnvironment(_globalThis$1);return Object.assign({},DEFAULT_ENVIRONMENT,e)}function getEnvWithoutDefaults(){return parseEnvironment(_globalThis$1)}function intValue(e){return e>=48&&e<=57?e-48:e>=97&&e<=102?e-87:e-55}function hexToBinary(e){for(var t=new Uint8Array(e.length/2),r=0,n=0;n<e.length;n+=2){var o=intValue(e.charCodeAt(n)),i=intValue(e.charCodeAt(n+1));t[r++]=o<<4|i}return t}var otperformance=performance,VERSION$3="1.28.0",TMP_EXCEPTION_TYPE="exception.type",TMP_EXCEPTION_MESSAGE="exception.message",TMP_EXCEPTION_STACKTRACE="exception.stacktrace",TMP_HTTP_METHOD="http.method",TMP_HTTP_URL="http.url",TMP_HTTP_HOST="http.host",TMP_HTTP_SCHEME="http.scheme",TMP_HTTP_STATUS_CODE="http.status_code",TMP_HTTP_USER_AGENT="http.user_agent",TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED="http.request_content_length_uncompressed",TMP_HTTP_RESPONSE_CONTENT_LENGTH="http.response_content_length",TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED="http.response_content_length_uncompressed",SEMATTRS_EXCEPTION_TYPE=TMP_EXCEPTION_TYPE,SEMATTRS_EXCEPTION_MESSAGE=TMP_EXCEPTION_MESSAGE,SEMATTRS_EXCEPTION_STACKTRACE=TMP_EXCEPTION_STACKTRACE,SEMATTRS_HTTP_METHOD=TMP_HTTP_METHOD,SEMATTRS_HTTP_URL=TMP_HTTP_URL,SEMATTRS_HTTP_HOST=TMP_HTTP_HOST,SEMATTRS_HTTP_SCHEME=TMP_HTTP_SCHEME,SEMATTRS_HTTP_STATUS_CODE=TMP_HTTP_STATUS_CODE,SEMATTRS_HTTP_USER_AGENT=TMP_HTTP_USER_AGENT,SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=TMP_HTTP_RESPONSE_CONTENT_LENGTH,SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,TMP_PROCESS_RUNTIME_NAME="process.runtime.name",TMP_SERVICE_NAME="service.name",TMP_TELEMETRY_SDK_NAME="telemetry.sdk.name",TMP_TELEMETRY_SDK_LANGUAGE="telemetry.sdk.language",TMP_TELEMETRY_SDK_VERSION="telemetry.sdk.version",SEMRESATTRS_PROCESS_RUNTIME_NAME=TMP_PROCESS_RUNTIME_NAME,SEMRESATTRS_SERVICE_NAME=TMP_SERVICE_NAME,SEMRESATTRS_TELEMETRY_SDK_NAME=TMP_TELEMETRY_SDK_NAME,SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=TMP_TELEMETRY_SDK_LANGUAGE,SEMRESATTRS_TELEMETRY_SDK_VERSION=TMP_TELEMETRY_SDK_VERSION,TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS="webjs",TELEMETRYSDKLANGUAGEVALUES_WEBJS=TMP_TELEMETRYSDKLANGUAGEVALUES_WEBJS,ATTR_SERVICE_NAME="service.name",_a$1,SDK_INFO=(_a$1={},_a$1[SEMRESATTRS_TELEMETRY_SDK_NAME]="opentelemetry",_a$1[SEMRESATTRS_PROCESS_RUNTIME_NAME]="browser",_a$1[SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]=TELEMETRYSDKLANGUAGEVALUES_WEBJS,_a$1[SEMRESATTRS_TELEMETRY_SDK_VERSION]=VERSION$3,_a$1);function unrefTimer(e){}var NANOSECOND_DIGITS=9,NANOSECOND_DIGITS_IN_MILLIS=6,MILLISECONDS_TO_NANOSECONDS=Math.pow(10,NANOSECOND_DIGITS_IN_MILLIS),SECOND_TO_NANOSECONDS=Math.pow(10,NANOSECOND_DIGITS),ExportResultCode;function millisToHrTime(e){var t=e/1e3;return[Math.trunc(t),Math.round(e%1e3*MILLISECONDS_TO_NANOSECONDS)]}function getTimeOrigin(){var e=otperformance.timeOrigin;if("number"!=typeof e){var t=otperformance;e=t.timing&&t.timing.fetchStart}return e}function hrTime(e){return addHrTimes(millisToHrTime(getTimeOrigin()),millisToHrTime("number"==typeof e?e:otperformance.now()))}function timeInputToHrTime(e){if(isTimeInputHrTime(e))return e;if("number"==typeof e)return e<getTimeOrigin()?hrTime(e):millisToHrTime(e);if(e instanceof Date)return millisToHrTime(e.getTime());throw TypeError("Invalid input type")}function hrTimeDuration(e,t){var r=t[0]-e[0],n=t[1]-e[1];return n<0&&(r-=1,n+=SECOND_TO_NANOSECONDS),[r,n]}function hrTimeToNanoseconds(e){return e[0]*SECOND_TO_NANOSECONDS+e[1]}function isTimeInputHrTime(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function isTimeInput(e){return isTimeInputHrTime(e)||"number"==typeof e||e instanceof Date}function addHrTimes(e,t){var r=[e[0]+t[0],e[1]+t[1]];return r[1]>=SECOND_TO_NANOSECONDS&&(r[1]-=SECOND_TO_NANOSECONDS,r[0]+=1),r}!function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILED=1]="FAILED"}(ExportResultCode||(ExportResultCode={}));var __values$6=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},CompositePropagator=function(){function e(e){var t;void 0===e&&(e={}),this._propagators=null!==(t=e.propagators)&&void 0!==t?t:[],this._fields=Array.from(new Set(this._propagators.map((function(e){return"function"==typeof e.fields?e.fields():[]})).reduce((function(e,t){return e.concat(t)}),[])))}return e.prototype.inject=function(e,t,r){var n,o;try{for(var i=__values$6(this._propagators),a=i.next();!a.done;a=i.next()){var s=a.value;try{s.inject(e,t,r)}catch(e){diag.warn("Failed to inject with "+s.constructor.name+". Err: "+e.message)}}}catch(e){n={error:e}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}},e.prototype.extract=function(e,t,r){return this._propagators.reduce((function(e,n){try{return n.extract(e,t,r)}catch(e){diag.warn("Failed to extract with "+n.constructor.name+". Err: "+e.message)}return e}),e)},e.prototype.fields=function(){return this._fields.slice()},e}(),VALID_KEY_CHAR_RANGE="[_0-9a-z-*/]",VALID_KEY="[a-z]"+VALID_KEY_CHAR_RANGE+"{0,255}",VALID_VENDOR_KEY="[a-z0-9]"+VALID_KEY_CHAR_RANGE+"{0,240}@[a-z]"+VALID_KEY_CHAR_RANGE+"{0,13}",VALID_KEY_REGEX=new RegExp("^(?:"+VALID_KEY+"|"+VALID_VENDOR_KEY+")$"),VALID_VALUE_BASE_REGEX=/^[ -~]{0,255}[!-~]$/,INVALID_VALUE_COMMA_EQUAL_REGEX=/,|=/;function validateKey(e){return VALID_KEY_REGEX.test(e)}function validateValue(e){return VALID_VALUE_BASE_REGEX.test(e)&&!INVALID_VALUE_COMMA_EQUAL_REGEX.test(e)}var MAX_TRACE_STATE_ITEMS=32,MAX_TRACE_STATE_LEN=512,LIST_MEMBERS_SEPARATOR=",",LIST_MEMBER_KEY_VALUE_SPLITTER="=",TraceState=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce((function(t,r){return t.push(r+LIST_MEMBER_KEY_VALUE_SPLITTER+e.get(r)),t}),[]).join(LIST_MEMBERS_SEPARATOR)},e.prototype._parse=function(e){e.length>MAX_TRACE_STATE_LEN||(this._internalState=e.split(LIST_MEMBERS_SEPARATOR).reverse().reduce((function(e,t){var r=t.trim(),n=r.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);if(-1!==n){var o=r.slice(0,n),i=r.slice(n+1,t.length);validateKey(o)&&validateValue(i)&&e.set(o,i)}return e}),new Map),this._internalState.size>MAX_TRACE_STATE_ITEMS&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,MAX_TRACE_STATE_ITEMS))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}(),TRACE_PARENT_HEADER="traceparent",TRACE_STATE_HEADER="tracestate",VERSION$2="00",VERSION_PART="(?!ff)[\\da-f]{2}",TRACE_ID_PART="(?![0]{32})[\\da-f]{32}",PARENT_ID_PART="(?![0]{16})[\\da-f]{16}",FLAGS_PART="[\\da-f]{2}",TRACE_PARENT_REGEX=new RegExp("^\\s?("+VERSION_PART+")-("+TRACE_ID_PART+")-("+PARENT_ID_PART+")-("+FLAGS_PART+")(-.*)?\\s?$");function parseTraceParent(e){var t=TRACE_PARENT_REGEX.exec(e);return t?"00"===t[1]&&t[5]?null:{traceId:t[2],spanId:t[3],traceFlags:parseInt(t[4],16)}:null}var W3CTraceContextPropagator=function(){function e(){}return e.prototype.inject=function(e,t,r){var n=trace.getSpanContext(e);if(n&&!isTracingSuppressed(e)&&isSpanContextValid(n)){var o=VERSION$2+"-"+n.traceId+"-"+n.spanId+"-0"+Number(n.traceFlags||TraceFlags.NONE).toString(16);r.set(t,TRACE_PARENT_HEADER,o),n.traceState&&r.set(t,TRACE_STATE_HEADER,n.traceState.serialize())}},e.prototype.extract=function(e,t,r){var n=r.get(t,TRACE_PARENT_HEADER);if(!n)return e;var o=Array.isArray(n)?n[0]:n;if("string"!=typeof o)return e;var i=parseTraceParent(o);if(!i)return e;i.isRemote=!0;var a=r.get(t,TRACE_STATE_HEADER);if(a){var s=Array.isArray(a)?a.join(","):a;i.traceState=new TraceState("string"==typeof s?s:void 0)}return trace.setSpanContext(e,i)},e.prototype.fields=function(){return[TRACE_PARENT_HEADER,TRACE_STATE_HEADER]},e}(),objectTag="[object Object]",nullTag="[object Null]",undefinedTag="[object Undefined]",funcProto=Function.prototype,funcToString=funcProto.toString,objectCtorString=funcToString.call(Object),getPrototype=overArg(Object.getPrototypeOf,Object),objectProto=Object.prototype,hasOwnProperty=objectProto.hasOwnProperty,symToStringTag=Symbol?Symbol.toStringTag:void 0,nativeObjectToString=objectProto.toString;function overArg(e,t){return function(r){return e(t(r))}}function isPlainObject(e){if(!isObjectLike(e)||baseGetTag(e)!==objectTag)return!1;var t=getPrototype(e);if(null===t)return!0;var r=hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&funcToString.call(r)===objectCtorString}function isObjectLike(e){return null!=e&&"object"==typeof e}function baseGetTag(e){return null==e?void 0===e?undefinedTag:nullTag:symToStringTag&&symToStringTag in Object(e)?getRawTag(e):objectToString(e)}function getRawTag(e){var t=hasOwnProperty.call(e,symToStringTag),r=e[symToStringTag],n=!1;try{e[symToStringTag]=void 0,n=!0}catch(e){}var o=nativeObjectToString.call(e);return n&&(t?e[symToStringTag]=r:delete e[symToStringTag]),o}function objectToString(e){return nativeObjectToString.call(e)}var MAX_LEVEL=20;function merge(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=e.shift(),n=new WeakMap;e.length>0;)r=mergeTwoObjects(r,e.shift(),0,n);return r}function takeValue(e){return isArray(e)?e.slice():e}function mergeTwoObjects(e,t,r,n){var o;if(void 0===r&&(r=0),!(r>MAX_LEVEL)){if(r++,isPrimitive(e)||isPrimitive(t)||isFunction$1(t))o=takeValue(t);else if(isArray(e)){if(o=e.slice(),isArray(t))for(var i=0,a=t.length;i<a;i++)o.push(takeValue(t[i]));else if(isObject(t))for(i=0,a=(s=Object.keys(t)).length;i<a;i++){o[u=s[i]]=takeValue(t[u])}}else if(isObject(e))if(isObject(t)){if(!shouldMerge(e,t))return t;o=Object.assign({},e);var s;for(i=0,a=(s=Object.keys(t)).length;i<a;i++){var u,c=t[u=s[i]];if(isPrimitive(c))void 0===c?delete o[u]:o[u]=c;else{var l=o[u],p=c;if(wasObjectReferenced(e,u,n)||wasObjectReferenced(t,u,n))delete o[u];else{if(isObject(l)&&isObject(p)){var f=n.get(l)||[],d=n.get(p)||[];f.push({obj:e,key:u}),d.push({obj:t,key:u}),n.set(l,f),n.set(p,d)}o[u]=mergeTwoObjects(o[u],c,r,n)}}}}else o=t;return o}}function wasObjectReferenced(e,t,r){for(var n=r.get(e[t])||[],o=0,i=n.length;o<i;o++){var a=n[o];if(a.key===t&&a.obj===e)return!0}return!1}function isArray(e){return Array.isArray(e)}function isFunction$1(e){return"function"==typeof e}function isObject(e){return!isPrimitive(e)&&!isArray(e)&&!isFunction$1(e)&&"object"==typeof e}function isPrimitive(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||void 0===e||e instanceof Date||e instanceof RegExp||null===e}function shouldMerge(e,t){return!(!isPlainObject(e)||!isPlainObject(t))}var __values$5=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};function urlMatches(e,t){return"string"==typeof t?e===t:!!e.match(t)}function isUrlIgnored(e,t){var r,n;if(!t)return!1;try{for(var o=__values$5(t),i=o.next();!i.done;i=o.next()){if(urlMatches(e,i.value))return!0}}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!1}var Deferred=function(){function e(){var e=this;this._promise=new Promise((function(t,r){e._resolve=t,e._reject=r}))}return Object.defineProperty(e.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),e.prototype.resolve=function(e){this._resolve(e)},e.prototype.reject=function(e){this._reject(e)},e}(),__read$b=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$4=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},BindOnceFuture=function(){function e(e,t){this._callback=e,this._that=t,this._isCalled=!1,this._deferred=new Deferred}return Object.defineProperty(e.prototype,"isCalled",{get:function(){return this._isCalled},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"promise",{get:function(){return this._deferred.promise},enumerable:!1,configurable:!0}),e.prototype.call=function(){for(var e,t=this,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];if(!this._isCalled){this._isCalled=!0;try{Promise.resolve((e=this._callback).call.apply(e,__spreadArray$4([this._that],__read$b(r),!1))).then((function(e){return t._deferred.resolve(e)}),(function(e){return t._deferred.reject(e)}))}catch(e){this._deferred.reject(e)}}return this._deferred.promise},e}(),ExceptionEventName="exception",__assign$3=window&&window.__assign||function(){return __assign$3=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign$3.apply(this,arguments)},__values$4=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},__read$a=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$3=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},Span=function(){function e(e,t,r,n,o,i,a,s,u,c){void 0===a&&(a=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:SpanStatusCode.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=r,this._spanContext=n,this.parentSpanId=i,this.kind=o,this.links=a;var l=Date.now();this._performanceStartTime=otperformance.now(),this._performanceOffset=l-(this._performanceStartTime+getTimeOrigin()),this._startTimeProvided=null!=s,this.startTime=this._getTime(null!=s?s:l),this.resource=e.resource,this.instrumentationLibrary=e.instrumentationLibrary,this._spanLimits=e.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,null!=c&&this.setAttributes(c),this._spanProcessor=e.getActiveSpanProcessor(),this._spanProcessor.onStart(this,t)}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return null==t||this._isSpanEnded()?this:0===e.length?(diag.warn("Invalid attribute key: "+e),this):isAttributeValue(t)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,e)?(this._droppedAttributesCount++,this):(this.attributes[e]=this._truncateToSize(t),this):(diag.warn("Invalid attribute value set for key: "+e),this)},e.prototype.setAttributes=function(e){var t,r;try{for(var n=__values$4(Object.entries(e)),o=n.next();!o.done;o=n.next()){var i=__read$a(o.value,2),a=i[0],s=i[1];this.setAttribute(a,s)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return this},e.prototype.addEvent=function(e,t,r){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return diag.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(0===this._droppedEventsCount&&diag.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),isTimeInput(t)&&(isTimeInput(r)||(r=t),t=void 0);var n=sanitizeAttributes(t);return this.events.push({name:e,attributes:n,time:this._getTime(r),droppedAttributesCount:0}),this},e.prototype.addLink=function(e){return this.links.push(e),this},e.prototype.addLinks=function(e){var t;return(t=this.links).push.apply(t,__spreadArray$3([],__read$a(e),!1)),this},e.prototype.setStatus=function(e){return this._isSpanEnded()||(this.status=__assign$3({},e),null!=this.status.message&&"string"!=typeof e.message&&(diag.warn("Dropping invalid status.message of type '"+typeof e.message+"', expected 'string'"),delete this.status.message)),this},e.prototype.updateName=function(e){return this._isSpanEnded()||(this.name=e),this},e.prototype.end=function(e){this._isSpanEnded()?diag.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once."):(this._ended=!0,this.endTime=this._getTime(e),this._duration=hrTimeDuration(this.startTime,this.endTime),this._duration[0]<0&&(diag.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&diag.warn("Dropped "+this._droppedEventsCount+" events because eventCountLimit reached"),this._spanProcessor.onEnd(this))},e.prototype._getTime=function(e){if("number"==typeof e&&e<=otperformance.now())return hrTime(e+this._performanceOffset);if("number"==typeof e)return millisToHrTime(e);if(e instanceof Date)return millisToHrTime(e.getTime());if(isTimeInputHrTime(e))return e;if(this._startTimeProvided)return millisToHrTime(Date.now());var t=otperformance.now()-this._performanceStartTime;return addHrTimes(this.startTime,millisToHrTime(t))},e.prototype.isRecording=function(){return!1===this._ended},e.prototype.recordException=function(e,t){var r={};"string"==typeof e?r[SEMATTRS_EXCEPTION_MESSAGE]=e:e&&(e.code?r[SEMATTRS_EXCEPTION_TYPE]=e.code.toString():e.name&&(r[SEMATTRS_EXCEPTION_TYPE]=e.name),e.message&&(r[SEMATTRS_EXCEPTION_MESSAGE]=e.message),e.stack&&(r[SEMATTRS_EXCEPTION_STACKTRACE]=e.stack)),r[SEMATTRS_EXCEPTION_TYPE]||r[SEMATTRS_EXCEPTION_MESSAGE]?this.addEvent(ExceptionEventName,r,t):diag.warn("Failed to record an exception "+e)},Object.defineProperty(e.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),e.prototype._isSpanEnded=function(){return this._ended&&diag.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},e.prototype._truncateToLimitUtil=function(e,t){return e.length<=t?e:e.substr(0,t)},e.prototype._truncateToSize=function(e){var t=this,r=this._attributeValueLengthLimit;return r<=0?(diag.warn("Attribute value limit must be positive, got "+r),e):"string"==typeof e?this._truncateToLimitUtil(e,r):Array.isArray(e)?e.map((function(e){return"string"==typeof e?t._truncateToLimitUtil(e,r):e})):e},e}(),SamplingDecision;!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(SamplingDecision||(SamplingDecision={}));var AlwaysOffSampler=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:SamplingDecision.NOT_RECORD}},e.prototype.toString=function(){return"AlwaysOffSampler"},e}(),AlwaysOnSampler=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:SamplingDecision.RECORD_AND_SAMPLED}},e.prototype.toString=function(){return"AlwaysOnSampler"},e}(),ParentBasedSampler=function(){function e(e){var t,r,n,o;this._root=e.root,this._root||(globalErrorHandler(new Error("ParentBasedSampler must have a root sampler configured")),this._root=new AlwaysOnSampler),this._remoteParentSampled=null!==(t=e.remoteParentSampled)&&void 0!==t?t:new AlwaysOnSampler,this._remoteParentNotSampled=null!==(r=e.remoteParentNotSampled)&&void 0!==r?r:new AlwaysOffSampler,this._localParentSampled=null!==(n=e.localParentSampled)&&void 0!==n?n:new AlwaysOnSampler,this._localParentNotSampled=null!==(o=e.localParentNotSampled)&&void 0!==o?o:new AlwaysOffSampler}return e.prototype.shouldSample=function(e,t,r,n,o,i){var a=trace.getSpanContext(e);return a&&isSpanContextValid(a)?a.isRemote?a.traceFlags&TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(e,t,r,n,o,i):this._remoteParentNotSampled.shouldSample(e,t,r,n,o,i):a.traceFlags&TraceFlags.SAMPLED?this._localParentSampled.shouldSample(e,t,r,n,o,i):this._localParentNotSampled.shouldSample(e,t,r,n,o,i):this._root.shouldSample(e,t,r,n,o,i)},e.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},e}(),TraceIdRatioBasedSampler=function(){function e(e){void 0===e&&(e=0),this._ratio=e,this._ratio=this._normalize(e),this._upperBound=Math.floor(4294967295*this._ratio)}return e.prototype.shouldSample=function(e,t){return{decision:isValidTraceId(t)&&this._accumulate(t)<this._upperBound?SamplingDecision.RECORD_AND_SAMPLED:SamplingDecision.NOT_RECORD}},e.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},e.prototype._normalize=function(e){return"number"!=typeof e||isNaN(e)?0:e>=1?1:e<=0?0:e},e.prototype._accumulate=function(e){for(var t=0,r=0;r<e.length/8;r++){var n=8*r;t=(t^parseInt(e.slice(n,n+8),16))>>>0}return t},e}(),env=getEnv(),FALLBACK_OTEL_TRACES_SAMPLER=TracesSamplerValues.AlwaysOn,DEFAULT_RATIO=1;function loadDefaultConfig(){var e=getEnv();return{sampler:buildSamplerFromEnv(env),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:e.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:e.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:e.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:e.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function buildSamplerFromEnv(e){switch(void 0===e&&(e=getEnv()),e.OTEL_TRACES_SAMPLER){case TracesSamplerValues.AlwaysOn:return new AlwaysOnSampler;case TracesSamplerValues.AlwaysOff:return new AlwaysOffSampler;case TracesSamplerValues.ParentBasedAlwaysOn:return new ParentBasedSampler({root:new AlwaysOnSampler});case TracesSamplerValues.ParentBasedAlwaysOff:return new ParentBasedSampler({root:new AlwaysOffSampler});case TracesSamplerValues.TraceIdRatio:return new TraceIdRatioBasedSampler(getSamplerProbabilityFromEnv(e));case TracesSamplerValues.ParentBasedTraceIdRatio:return new ParentBasedSampler({root:new TraceIdRatioBasedSampler(getSamplerProbabilityFromEnv(e))});default:return diag.error('OTEL_TRACES_SAMPLER value "'+e.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+FALLBACK_OTEL_TRACES_SAMPLER+'".'),new AlwaysOnSampler}}function getSamplerProbabilityFromEnv(e){if(void 0===e.OTEL_TRACES_SAMPLER_ARG||""===e.OTEL_TRACES_SAMPLER_ARG)return diag.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to "+DEFAULT_RATIO+"."),DEFAULT_RATIO;var t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(diag.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to "+DEFAULT_RATIO+"."),DEFAULT_RATIO):t<0||t>1?(diag.error("OTEL_TRACES_SAMPLER_ARG="+e.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to "+DEFAULT_RATIO+"."),DEFAULT_RATIO):t}function mergeConfig(e){var t={sampler:buildSamplerFromEnv()},r=loadDefaultConfig(),n=Object.assign({},r,t,e);return n.generalLimits=Object.assign({},r.generalLimits,e.generalLimits||{}),n.spanLimits=Object.assign({},r.spanLimits,e.spanLimits||{}),n}function reconfigureLimits(e){var t,r,n,o,i,a,s,u,c,l,p,f,d=Object.assign({},e.spanLimits),m=getEnvWithoutDefaults();return d.attributeCountLimit=null!==(a=null!==(i=null!==(o=null!==(r=null===(t=e.spanLimits)||void 0===t?void 0:t.attributeCountLimit)&&void 0!==r?r:null===(n=e.generalLimits)||void 0===n?void 0:n.attributeCountLimit)&&void 0!==o?o:m.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)&&void 0!==i?i:m.OTEL_ATTRIBUTE_COUNT_LIMIT)&&void 0!==a?a:DEFAULT_ATTRIBUTE_COUNT_LIMIT,d.attributeValueLengthLimit=null!==(f=null!==(p=null!==(l=null!==(u=null===(s=e.spanLimits)||void 0===s?void 0:s.attributeValueLengthLimit)&&void 0!==u?u:null===(c=e.generalLimits)||void 0===c?void 0:c.attributeValueLengthLimit)&&void 0!==l?l:m.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==p?p:m.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==f?f:DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,Object.assign({},e,{spanLimits:d})}var BatchSpanProcessorBase=function(){function e(e,t){this._exporter=e,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var r=getEnv();this._maxExportBatchSize="number"==typeof(null==t?void 0:t.maxExportBatchSize)?t.maxExportBatchSize:r.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==t?void 0:t.maxQueueSize)?t.maxQueueSize:r.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==t?void 0:t.scheduledDelayMillis)?t.scheduledDelayMillis:r.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==t?void 0:t.exportTimeoutMillis)?t.exportTimeoutMillis:r.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(diag.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return e.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){this._shutdownOnce.isCalled||0!=(e.spanContext().traceFlags&TraceFlags.SAMPLED)&&this._addToBuffer(e)},e.prototype.shutdown=function(){return this._shutdownOnce.call()},e.prototype._shutdown=function(){var e=this;return Promise.resolve().then((function(){return e.onShutdown()})).then((function(){return e._flushAll()})).then((function(){return e._exporter.shutdown()}))},e.prototype._addToBuffer=function(e){if(this._finishedSpans.length>=this._maxQueueSize)return 0===this._droppedSpansCount&&diag.debug("maxQueueSize reached, dropping spans"),void this._droppedSpansCount++;this._droppedSpansCount>0&&(diag.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(e),this._maybeStartTimer()},e.prototype._flushAll=function(){var e=this;return new Promise((function(t,r){for(var n=[],o=0,i=Math.ceil(e._finishedSpans.length/e._maxExportBatchSize);o<i;o++)n.push(e._flushOneBatch());Promise.all(n).then((function(){t()})).catch(r)}))},e.prototype._flushOneBatch=function(){var e=this;return this._clearTimer(),0===this._finishedSpans.length?Promise.resolve():new Promise((function(t,r){var n=setTimeout((function(){r(new Error("Timeout"))}),e._exportTimeoutMillis);context.with(suppressTracing(context.active()),(function(){var o;e._finishedSpans.length<=e._maxExportBatchSize?(o=e._finishedSpans,e._finishedSpans=[]):o=e._finishedSpans.splice(0,e._maxExportBatchSize);for(var i=function(){return e._exporter.export(o,(function(e){var o;clearTimeout(n),e.code===ExportResultCode.SUCCESS?t():r(null!==(o=e.error)&&void 0!==o?o:new Error("BatchSpanProcessor: span export failed"))}))},a=null,s=0,u=o.length;s<u;s++){var c=o[s];c.resource.asyncAttributesPending&&c.resource.waitForAsyncAttributes&&(null!=a||(a=[]),a.push(c.resource.waitForAsyncAttributes()))}null===a?i():Promise.all(a).then(i,(function(e){globalErrorHandler(e),r(e)}))}))}))},e.prototype._maybeStartTimer=function(){var e=this;if(!this._isExporting){var t=function(){e._isExporting=!0,e._flushOneBatch().finally((function(){e._isExporting=!1,e._finishedSpans.length>0&&(e._clearTimer(),e._maybeStartTimer())})).catch((function(t){e._isExporting=!1,globalErrorHandler(t)}))};if(this._finishedSpans.length>=this._maxExportBatchSize)return t();void 0===this._timer&&(this._timer=setTimeout((function(){return t()}),this._scheduledDelayMillis),unrefTimer(this._timer))}},e.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},e}(),__extends$9=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),BatchSpanProcessor=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.onInit(r),n}return __extends$9(t,e),t.prototype.onInit=function(e){var t=this;!0!==(null==e?void 0:e.disableAutoFlushOnDocumentHide)&&"undefined"!=typeof document&&(this._visibilityChangeListener=function(){"hidden"===document.visibilityState&&t.forceFlush().catch((function(e){globalErrorHandler(e)}))},this._pageHideListener=function(){t.forceFlush().catch((function(e){globalErrorHandler(e)}))},document.addEventListener("visibilitychange",this._visibilityChangeListener),document.addEventListener("pagehide",this._pageHideListener))},t.prototype.onShutdown=function(){"undefined"!=typeof document&&(this._visibilityChangeListener&&document.removeEventListener("visibilitychange",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener("pagehide",this._pageHideListener))},t}(BatchSpanProcessorBase),SPAN_ID_BYTES=8,TRACE_ID_BYTES=16,RandomIdGenerator=function(){this.generateTraceId=getIdGenerator(TRACE_ID_BYTES),this.generateSpanId=getIdGenerator(SPAN_ID_BYTES)},SHARED_CHAR_CODES_ARRAY=Array(32);function getIdGenerator(e){return function(){for(var t=0;t<2*e;t++)SHARED_CHAR_CODES_ARRAY[t]=Math.floor(16*Math.random())+48,SHARED_CHAR_CODES_ARRAY[t]>=58&&(SHARED_CHAR_CODES_ARRAY[t]+=39);return String.fromCharCode.apply(null,SHARED_CHAR_CODES_ARRAY.slice(0,2*e))}}var Tracer=function(){function e(e,t,r){this._tracerProvider=r;var n=mergeConfig(t);this._sampler=n.sampler,this._generalLimits=n.generalLimits,this._spanLimits=n.spanLimits,this._idGenerator=t.idGenerator||new RandomIdGenerator,this.resource=r.resource,this.instrumentationLibrary=e}return e.prototype.startSpan=function(e,t,r){var n,o,i;void 0===t&&(t={}),void 0===r&&(r=context.active()),t.root&&(r=trace.deleteSpan(r));var a=trace.getSpan(r);if(isTracingSuppressed(r))return diag.debug("Instrumentation suppressed, returning Noop Span"),trace.wrapSpanContext(INVALID_SPAN_CONTEXT);var s,u,c,l=null==a?void 0:a.spanContext(),p=this._idGenerator.generateSpanId();l&&trace.isSpanContextValid(l)?(s=l.traceId,u=l.traceState,c=l.spanId):s=this._idGenerator.generateTraceId();var f=null!==(n=t.kind)&&void 0!==n?n:SpanKind.INTERNAL,d=(null!==(o=t.links)&&void 0!==o?o:[]).map((function(e){return{context:e.context,attributes:sanitizeAttributes(e.attributes)}})),m=sanitizeAttributes(t.attributes),g=this._sampler.shouldSample(r,s,e,f,m,d);u=null!==(i=g.traceState)&&void 0!==i?i:u;var h={traceId:s,spanId:p,traceFlags:g.decision===SamplingDecision$1.RECORD_AND_SAMPLED?TraceFlags.SAMPLED:TraceFlags.NONE,traceState:u};if(g.decision===SamplingDecision$1.NOT_RECORD)return diag.debug("Recording is off, propagating context in a non-recording span"),trace.wrapSpanContext(h);var y=sanitizeAttributes(Object.assign(m,g.attributes));return new Span(this,r,e,h,f,c,d,t.startTime,void 0,y)},e.prototype.startActiveSpan=function(e,t,r,n){var o,i,a;if(!(arguments.length<2)){2===arguments.length?a=t:3===arguments.length?(o=t,a=r):(o=t,i=r,a=n);var s=null!=i?i:context.active(),u=this.startSpan(e,o,s),c=trace.setSpan(s,u);return context.with(c,a,void 0,u)}},e.prototype.getGeneralLimits=function(){return this._generalLimits},e.prototype.getSpanLimits=function(){return this._spanLimits},e.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},e}();function defaultServiceName(){return"unknown_service"}var __assign$2=window&&window.__assign||function(){return __assign$2=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign$2.apply(this,arguments)},__awaiter$2=window&&window.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))},__generator$2=window&&window.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},__read$9=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},Resource=function(){function e(e,t){var r,n=this;this._attributes=e,this.asyncAttributesPending=null!=t,this._syncAttributes=null!==(r=this._attributes)&&void 0!==r?r:{},this._asyncAttributesPromise=null==t?void 0:t.then((function(e){return n._attributes=Object.assign({},n._attributes,e),n.asyncAttributesPending=!1,e}),(function(e){return diag.debug("a resource's async attributes promise rejected: %s",e),n.asyncAttributesPending=!1,{}}))}return e.empty=function(){return e.EMPTY},e.default=function(){var t;return new e(((t={})[SEMRESATTRS_SERVICE_NAME]=defaultServiceName(),t[SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]=SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_LANGUAGE],t[SEMRESATTRS_TELEMETRY_SDK_NAME]=SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_NAME],t[SEMRESATTRS_TELEMETRY_SDK_VERSION]=SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_VERSION],t))},Object.defineProperty(e.prototype,"attributes",{get:function(){var e;return this.asyncAttributesPending&&diag.error("Accessing resource attributes before async attributes settled"),null!==(e=this._attributes)&&void 0!==e?e:{}},enumerable:!1,configurable:!0}),e.prototype.waitForAsyncAttributes=function(){return __awaiter$2(this,void 0,void 0,(function(){return __generator$2(this,(function(e){switch(e.label){case 0:return this.asyncAttributesPending?[4,this._asyncAttributesPromise]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.merge=function(t){var r,n=this;if(!t)return this;var o=__assign$2(__assign$2({},this._syncAttributes),null!==(r=t._syncAttributes)&&void 0!==r?r:t.attributes);if(!this._asyncAttributesPromise&&!t._asyncAttributesPromise)return new e(o);var i=Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then((function(e){var r,o=__read$9(e,2),i=o[0],a=o[1];return __assign$2(__assign$2(__assign$2(__assign$2({},n._syncAttributes),i),null!==(r=t._syncAttributes)&&void 0!==r?r:t.attributes),a)}));return new e(o,i)},e.EMPTY=new e({}),e}(),__values$3=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},MultiSpanProcessor=function(){function e(e){this._spanProcessors=e}return e.prototype.forceFlush=function(){var e,t,r=[];try{for(var n=__values$3(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.forceFlush())}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return new Promise((function(e){Promise.all(r).then((function(){e()})).catch((function(t){globalErrorHandler(t||new Error("MultiSpanProcessor: forceFlush failed")),e()}))}))},e.prototype.onStart=function(e,t){var r,n;try{for(var o=__values$3(this._spanProcessors),i=o.next();!i.done;i=o.next()){i.value.onStart(e,t)}}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}},e.prototype.onEnd=function(e){var t,r;try{for(var n=__values$3(this._spanProcessors),o=n.next();!o.done;o=n.next()){o.value.onEnd(e)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},e.prototype.shutdown=function(){var e,t,r=[];try{for(var n=__values$3(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.shutdown())}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return new Promise((function(e,t){Promise.all(r).then((function(){e()}),t)}))},e}(),NoopSpanProcessor=function(){function e(){}return e.prototype.onStart=function(e,t){},e.prototype.onEnd=function(e){},e.prototype.shutdown=function(){return Promise.resolve()},e.prototype.forceFlush=function(){return Promise.resolve()},e}(),__read$8=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$2=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},ForceFlushState;!function(e){e[e.resolved=0]="resolved",e[e.timeout=1]="timeout",e[e.error=2]="error",e[e.unresolved=3]="unresolved"}(ForceFlushState||(ForceFlushState={}));var BasicTracerProvider=function(){function e(e){var t,r;void 0===e&&(e={}),this._registeredSpanProcessors=[],this._tracers=new Map;var n=merge({},loadDefaultConfig(),reconfigureLimits(e));if(this.resource=null!==(t=n.resource)&&void 0!==t?t:Resource.empty(),n.mergeResourceWithDefaults&&(this.resource=Resource.default().merge(this.resource)),this._config=Object.assign({},n,{resource:this.resource}),null===(r=e.spanProcessors)||void 0===r?void 0:r.length)this._registeredSpanProcessors=__spreadArray$2([],__read$8(e.spanProcessors),!1),this.activeSpanProcessor=new MultiSpanProcessor(this._registeredSpanProcessors);else{var o=this._buildExporterFromEnv();if(void 0!==o){var i=new BatchSpanProcessor(o);this.activeSpanProcessor=i}else this.activeSpanProcessor=new NoopSpanProcessor}}return e.prototype.getTracer=function(e,t,r){var n=e+"@"+(t||"")+":"+((null==r?void 0:r.schemaUrl)||"");return this._tracers.has(n)||this._tracers.set(n,new Tracer({name:e,version:t,schemaUrl:null==r?void 0:r.schemaUrl},this._config,this)),this._tracers.get(n)},e.prototype.addSpanProcessor=function(e){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch((function(e){return diag.error("Error while trying to shutdown current span processor",e)})),this._registeredSpanProcessors.push(e),this.activeSpanProcessor=new MultiSpanProcessor(this._registeredSpanProcessors)},e.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},e.prototype.register=function(e){void 0===e&&(e={}),trace.setGlobalTracerProvider(this),void 0===e.propagator&&(e.propagator=this._buildPropagatorFromEnv()),e.contextManager&&context.setGlobalContextManager(e.contextManager),e.propagator&&propagation.setGlobalPropagator(e.propagator)},e.prototype.forceFlush=function(){var e=this._config.forceFlushTimeoutMillis,t=this._registeredSpanProcessors.map((function(t){return new Promise((function(r){var n,o=setTimeout((function(){r(new Error("Span processor did not completed within timeout period of "+e+" ms")),n=ForceFlushState.timeout}),e);t.forceFlush().then((function(){clearTimeout(o),n!==ForceFlushState.timeout&&(n=ForceFlushState.resolved,r(n))})).catch((function(e){clearTimeout(o),n=ForceFlushState.error,r(e)}))}))}));return new Promise((function(e,r){Promise.all(t).then((function(t){var n=t.filter((function(e){return e!==ForceFlushState.resolved}));n.length>0?r(n):e()})).catch((function(e){return r([e])}))}))},e.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},e.prototype._getPropagator=function(e){var t;return null===(t=this.constructor._registeredPropagators.get(e))||void 0===t?void 0:t()},e.prototype._getSpanExporter=function(e){var t;return null===(t=this.constructor._registeredExporters.get(e))||void 0===t?void 0:t()},e.prototype._buildPropagatorFromEnv=function(){var e=this,t=Array.from(new Set(getEnv().OTEL_PROPAGATORS)),r=t.map((function(t){var r=e._getPropagator(t);return r||diag.warn('Propagator "'+t+'" requested through environment variable is unavailable.'),r})).reduce((function(e,t){return t&&e.push(t),e}),[]);return 0===r.length?void 0:1===t.length?r[0]:new CompositePropagator({propagators:r})},e.prototype._buildExporterFromEnv=function(){var e=getEnv().OTEL_TRACES_EXPORTER;if("none"!==e&&""!==e){var t=this._getSpanExporter(e);return t||diag.error('Exporter "'+e+'" requested through environment variable is unavailable.'),t}},e._registeredPropagators=new Map([["tracecontext",function(){return new W3CTraceContextPropagator}],["baggage",function(){return new W3CBaggagePropagator}]]),e._registeredExporters=new Map,e}(),__read$7=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray$1=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},StackContextManager=function(){function e(){this._enabled=!1,this._currentContext=ROOT_CONTEXT}return e.prototype._bindFunction=function(e,t){void 0===e&&(e=ROOT_CONTEXT);var r=this,n=function(){for(var n=this,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];return r.with(e,(function(){return t.apply(n,o)}))};return Object.defineProperty(n,"length",{enumerable:!1,configurable:!0,writable:!1,value:t.length}),n},e.prototype.active=function(){return this._currentContext},e.prototype.bind=function(e,t){return void 0===e&&(e=this.active()),"function"==typeof t?this._bindFunction(e,t):t},e.prototype.disable=function(){return this._currentContext=ROOT_CONTEXT,this._enabled=!1,this},e.prototype.enable=function(){return this._enabled||(this._enabled=!0,this._currentContext=ROOT_CONTEXT),this},e.prototype.with=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var i=this._currentContext;this._currentContext=e||ROOT_CONTEXT;try{return t.call.apply(t,__spreadArray$1([r],__read$7(n),!1))}finally{this._currentContext=i}},e}(),__extends$8=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),WebTracerProvider=function(e){function t(t){void 0===t&&(t={});var r=e.call(this,t)||this;if(t.contextManager)throw"contextManager should be defined in register method not in constructor";if(t.propagator)throw"propagator should be defined in register method not in constructor";return r}return __extends$8(t,e),t.prototype.register=function(t){void 0===t&&(t={}),void 0===t.contextManager&&(t.contextManager=new StackContextManager),t.contextManager&&t.contextManager.enable(),e.prototype.register.call(this,t)},t}(BasicTracerProvider),PerformanceTimingNames,urlNormalizingAnchor;function getUrlNormalizingAnchor(){return urlNormalizingAnchor||(urlNormalizingAnchor=document.createElement("a")),urlNormalizingAnchor}function hasKey(e,t){return t in e}function addSpanNetworkEvent(e,t,r,n){var o=void 0,i=void 0;hasKey(r,t)&&"number"==typeof r[t]&&(o=r[t]);var a=n||PerformanceTimingNames.FETCH_START;if(hasKey(r,a)&&"number"==typeof r[a]&&(i=r[a]),void 0!==o&&void 0!==i&&o>=i)return e.addEvent(t,o),e}function addSpanNetworkEvents(e,t){addSpanNetworkEvent(e,PerformanceTimingNames.FETCH_START,t),addSpanNetworkEvent(e,PerformanceTimingNames.DOMAIN_LOOKUP_START,t),addSpanNetworkEvent(e,PerformanceTimingNames.DOMAIN_LOOKUP_END,t),addSpanNetworkEvent(e,PerformanceTimingNames.CONNECT_START,t),hasKey(t,"name")&&t.name.startsWith("https:")&&addSpanNetworkEvent(e,PerformanceTimingNames.SECURE_CONNECTION_START,t),addSpanNetworkEvent(e,PerformanceTimingNames.CONNECT_END,t),addSpanNetworkEvent(e,PerformanceTimingNames.REQUEST_START,t),addSpanNetworkEvent(e,PerformanceTimingNames.RESPONSE_START,t),addSpanNetworkEvent(e,PerformanceTimingNames.RESPONSE_END,t);var r=t[PerformanceTimingNames.ENCODED_BODY_SIZE];void 0!==r&&e.setAttribute(SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH,r);var n=t[PerformanceTimingNames.DECODED_BODY_SIZE];void 0!==n&&r!==n&&e.setAttribute(SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,n)}function sortResources(e){return e.slice().sort((function(e,t){var r=e[PerformanceTimingNames.FETCH_START],n=t[PerformanceTimingNames.FETCH_START];return r>n?1:r<n?-1:0}))}function getOrigin(){return"undefined"!=typeof location?location.origin:void 0}function getResource(e,t,r,n,o,i){void 0===o&&(o=new WeakSet);var a=parseUrl(e),s=filterResourcesForSpan(e=a.toString(),t,r,n,o,i);if(0===s.length)return{mainRequest:void 0};if(1===s.length)return{mainRequest:s[0]};var u=sortResources(s);if(a.origin!==getOrigin()&&u.length>1){var c=u[0],l=findMainRequest(u,c[PerformanceTimingNames.RESPONSE_END],r),p=c[PerformanceTimingNames.RESPONSE_END];return l[PerformanceTimingNames.FETCH_START]<p&&(l=c,c=void 0),{corsPreFlightRequest:c,mainRequest:l}}return{mainRequest:s[0]}}function findMainRequest(e,t,r){for(var n,o=hrTimeToNanoseconds(r),i=hrTimeToNanoseconds(timeInputToHrTime(t)),a=e[1],s=e.length,u=1;u<s;u++){var c=e[u],l=hrTimeToNanoseconds(timeInputToHrTime(c[PerformanceTimingNames.FETCH_START])),p=o-hrTimeToNanoseconds(timeInputToHrTime(c[PerformanceTimingNames.RESPONSE_END]));l>=i&&(!n||p<n)&&(n=p,a=c)}return a}function filterResourcesForSpan(e,t,r,n,o,i){var a=hrTimeToNanoseconds(t),s=hrTimeToNanoseconds(r),u=n.filter((function(t){var r=hrTimeToNanoseconds(timeInputToHrTime(t[PerformanceTimingNames.FETCH_START])),n=hrTimeToNanoseconds(timeInputToHrTime(t[PerformanceTimingNames.RESPONSE_END]));return t.initiatorType.toLowerCase()===(i||"xmlhttprequest")&&t.name===e&&r>=a&&n<=s}));return u.length>0&&(u=u.filter((function(e){return!o.has(e)}))),u}function parseUrl(e){if("function"==typeof URL)return new URL(e,"undefined"!=typeof document?document.baseURI:"undefined"!=typeof location?location.href:void 0);var t=getUrlNormalizingAnchor();return t.href=e,t}function getElementXPath(e,t){if(e.nodeType===Node.DOCUMENT_NODE)return"/";var r=getNodeValue(e,t);if(t&&r.indexOf("@id")>0)return r;var n="";return e.parentNode&&(n+=getElementXPath(e.parentNode,!1)),n+=r}function getNodeIndex(e){if(!e.parentNode)return 0;var t=[e.nodeType];e.nodeType===Node.CDATA_SECTION_NODE&&t.push(Node.TEXT_NODE);var r=Array.from(e.parentNode.childNodes);return(r=r.filter((function(r){var n=r.localName;return t.indexOf(r.nodeType)>=0&&n===e.localName}))).length>=1?r.indexOf(e)+1:0}function getNodeValue(e,t){var r=e.nodeType,n=getNodeIndex(e),o="";if(r===Node.ELEMENT_NODE){var i=e.getAttribute("id");if(t&&i)return'//*[@id="'+i+'"]';o=e.localName}else if(r===Node.TEXT_NODE||r===Node.CDATA_SECTION_NODE)o="text()";else{if(r!==Node.COMMENT_NODE)return"";o="comment()"}return o&&n>1?"/"+o+"["+n+"]":"/"+o}function shouldPropagateTraceHeaders(e,t){var r=t||[];return("string"==typeof r||r instanceof RegExp)&&(r=[r]),parseUrl(e).origin===getOrigin()||r.some((function(t){return urlMatches(e,t)}))}!function(e){e.CONNECT_END="connectEnd",e.CONNECT_START="connectStart",e.DECODED_BODY_SIZE="decodedBodySize",e.DOM_COMPLETE="domComplete",e.DOM_CONTENT_LOADED_EVENT_END="domContentLoadedEventEnd",e.DOM_CONTENT_LOADED_EVENT_START="domContentLoadedEventStart",e.DOM_INTERACTIVE="domInteractive",e.DOMAIN_LOOKUP_END="domainLookupEnd",e.DOMAIN_LOOKUP_START="domainLookupStart",e.ENCODED_BODY_SIZE="encodedBodySize",e.FETCH_START="fetchStart",e.LOAD_EVENT_END="loadEventEnd",e.LOAD_EVENT_START="loadEventStart",e.NAVIGATION_START="navigationStart",e.REDIRECT_END="redirectEnd",e.REDIRECT_START="redirectStart",e.REQUEST_START="requestStart",e.RESPONSE_END="responseEnd",e.RESPONSE_START="responseStart",e.SECURE_CONNECTION_START="secureConnectionStart",e.UNLOAD_EVENT_END="unloadEventEnd",e.UNLOAD_EVENT_START="unloadEventStart"}(PerformanceTimingNames||(PerformanceTimingNames={}));var NoopLogger=function(){function e(){}return e.prototype.emit=function(e){},e}(),NOOP_LOGGER=new NoopLogger,NoopLoggerProvider=function(){function e(){}return e.prototype.getLogger=function(e,t,r){return new NoopLogger},e}(),NOOP_LOGGER_PROVIDER=new NoopLoggerProvider,ProxyLogger=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.emit=function(e){this._getLogger().emit(e)},e.prototype._getLogger=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateLogger(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):NOOP_LOGGER},e}(),ProxyLoggerProvider=function(){function e(){}return e.prototype.getLogger=function(e,t,r){var n;return null!==(n=this.getDelegateLogger(e,t,r))&&void 0!==n?n:new ProxyLogger(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!==(e=this._delegate)&&void 0!==e?e:NOOP_LOGGER_PROVIDER},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateLogger=function(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getLogger(e,t,r)},e}(),_globalThis="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{},GLOBAL_LOGS_API_KEY=Symbol.for("io.opentelemetry.js.api.logs"),_global$1=_globalThis;function makeGetter(e,t,r){return function(n){return n===e?t:r}}var API_BACKWARDS_COMPATIBILITY_VERSION=1,LogsAPI=function(){function e(){this._proxyLoggerProvider=new ProxyLoggerProvider}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalLoggerProvider=function(e){return _global$1[GLOBAL_LOGS_API_KEY]?this.getLoggerProvider():(_global$1[GLOBAL_LOGS_API_KEY]=makeGetter(API_BACKWARDS_COMPATIBILITY_VERSION,e,NOOP_LOGGER_PROVIDER),this._proxyLoggerProvider.setDelegate(e),e)},e.prototype.getLoggerProvider=function(){var e,t;return null!==(t=null===(e=_global$1[GLOBAL_LOGS_API_KEY])||void 0===e?void 0:e.call(_global$1,API_BACKWARDS_COMPATIBILITY_VERSION))&&void 0!==t?t:this._proxyLoggerProvider},e.prototype.getLogger=function(e,t,r){return this.getLoggerProvider().getLogger(e,t,r)},e.prototype.disable=function(){delete _global$1[GLOBAL_LOGS_API_KEY],this._proxyLoggerProvider=new ProxyLoggerProvider},e}(),logs=LogsAPI.getInstance();function enableInstrumentations(e,t,r,n){for(var o=0,i=e.length;o<i;o++){var a=e[o];t&&a.setTracerProvider(t),r&&a.setMeterProvider(r),n&&a.setLoggerProvider&&a.setLoggerProvider(n),a.getConfig().enabled||a.enable()}}function disableInstrumentations(e){e.forEach((function(e){return e.disable()}))}function registerInstrumentations(e){var t,r,n=e.tracerProvider||trace.getTracerProvider(),o=e.meterProvider||metrics.getMeterProvider(),i=e.loggerProvider||logs.getLoggerProvider(),a=null!==(r=null===(t=e.instrumentations)||void 0===t?void 0:t.flat())&&void 0!==r?r:[];return enableInstrumentations(a,n,o,i),function(){disableInstrumentations(a)}}function isFunction(e){return"function"==typeof e}var logger=console.error.bind(console);function defineProperty(e,t,r){var n=!!e[t]&&e.propertyIsEnumerable(t);Object.defineProperty(e,t,{configurable:!0,enumerable:n,writable:!0,value:r})}function shimmer(e){e&&e.logger&&(isFunction(e.logger)?logger=e.logger:logger("new logger isn't a function, not replacing"))}function wrap(e,t,r){if(e&&e[t]){if(!r)return logger("no wrapper function"),void logger((new Error).stack);if(isFunction(e[t])&&isFunction(r)){var n=e[t],o=r(n,t);return defineProperty(o,"__original",n),defineProperty(o,"__unwrap",(function(){e[t]===o&&defineProperty(e,t,n)})),defineProperty(o,"__wrapped",!0),defineProperty(e,t,o),o}logger("original object and wrapper must be functions")}else logger("no original function "+t+" to wrap")}function massWrap(e,t,r){if(!e)return logger("must provide one or more modules to patch"),void logger((new Error).stack);Array.isArray(e)||(e=[e]),t&&Array.isArray(t)?e.forEach((function(e){t.forEach((function(t){wrap(e,t,r)}))})):logger("must provide one or more functions to wrap on modules")}function unwrap(e,t){return e&&e[t]?e[t].__unwrap?e[t].__unwrap():void logger("no original to unwrap to -- has "+t+" already been unwrapped?"):(logger("no function to unwrap."),void logger((new Error).stack))}function massUnwrap(e,t){if(!e)return logger("must provide one or more modules to patch"),void logger((new Error).stack);Array.isArray(e)||(e=[e]),t&&Array.isArray(t)?e.forEach((function(e){t.forEach((function(t){unwrap(e,t)}))})):logger("must provide one or more functions to unwrap on modules")}shimmer.wrap=wrap,shimmer.massWrap=massWrap,shimmer.unwrap=unwrap,shimmer.massUnwrap=massUnwrap;var shimmer_1=shimmer,__assign$1=window&&window.__assign||function(){return __assign$1=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign$1.apply(this,arguments)},InstrumentationAbstract=function(){function e(e,t,r){this.instrumentationName=e,this.instrumentationVersion=t,this._config={},this._wrap=shimmer_1.wrap,this._unwrap=shimmer_1.unwrap,this._massWrap=shimmer_1.massWrap,this._massUnwrap=shimmer_1.massUnwrap,this.setConfig(r),this._diag=diag.createComponentLogger({namespace:e}),this._tracer=trace.getTracer(e,t),this._meter=metrics.getMeter(e,t),this._logger=logs.getLogger(e,t),this._updateMetricInstruments()}return Object.defineProperty(e.prototype,"meter",{get:function(){return this._meter},enumerable:!1,configurable:!0}),e.prototype.setMeterProvider=function(e){this._meter=e.getMeter(this.instrumentationName,this.instrumentationVersion),this._updateMetricInstruments()},Object.defineProperty(e.prototype,"logger",{get:function(){return this._logger},enumerable:!1,configurable:!0}),e.prototype.setLoggerProvider=function(e){this._logger=e.getLogger(this.instrumentationName,this.instrumentationVersion)},e.prototype.getModuleDefinitions=function(){var e,t=null!==(e=this.init())&&void 0!==e?e:[];return Array.isArray(t)?t:[t]},e.prototype._updateMetricInstruments=function(){},e.prototype.getConfig=function(){return this._config},e.prototype.setConfig=function(e){this._config=__assign$1({enabled:!0},e)},e.prototype.setTracerProvider=function(e){this._tracer=e.getTracer(this.instrumentationName,this.instrumentationVersion)},Object.defineProperty(e.prototype,"tracer",{get:function(){return this._tracer},enumerable:!1,configurable:!0}),e.prototype._runSpanCustomizationHook=function(e,t,r,n){if(e)try{e(r,n)}catch(e){this._diag.error("Error running span customization hook due to exception in handler",{triggerName:t},e)}},e}(),__extends$7=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),InstrumentationBase=function(e){function t(t,r,n){var o=e.call(this,t,r,n)||this;return o._config.enabled&&o.enable(),o}return __extends$7(t,e),t}(InstrumentationAbstract);function safeExecuteInTheMiddle(e,t,r){var n,o;try{o=e()}catch(e){n=e}finally{if(t(n,o),n&&!r)throw n;return o}}function isWrapped(e){return"function"==typeof e&&"function"==typeof e.__original&&"function"==typeof e.__unwrap&&!0===e.__wrapped}window&&window.__awaiter,window&&window.__generator;var OTLPExporterBase=function(){function e(e){void 0===e&&(e={}),this._sendingPromises=[],this.shutdown=this.shutdown.bind(this),this._shutdownOnce=new BindOnceFuture(this._shutdown,this),this._concurrencyLimit="number"==typeof e.concurrencyLimit?e.concurrencyLimit:30}return e.prototype.export=function(e,t){this._shutdownOnce.isCalled?t({code:ExportResultCode.FAILED,error:new Error("Exporter has been shutdown")}):this._sendingPromises.length>=this._concurrencyLimit?t({code:ExportResultCode.FAILED,error:new Error("Concurrent export limit reached")}):this._export(e).then((function(){t({code:ExportResultCode.SUCCESS})})).catch((function(e){t({code:ExportResultCode.FAILED,error:e})}))},e.prototype._export=function(e){var t=this;return new Promise((function(r,n){try{diag.debug("items to be sent",e),t.send(e,r,n)}catch(e){n(e)}}))},e.prototype.shutdown=function(){return this._shutdownOnce.call()},e.prototype.forceFlush=function(){return Promise.all(this._sendingPromises).then((function(){}))},e.prototype._shutdown=function(){return diag.debug("shutdown started"),this.onShutdown(),this.forceFlush()},e}(),__extends$6=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),OTLPExporterError=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.name="OTLPExporterError",o.data=n,o.code=r,o}return __extends$6(t,e),t}(Error);function isExportRetryable(e){return[429,502,503,504].includes(e)}function parseRetryAfterToMills(e){if(null!=e){var t=Number.parseInt(e,10);if(Number.isInteger(t))return t>0?1e3*t:-1;var r=new Date(e).getTime()-Date.now();return r>=0?r:0}}var __read$6=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},XhrTransport=function(){function e(e){this._parameters=e}return e.prototype.send=function(e,t){var r=this;return new Promise((function(n){var o=new XMLHttpRequest;o.timeout=t,o.open("POST",r._parameters.url),Object.entries(r._parameters.headers).forEach((function(e){var t=__read$6(e,2),r=t[0],n=t[1];o.setRequestHeader(r,n)})),o.ontimeout=function(e){n({status:"failure",error:new Error("XHR request timed out")})},o.onreadystatechange=function(){o.status>=200&&o.status<=299?(diag.debug("XHR success"),n({status:"success"})):o.status&&isExportRetryable(o.status)?n({status:"retryable",retryInMillis:parseRetryAfterToMills(o.getResponseHeader("Retry-After"))}):0!==o.status&&n({status:"failure",error:new Error("XHR request failed with non-retryable status")})},o.onabort=function(){n({status:"failure",error:new Error("XHR request aborted")})},o.onerror=function(){n({status:"failure",error:new Error("XHR request errored")})},o.send(new Blob([e],{type:r._parameters.headers["Content-Type"]}))}))},e.prototype.shutdown=function(){},e}();function createXhrTransport(e){return new XhrTransport(e)}var SendBeaconTransport=function(){function e(e){this._params=e}return e.prototype.send=function(e){var t=this;return new Promise((function(r){navigator.sendBeacon(t._params.url,new Blob([e],{type:t._params.blobType}))?(diag.debug("SendBeacon success"),r({status:"success"})):r({status:"failure",error:new Error("SendBeacon failed")})}))},e.prototype.shutdown=function(){},e}();function createSendBeaconTransport(e){return new SendBeaconTransport(e)}var __awaiter$1=window&&window.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))},__generator$1=window&&window.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},MAX_ATTEMPTS=5,INITIAL_BACKOFF=1e3,MAX_BACKOFF=5e3,BACKOFF_MULTIPLIER=1.5,JITTER=.2;function getJitter(){return Math.random()*(2*JITTER)-JITTER}var RetryingTransport=function(){function e(e){this._transport=e}return e.prototype.retry=function(e,t,r){var n=this;return new Promise((function(o,i){setTimeout((function(){n._transport.send(e,t).then(o,i)}),r)}))},e.prototype.send=function(e,t){var r;return __awaiter$1(this,void 0,void 0,(function(){var n,o,i,a,s,u,c;return __generator$1(this,(function(l){switch(l.label){case 0:return n=Date.now()+t,[4,this._transport.send(e,t)];case 1:o=l.sent(),i=MAX_ATTEMPTS,a=INITIAL_BACKOFF,l.label=2;case 2:return"retryable"===o.status&&i>0?(i--,s=Math.max(Math.min(a,MAX_BACKOFF)+getJitter(),0),a*=BACKOFF_MULTIPLIER,u=null!==(r=o.retryInMillis)&&void 0!==r?r:s,c=n-Date.now(),u>c?[2,o]:[4,this.retry(e,c,u)]):[3,4];case 3:return o=l.sent(),[3,2];case 4:return[2,o]}}))}))},e.prototype.shutdown=function(){return this._transport.shutdown()},e}();function createRetryingTransport(e){return new RetryingTransport(e.transport)}function validateTimeoutMillis(e){if(!Number.isNaN(e)&&Number.isFinite(e)&&e>0)return e;throw new Error("Configuration: timeoutMillis is invalid, expected number greater than 0 (actual: '"+e+"')")}function mergeOtlpSharedConfigurationWithDefaults(e,t,r){var n,o,i,a,s,u;return{timeoutMillis:validateTimeoutMillis(null!==(o=null!==(n=e.timeoutMillis)&&void 0!==n?n:t.timeoutMillis)&&void 0!==o?o:r.timeoutMillis),concurrencyLimit:null!==(a=null!==(i=e.concurrencyLimit)&&void 0!==i?i:t.concurrencyLimit)&&void 0!==a?a:r.concurrencyLimit,compression:null!==(u=null!==(s=e.compression)&&void 0!==s?s:t.compression)&&void 0!==u?u:r.compression}}function getSharedConfigurationDefaults(){return{timeoutMillis:1e4,concurrencyLimit:30,compression:"none"}}var __read$5=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function validateAndNormalizeHeaders(e){void 0===e&&(e={});var t={};return Object.entries(e).forEach((function(e){var r=__read$5(e,2),n=r[0],o=r[1];void 0!==o?t[n]=String(o):diag.warn('Header "'+n+'" has invalid value ('+o+") and will be ignored")})),t}var __assign=window&&window.__assign||function(){return __assign=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};function mergeHeaders(e,t,r){var n=__assign({},r),o={};return null!=t&&Object.assign(o,t),null!=e&&Object.assign(o,e),Object.assign(o,n)}function validateUserProvidedUrl(e){if(null!=e)try{return new URL(e),e}catch(t){throw new Error("Configuration: Could not parse user-provided export URL: '"+e+"'")}}function mergeOtlpHttpConfigurationWithDefaults(e,t,r){var n,o;return __assign(__assign({},mergeOtlpSharedConfigurationWithDefaults(e,t,r)),{headers:mergeHeaders(validateAndNormalizeHeaders(e.headers),t.headers,r.headers),url:null!==(o=null!==(n=validateUserProvidedUrl(e.url))&&void 0!==n?n:t.url)&&void 0!==o?o:r.url})}function getHttpConfigurationDefaults(e,t){return __assign(__assign({},getSharedConfigurationDefaults()),{headers:e,url:"http://localhost:4318/"+t})}var __extends$5=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),OTLPExporterBrowserBase=function(e){function t(t,r,n,o){void 0===t&&(t={});var i=e.call(this,t)||this;i._serializer=r;var a=!!t.headers||"function"!=typeof navigator.sendBeacon,s=mergeOtlpHttpConfigurationWithDefaults({url:t.url,timeoutMillis:t.timeoutMillis,headers:t.headers,concurrencyLimit:t.concurrencyLimit},{},getHttpConfigurationDefaults(n,o));return i._timeoutMillis=s.timeoutMillis,i._concurrencyLimit=s.concurrencyLimit,i._transport=a?createRetryingTransport({transport:createXhrTransport({headers:s.headers,url:s.url})}):createSendBeaconTransport({url:s.url,blobType:s.headers["Content-Type"]}),i}return __extends$5(t,e),t.prototype.onShutdown=function(){},t.prototype.send=function(e,t,r){var n=this;if(this._shutdownOnce.isCalled)diag.debug("Shutdown already started. Cannot send objects");else{var o=this._serializer.serializeRequest(e);if(null!=o){var i=this._transport.send(o,this._timeoutMillis).then((function(e){"success"===e.status?t():"failure"===e.status&&e.error?r(e.error):"retryable"===e.status?r(new OTLPExporterError("Export failed with retryable status")):r(new OTLPExporterError("Export failed with unknown error"))}),r);this._sendingPromises.push(i);var a=function(){var e=n._sendingPromises.indexOf(i);n._sendingPromises.splice(e,1)};i.then(a,a)}else r(new Error("Could not serialize message"))}},t}(OTLPExporterBase);function hrTimeToNanos(e){var t=BigInt(1e9);return BigInt(e[0])*t+BigInt(e[1])}function toLongBits(e){return{low:Number(BigInt.asUintN(32,e)),high:Number(BigInt.asUintN(32,e>>BigInt(32)))}}function encodeAsLongBits(e){return toLongBits(hrTimeToNanos(e))}function encodeAsString(e){return hrTimeToNanos(e).toString()}var encodeTimestamp="undefined"!=typeof BigInt?encodeAsString:hrTimeToNanoseconds;function identity(e){return e}function optionalHexToBinary(e){if(void 0!==e)return hexToBinary(e)}var DEFAULT_ENCODER={encodeHrTime:encodeAsLongBits,encodeSpanContext:hexToBinary,encodeOptionalSpanContext:optionalHexToBinary};function getOtlpEncoder(e){var t,r;if(void 0===e)return DEFAULT_ENCODER;var n=null===(t=e.useLongBits)||void 0===t||t,o=null!==(r=e.useHex)&&void 0!==r&&r;return{encodeHrTime:n?encodeAsLongBits:encodeTimestamp,encodeSpanContext:o?identity:hexToBinary,encodeOptionalSpanContext:o?identity:optionalHexToBinary}}var __read$4=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function createInstrumentationScope(e){return{name:e.name,version:e.version}}function toAttributes(e){return Object.keys(e).map((function(t){return toKeyValue(t,e[t])}))}function toKeyValue(e,t){return{key:e,value:toAnyValue(t)}}function toAnyValue(e){var t=typeof e;return"string"===t?{stringValue:e}:"number"===t?Number.isInteger(e)?{intValue:e}:{doubleValue:e}:"boolean"===t?{boolValue:e}:e instanceof Uint8Array?{bytesValue:e}:Array.isArray(e)?{arrayValue:{values:e.map(toAnyValue)}}:"object"===t&&null!=e?{kvlistValue:{values:Object.entries(e).map((function(e){var t=__read$4(e,2);return toKeyValue(t[0],t[1])}))}}:{}}function sdkSpanToOtlpSpan(e,t){var r,n=e.spanContext(),o=e.status;return{traceId:t.encodeSpanContext(n.traceId),spanId:t.encodeSpanContext(n.spanId),parentSpanId:t.encodeOptionalSpanContext(e.parentSpanId),traceState:null===(r=n.traceState)||void 0===r?void 0:r.serialize(),name:e.name,kind:null==e.kind?0:e.kind+1,startTimeUnixNano:t.encodeHrTime(e.startTime),endTimeUnixNano:t.encodeHrTime(e.endTime),attributes:toAttributes(e.attributes),droppedAttributesCount:e.droppedAttributesCount,events:e.events.map((function(e){return toOtlpSpanEvent(e,t)})),droppedEventsCount:e.droppedEventsCount,status:{code:o.code,message:o.message},links:e.links.map((function(e){return toOtlpLink(e,t)})),droppedLinksCount:e.droppedLinksCount}}function toOtlpLink(e,t){var r;return{attributes:e.attributes?toAttributes(e.attributes):[],spanId:t.encodeSpanContext(e.context.spanId),traceId:t.encodeSpanContext(e.context.traceId),traceState:null===(r=e.context.traceState)||void 0===r?void 0:r.serialize(),droppedAttributesCount:e.droppedAttributesCount||0}}function toOtlpSpanEvent(e,t){return{attributes:e.attributes?toAttributes(e.attributes):[],name:e.name,timeUnixNano:t.encodeHrTime(e.time),droppedAttributesCount:e.droppedAttributesCount||0}}function createResource(e){return{attributes:toAttributes(e.attributes),droppedAttributesCount:0}}var __values$2=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},__read$3=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function createExportTraceServiceRequest(e,t){return{resourceSpans:spanRecordsToResourceSpans(e,getOtlpEncoder(t))}}function createResourceMap(e){var t,r,n=new Map;try{for(var o=__values$2(e),i=o.next();!i.done;i=o.next()){var a=i.value,s=n.get(a.resource);s||(s=new Map,n.set(a.resource,s));var u=a.instrumentationLibrary.name+"@"+(a.instrumentationLibrary.version||"")+":"+(a.instrumentationLibrary.schemaUrl||""),c=s.get(u);c||(c=[],s.set(u,c)),c.push(a)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return n}function spanRecordsToResourceSpans(e,t){for(var r=[],n=createResourceMap(e).entries(),o=n.next();!o.done;){for(var i=__read$3(o.value,2),a=i[0],s=[],u=i[1].values(),c=u.next();!c.done;){var l=c.value;if(l.length>0){var p=l.map((function(e){return sdkSpanToOtlpSpan(e,t)}));s.push({scope:createInstrumentationScope(l[0].instrumentationLibrary),spans:p,schemaUrl:l[0].instrumentationLibrary.schemaUrl})}c=u.next()}var f={resource:createResource(a),scopeSpans:s,schemaUrl:void 0};r.push(f),o=n.next()}return r}var indexMinimal={},minimal$1={},aspromise=asPromise;function asPromise(e,t){for(var r=new Array(arguments.length-1),n=0,o=2,i=!0;o<arguments.length;)r[n++]=arguments[o++];return new Promise((function(o,a){r[n]=function(e){if(i)if(i=!1,e)a(e);else{for(var t=new Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];o.apply(null,t)}};try{e.apply(t||null,r)}catch(e){i&&(i=!1,a(e))}}))}var base64$1={};!function(e){var t=e;t.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};for(var r=new Array(64),n=new Array(123),o=0;o<64;)n[r[o]=o<26?o+65:o<52?o+71:o<62?o-4:o-59|43]=o++;t.encode=function(e,t,n){for(var o,i=null,a=[],s=0,u=0;t<n;){var c=e[t++];switch(u){case 0:a[s++]=r[c>>2],o=(3&c)<<4,u=1;break;case 1:a[s++]=r[o|c>>4],o=(15&c)<<2,u=2;break;case 2:a[s++]=r[o|c>>6],a[s++]=r[63&c],u=0}s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,a)),s=0)}return u&&(a[s++]=r[o],a[s++]=61,1===u&&(a[s++]=61)),i?(s&&i.push(String.fromCharCode.apply(String,a.slice(0,s))),i.join("")):String.fromCharCode.apply(String,a.slice(0,s))};var i="invalid encoding";t.decode=function(e,t,r){for(var o,a=r,s=0,u=0;u<e.length;){var c=e.charCodeAt(u++);if(61===c&&s>1)break;if(void 0===(c=n[c]))throw Error(i);switch(s){case 0:o=c,s=1;break;case 1:t[r++]=o<<2|(48&c)>>4,o=c,s=2;break;case 2:t[r++]=(15&o)<<4|(60&c)>>2,o=c,s=3;break;case 3:t[r++]=(3&o)<<6|c,s=0}}if(1===s)throw Error(i);return r-a},t.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}}(base64$1);var eventemitter=EventEmitter;function EventEmitter(){this._listeners={}}EventEmitter.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},EventEmitter.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],n=0;n<r.length;)r[n].fn===t?r.splice(n,1):++n;return this},EventEmitter.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<t.length;)t[n].fn.apply(t[n++].ctx,r)}return this};var float=factory(factory);function factory(e){return"undefined"!=typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),n=128===r[3];function o(e,n,o){t[0]=e,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3]}function i(e,n,o){t[0]=e,n[o]=r[3],n[o+1]=r[2],n[o+2]=r[1],n[o+3]=r[0]}function a(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],t[0]}function s(e,n){return r[3]=e[n],r[2]=e[n+1],r[1]=e[n+2],r[0]=e[n+3],t[0]}e.writeFloatLE=n?o:i,e.writeFloatBE=n?i:o,e.readFloatLE=n?a:s,e.readFloatBE=n?s:a}():function(){function t(e,t,r,n){var o=t<0?1:0;if(o&&(t=-t),0===t)e(1/t>0?0:2147483648,r,n);else if(isNaN(t))e(2143289344,r,n);else if(t>34028234663852886e22)e((o<<31|2139095040)>>>0,r,n);else if(t<11754943508222875e-54)e((o<<31|Math.round(t/1401298464324817e-60))>>>0,r,n);else{var i=Math.floor(Math.log(t)/Math.LN2);e((o<<31|i+127<<23|8388607&Math.round(t*Math.pow(2,-i)*8388608))>>>0,r,n)}}function r(e,t,r){var n=e(t,r),o=2*(n>>31)+1,i=n>>>23&255,a=8388607&n;return 255===i?a?NaN:o*(1/0):0===i?1401298464324817e-60*o*a:o*Math.pow(2,i-150)*(a+8388608)}e.writeFloatLE=t.bind(null,writeUintLE),e.writeFloatBE=t.bind(null,writeUintBE),e.readFloatLE=r.bind(null,readUintLE),e.readFloatBE=r.bind(null,readUintBE)}(),"undefined"!=typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),n=128===r[7];function o(e,n,o){t[0]=e,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3],n[o+4]=r[4],n[o+5]=r[5],n[o+6]=r[6],n[o+7]=r[7]}function i(e,n,o){t[0]=e,n[o]=r[7],n[o+1]=r[6],n[o+2]=r[5],n[o+3]=r[4],n[o+4]=r[3],n[o+5]=r[2],n[o+6]=r[1],n[o+7]=r[0]}function a(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],r[4]=e[n+4],r[5]=e[n+5],r[6]=e[n+6],r[7]=e[n+7],t[0]}function s(e,n){return r[7]=e[n],r[6]=e[n+1],r[5]=e[n+2],r[4]=e[n+3],r[3]=e[n+4],r[2]=e[n+5],r[1]=e[n+6],r[0]=e[n+7],t[0]}e.writeDoubleLE=n?o:i,e.writeDoubleBE=n?i:o,e.readDoubleLE=n?a:s,e.readDoubleBE=n?s:a}():function(){function t(e,t,r,n,o,i){var a=n<0?1:0;if(a&&(n=-n),0===n)e(0,o,i+t),e(1/n>0?0:2147483648,o,i+r);else if(isNaN(n))e(0,o,i+t),e(2146959360,o,i+r);else if(n>17976931348623157e292)e(0,o,i+t),e((a<<31|2146435072)>>>0,o,i+r);else{var s;if(n<22250738585072014e-324)e((s=n/5e-324)>>>0,o,i+t),e((a<<31|s/4294967296)>>>0,o,i+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),e(4503599627370496*(s=n*Math.pow(2,-u))>>>0,o,i+t),e((a<<31|u+1023<<20|1048576*s&1048575)>>>0,o,i+r)}}}function r(e,t,r,n,o){var i=e(n,o+t),a=e(n,o+r),s=2*(a>>31)+1,u=a>>>20&2047,c=4294967296*(1048575&a)+i;return 2047===u?c?NaN:s*(1/0):0===u?5e-324*s*c:s*Math.pow(2,u-1075)*(c+4503599627370496)}e.writeDoubleLE=t.bind(null,writeUintLE,0,4),e.writeDoubleBE=t.bind(null,writeUintBE,4,0),e.readDoubleLE=r.bind(null,readUintLE,0,4),e.readDoubleBE=r.bind(null,readUintBE,4,0)}(),e}function writeUintLE(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function writeUintBE(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function readUintLE(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function readUintBE(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}var inquire_1=inquire;function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}var utf8$2={};!function(e){var t=e;t.length=function(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:55296==(64512&r)&&56320==(64512&e.charCodeAt(n+1))?(++n,t+=4):t+=3;return t},t.read=function(e,t,r){if(r-t<1)return"";for(var n,o=null,i=[],a=0;t<r;)(n=e[t++])<128?i[a++]=n:n>191&&n<224?i[a++]=(31&n)<<6|63&e[t++]:n>239&&n<365?(n=((7&n)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,i[a++]=55296+(n>>10),i[a++]=56320+(1023&n)):i[a++]=(15&n)<<12|(63&e[t++])<<6|63&e[t++],a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),a=0);return o?(a&&o.push(String.fromCharCode.apply(String,i.slice(0,a))),o.join("")):String.fromCharCode.apply(String,i.slice(0,a))},t.write=function(e,t,r){for(var n,o,i=r,a=0;a<e.length;++a)(n=e.charCodeAt(a))<128?t[r++]=n:n<2048?(t[r++]=n>>6|192,t[r++]=63&n|128):55296==(64512&n)&&56320==(64512&(o=e.charCodeAt(a+1)))?(n=65536+((1023&n)<<10)+(1023&o),++a,t[r++]=n>>18|240,t[r++]=n>>12&63|128,t[r++]=n>>6&63|128,t[r++]=63&n|128):(t[r++]=n>>12|224,t[r++]=n>>6&63|128,t[r++]=63&n|128);return r-i}}(utf8$2);var pool_1=pool,longbits,hasRequiredLongbits,hasRequiredMinimal;function pool(e,t,r){var n=r||8192,o=n>>>1,i=null,a=n;return function(r){if(r<1||r>o)return e(r);a+r>n&&(i=e(n),a=0);var s=t.call(i,a,a+=r);return 7&a&&(a=1+(7|a)),s}}function requireLongbits(){if(hasRequiredLongbits)return longbits;hasRequiredLongbits=1,longbits=t;var e=requireMinimal();function t(e,t){this.lo=e>>>0,this.hi=t>>>0}var r=t.zero=new t(0,0);r.toNumber=function(){return 0},r.zzEncode=r.zzDecode=function(){return this},r.length=function(){return 1};var n=t.zeroHash="\0\0\0\0\0\0\0\0";t.fromNumber=function(e){if(0===e)return r;var n=e<0;n&&(e=-e);var o=e>>>0,i=(e-o)/4294967296>>>0;return n&&(i=~i>>>0,o=~o>>>0,++o>4294967295&&(o=0,++i>4294967295&&(i=0))),new t(o,i)},t.from=function(n){if("number"==typeof n)return t.fromNumber(n);if(e.isString(n)){if(!e.Long)return t.fromNumber(parseInt(n,10));n=e.Long.fromString(n)}return n.low||n.high?new t(n.low>>>0,n.high>>>0):r},t.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=1+~this.lo>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+4294967296*r)}return this.lo+4294967296*this.hi},t.prototype.toLong=function(t){return e.Long?new e.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var o=String.prototype.charCodeAt;return t.fromHash=function(e){return e===n?r:new t((o.call(e,0)|o.call(e,1)<<8|o.call(e,2)<<16|o.call(e,3)<<24)>>>0,(o.call(e,4)|o.call(e,5)<<8|o.call(e,6)<<16|o.call(e,7)<<24)>>>0)},t.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},t.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},t.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},t.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10},longbits}function requireMinimal(){return hasRequiredMinimal||(hasRequiredMinimal=1,function(e){var t=e;function r(e,t,r){for(var n=Object.keys(t),o=0;o<n.length;++o)void 0!==e[n[o]]&&r||(e[n[o]]=t[n[o]]);return e}function n(e){function t(e,n){if(!(this instanceof t))return new t(e,n);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),n&&r(this,n)}return t.prototype=Object.create(Error.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return e},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),t}t.asPromise=aspromise,t.base64=base64$1,t.EventEmitter=eventemitter,t.float=float,t.inquire=inquire_1,t.utf8=utf8$2,t.pool=pool_1,t.LongBits=requireLongbits(),t.isNode=Boolean(void 0!==commonjsGlobal&&commonjsGlobal&&commonjsGlobal.process&&commonjsGlobal.process.versions&&commonjsGlobal.process.versions.node),t.global=t.isNode&&commonjsGlobal||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||commonjsGlobal,t.emptyArray=Object.freeze?Object.freeze([]):[],t.emptyObject=Object.freeze?Object.freeze({}):{},t.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},t.isString=function(e){return"string"==typeof e||e instanceof String},t.isObject=function(e){return e&&"object"==typeof e},t.isset=t.isSet=function(e,t){var r=e[t];return!(null==r||!e.hasOwnProperty(t))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},t.Buffer=function(){try{var e=t.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch(e){return null}}(),t._Buffer_from=null,t._Buffer_allocUnsafe=null,t.newBuffer=function(e){return"number"==typeof e?t.Buffer?t._Buffer_allocUnsafe(e):new t.Array(e):t.Buffer?t._Buffer_from(e):"undefined"==typeof Uint8Array?e:new Uint8Array(e)},t.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,t.Long=t.global.dcodeIO&&t.global.dcodeIO.Long||t.global.Long||t.inquire("long"),t.key2Re=/^true|false|0|1$/,t.key32Re=/^-?(?:0|[1-9][0-9]*)$/,t.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,t.longToHash=function(e){return e?t.LongBits.from(e).toHash():t.LongBits.zeroHash},t.longFromHash=function(e,r){var n=t.LongBits.fromHash(e);return t.Long?t.Long.fromBits(n.lo,n.hi,r):n.toNumber(Boolean(r))},t.merge=r,t.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},t.newError=n,t.ProtocolError=n("ProtocolError"),t.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},t.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},t.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},t._configure=function(){var e=t.Buffer;e?(t._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},t._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}):t._Buffer_from=t._Buffer_allocUnsafe=null}}(minimal$1)),minimal$1}var writer=Writer$1,util$4=requireMinimal(),BufferWriter$1,LongBits$1=util$4.LongBits,base64=util$4.base64,utf8$1=util$4.utf8;function Op(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function noop(){}function State(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function Writer$1(){this.len=0,this.head=new Op(noop,0,0),this.tail=this.head,this.states=null}var create$1=function(){return util$4.Buffer?function(){return(Writer$1.create=function(){return new BufferWriter$1})()}:function(){return new Writer$1}};function writeByte(e,t,r){t[r]=255&e}function writeVarint32(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e}function VarintOp(e,t){this.len=e,this.next=void 0,this.val=t}function writeVarint64(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function writeFixed32(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}Writer$1.create=create$1(),Writer$1.alloc=function(e){return new util$4.Array(e)},util$4.Array!==Array&&(Writer$1.alloc=util$4.pool(Writer$1.alloc,util$4.Array.prototype.subarray)),Writer$1.prototype._push=function(e,t,r){return this.tail=this.tail.next=new Op(e,t,r),this.len+=t,this},VarintOp.prototype=Object.create(Op.prototype),VarintOp.prototype.fn=writeVarint32,Writer$1.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new VarintOp((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this},Writer$1.prototype.int32=function(e){return e<0?this._push(writeVarint64,10,LongBits$1.fromNumber(e)):this.uint32(e)},Writer$1.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},Writer$1.prototype.uint64=function(e){var t=LongBits$1.from(e);return this._push(writeVarint64,t.length(),t)},Writer$1.prototype.int64=Writer$1.prototype.uint64,Writer$1.prototype.sint64=function(e){var t=LongBits$1.from(e).zzEncode();return this._push(writeVarint64,t.length(),t)},Writer$1.prototype.bool=function(e){return this._push(writeByte,1,e?1:0)},Writer$1.prototype.fixed32=function(e){return this._push(writeFixed32,4,e>>>0)},Writer$1.prototype.sfixed32=Writer$1.prototype.fixed32,Writer$1.prototype.fixed64=function(e){var t=LongBits$1.from(e);return this._push(writeFixed32,4,t.lo)._push(writeFixed32,4,t.hi)},Writer$1.prototype.sfixed64=Writer$1.prototype.fixed64,Writer$1.prototype.float=function(e){return this._push(util$4.float.writeFloatLE,4,e)},Writer$1.prototype.double=function(e){return this._push(util$4.float.writeDoubleLE,8,e)};var writeBytes=util$4.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var n=0;n<e.length;++n)t[r+n]=e[n]};Writer$1.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(writeByte,1,0);if(util$4.isString(e)){var r=Writer$1.alloc(t=base64.length(e));base64.decode(e,r,0),e=r}return this.uint32(t)._push(writeBytes,t,e)},Writer$1.prototype.string=function(e){var t=utf8$1.length(e);return t?this.uint32(t)._push(utf8$1.write,t,e):this._push(writeByte,1,0)},Writer$1.prototype.fork=function(){return this.states=new State(this),this.head=this.tail=new Op(noop,0,0),this.len=0,this},Writer$1.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new Op(noop,0,0),this.len=0),this},Writer$1.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},Writer$1.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},Writer$1._configure=function(e){BufferWriter$1=e,Writer$1.create=create$1(),BufferWriter$1._configure()};var writer_buffer=BufferWriter,Writer=writer;(BufferWriter.prototype=Object.create(Writer.prototype)).constructor=BufferWriter;var util$3=requireMinimal();function BufferWriter(){Writer.call(this)}function writeStringBuffer(e,t,r){e.length<40?util$3.utf8.write(e,t,r):t.utf8Write?t.utf8Write(e,r):t.write(e,r)}BufferWriter._configure=function(){BufferWriter.alloc=util$3._Buffer_allocUnsafe,BufferWriter.writeBytesBuffer=util$3.Buffer&&util$3.Buffer.prototype instanceof Uint8Array&&"set"===util$3.Buffer.prototype.set.name?function(e,t,r){t.set(e,r)}:function(e,t,r){if(e.copy)e.copy(t,r,0,e.length);else for(var n=0;n<e.length;)t[r++]=e[n++]}},BufferWriter.prototype.bytes=function(e){util$3.isString(e)&&(e=util$3._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(BufferWriter.writeBytesBuffer,t,e),this},BufferWriter.prototype.string=function(e){var t=util$3.Buffer.byteLength(e);return this.uint32(t),t&&this._push(writeStringBuffer,t,e),this},BufferWriter._configure();var reader=Reader$1,util$2=requireMinimal(),BufferReader$1,LongBits=util$2.LongBits,utf8=util$2.utf8;function indexOutOfRange(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function Reader$1(e){this.buf=e,this.pos=0,this.len=e.length}var create_array="undefined"!=typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new Reader$1(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new Reader$1(e);throw Error("illegal buffer")},create=function(){return util$2.Buffer?function(e){return(Reader$1.create=function(e){return util$2.Buffer.isBuffer(e)?new BufferReader$1(e):create_array(e)})(e)}:create_array},value;function readLongVarint(){var e=new LongBits(0,0),t=0;if(!(this.len-this.pos>4)){for(;t<3;++t){if(this.pos>=this.len)throw indexOutOfRange(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;if(t=0,this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw indexOutOfRange(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function readFixed32_end(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function readFixed64(){if(this.pos+8>this.len)throw indexOutOfRange(this,8);return new LongBits(readFixed32_end(this.buf,this.pos+=4),readFixed32_end(this.buf,this.pos+=4))}Reader$1.create=create(),Reader$1.prototype._slice=util$2.Array.prototype.subarray||util$2.Array.prototype.slice,Reader$1.prototype.uint32=(value=4294967295,function(){if(value=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return value;if(value=(value|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return value;if((this.pos+=5)>this.len)throw this.pos=this.len,indexOutOfRange(this,10);return value}),Reader$1.prototype.int32=function(){return 0|this.uint32()},Reader$1.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)|0},Reader$1.prototype.bool=function(){return 0!==this.uint32()},Reader$1.prototype.fixed32=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);return readFixed32_end(this.buf,this.pos+=4)},Reader$1.prototype.sfixed32=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);return 0|readFixed32_end(this.buf,this.pos+=4)},Reader$1.prototype.float=function(){if(this.pos+4>this.len)throw indexOutOfRange(this,4);var e=util$2.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},Reader$1.prototype.double=function(){if(this.pos+8>this.len)throw indexOutOfRange(this,4);var e=util$2.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},Reader$1.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw indexOutOfRange(this,e);if(this.pos+=e,Array.isArray(this.buf))return this.buf.slice(t,r);if(t===r){var n=util$2.Buffer;return n?n.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,t,r)},Reader$1.prototype.string=function(){var e=this.bytes();return utf8.read(e,0,e.length)},Reader$1.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw indexOutOfRange(this,e);this.pos+=e}else do{if(this.pos>=this.len)throw indexOutOfRange(this)}while(128&this.buf[this.pos++]);return this},Reader$1.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},Reader$1._configure=function(e){BufferReader$1=e,Reader$1.create=create(),BufferReader$1._configure();var t=util$2.Long?"toLong":"toNumber";util$2.merge(Reader$1.prototype,{int64:function(){return readLongVarint.call(this)[t](!1)},uint64:function(){return readLongVarint.call(this)[t](!0)},sint64:function(){return readLongVarint.call(this).zzDecode()[t](!1)},fixed64:function(){return readFixed64.call(this)[t](!0)},sfixed64:function(){return readFixed64.call(this)[t](!1)}})};var reader_buffer=BufferReader,Reader=reader;(BufferReader.prototype=Object.create(Reader.prototype)).constructor=BufferReader;var util$1=requireMinimal();function BufferReader(e){Reader.call(this,e)}BufferReader._configure=function(){util$1.Buffer&&(BufferReader.prototype._slice=util$1.Buffer.prototype.slice)},BufferReader.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},BufferReader._configure();var rpc={},service=Service,util=requireMinimal();function Service(e,t,r){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");util.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=Boolean(t),this.responseDelimited=Boolean(r)}(Service.prototype=Object.create(util.EventEmitter.prototype)).constructor=Service,Service.prototype.rpcCall=function e(t,r,n,o,i){if(!o)throw TypeError("request must be specified");var a=this;if(!i)return util.asPromise(e,a,t,r,n,o);if(a.rpcImpl)try{return a.rpcImpl(t,r[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),(function(e,r){if(e)return a.emit("error",e,t),i(e);if(null!==r){if(!(r instanceof n))try{r=n[a.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return a.emit("error",e,t),i(e)}return a.emit("data",r,t),i(null,r)}a.end(!0)}))}catch(e){return a.emit("error",e,t),void setTimeout((function(){i(e)}),0)}else setTimeout((function(){i(Error("already ended"))}),0)},Service.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this},function(e){e.Service=service}(rpc);var roots={};!function(e){var t=e;function r(){t.util._configure(),t.Writer._configure(t.BufferWriter),t.Reader._configure(t.BufferReader)}t.build="minimal",t.Writer=writer,t.BufferWriter=writer_buffer,t.Reader=reader,t.BufferReader=reader_buffer,t.util=requireMinimal(),t.rpc=rpc,t.roots=roots,t.configure=r,r()}(indexMinimal);var minimal=indexMinimal,$protobuf=minimal,$Reader=$protobuf.Reader,$Writer=$protobuf.Writer,$util=$protobuf.util,$root=$protobuf.roots.default||($protobuf.roots.default={}),v1,common,resource,collector,proto,opentelemetry;$root.opentelemetry=(opentelemetry={},opentelemetry.proto=((proto={}).common=((common={}).v1=((v1={}).AnyValue=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.stringValue=null,e.prototype.boolValue=null,e.prototype.intValue=null,e.prototype.doubleValue=null,e.prototype.arrayValue=null,e.prototype.kvlistValue=null,e.prototype.bytesValue=null,Object.defineProperty(e.prototype,"value",{get:$util.oneOfGetter(t=["stringValue","boolValue","intValue","doubleValue","arrayValue","kvlistValue","bytesValue"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.stringValue&&Object.hasOwnProperty.call(e,"stringValue")&&t.uint32(10).string(e.stringValue),null!=e.boolValue&&Object.hasOwnProperty.call(e,"boolValue")&&t.uint32(16).bool(e.boolValue),null!=e.intValue&&Object.hasOwnProperty.call(e,"intValue")&&t.uint32(24).int64(e.intValue),null!=e.doubleValue&&Object.hasOwnProperty.call(e,"doubleValue")&&t.uint32(33).double(e.doubleValue),null!=e.arrayValue&&Object.hasOwnProperty.call(e,"arrayValue")&&$root.opentelemetry.proto.common.v1.ArrayValue.encode(e.arrayValue,t.uint32(42).fork()).ldelim(),null!=e.kvlistValue&&Object.hasOwnProperty.call(e,"kvlistValue")&&$root.opentelemetry.proto.common.v1.KeyValueList.encode(e.kvlistValue,t.uint32(50).fork()).ldelim(),null!=e.bytesValue&&Object.hasOwnProperty.call(e,"bytesValue")&&t.uint32(58).bytes(e.bytesValue),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.common.v1.AnyValue;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.stringValue=e.string();break;case 2:n.boolValue=e.bool();break;case 3:n.intValue=e.int64();break;case 4:n.doubleValue=e.double();break;case 5:n.arrayValue=$root.opentelemetry.proto.common.v1.ArrayValue.decode(e,e.uint32());break;case 6:n.kvlistValue=$root.opentelemetry.proto.common.v1.KeyValueList.decode(e,e.uint32());break;case 7:n.bytesValue=e.bytes();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";var t={};if(null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(t.value=1,!$util.isString(e.stringValue)))return"stringValue: string expected";if(null!=e.boolValue&&e.hasOwnProperty("boolValue")){if(1===t.value)return"value: multiple values";if(t.value=1,"boolean"!=typeof e.boolValue)return"boolValue: boolean expected"}if(null!=e.intValue&&e.hasOwnProperty("intValue")){if(1===t.value)return"value: multiple values";if(t.value=1,!($util.isInteger(e.intValue)||e.intValue&&$util.isInteger(e.intValue.low)&&$util.isInteger(e.intValue.high)))return"intValue: integer|Long expected"}if(null!=e.doubleValue&&e.hasOwnProperty("doubleValue")){if(1===t.value)return"value: multiple values";if(t.value=1,"number"!=typeof e.doubleValue)return"doubleValue: number expected"}if(null!=e.arrayValue&&e.hasOwnProperty("arrayValue")){if(1===t.value)return"value: multiple values";if(t.value=1,r=$root.opentelemetry.proto.common.v1.ArrayValue.verify(e.arrayValue))return"arrayValue."+r}if(null!=e.kvlistValue&&e.hasOwnProperty("kvlistValue")){if(1===t.value)return"value: multiple values";var r;if(t.value=1,r=$root.opentelemetry.proto.common.v1.KeyValueList.verify(e.kvlistValue))return"kvlistValue."+r}if(null!=e.bytesValue&&e.hasOwnProperty("bytesValue")){if(1===t.value)return"value: multiple values";if(t.value=1,!(e.bytesValue&&"number"==typeof e.bytesValue.length||$util.isString(e.bytesValue)))return"bytesValue: buffer expected"}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.common.v1.AnyValue)return e;var t=new $root.opentelemetry.proto.common.v1.AnyValue;if(null!=e.stringValue&&(t.stringValue=String(e.stringValue)),null!=e.boolValue&&(t.boolValue=Boolean(e.boolValue)),null!=e.intValue&&($util.Long?(t.intValue=$util.Long.fromValue(e.intValue)).unsigned=!1:"string"==typeof e.intValue?t.intValue=parseInt(e.intValue,10):"number"==typeof e.intValue?t.intValue=e.intValue:"object"==typeof e.intValue&&(t.intValue=new $util.LongBits(e.intValue.low>>>0,e.intValue.high>>>0).toNumber())),null!=e.doubleValue&&(t.doubleValue=Number(e.doubleValue)),null!=e.arrayValue){if("object"!=typeof e.arrayValue)throw TypeError(".opentelemetry.proto.common.v1.AnyValue.arrayValue: object expected");t.arrayValue=$root.opentelemetry.proto.common.v1.ArrayValue.fromObject(e.arrayValue)}if(null!=e.kvlistValue){if("object"!=typeof e.kvlistValue)throw TypeError(".opentelemetry.proto.common.v1.AnyValue.kvlistValue: object expected");t.kvlistValue=$root.opentelemetry.proto.common.v1.KeyValueList.fromObject(e.kvlistValue)}return null!=e.bytesValue&&("string"==typeof e.bytesValue?$util.base64.decode(e.bytesValue,t.bytesValue=$util.newBuffer($util.base64.length(e.bytesValue)),0):e.bytesValue.length>=0&&(t.bytesValue=e.bytesValue)),t},e.toObject=function(e,t){t||(t={});var r={};return null!=e.stringValue&&e.hasOwnProperty("stringValue")&&(r.stringValue=e.stringValue,t.oneofs&&(r.value="stringValue")),null!=e.boolValue&&e.hasOwnProperty("boolValue")&&(r.boolValue=e.boolValue,t.oneofs&&(r.value="boolValue")),null!=e.intValue&&e.hasOwnProperty("intValue")&&("number"==typeof e.intValue?r.intValue=t.longs===String?String(e.intValue):e.intValue:r.intValue=t.longs===String?$util.Long.prototype.toString.call(e.intValue):t.longs===Number?new $util.LongBits(e.intValue.low>>>0,e.intValue.high>>>0).toNumber():e.intValue,t.oneofs&&(r.value="intValue")),null!=e.doubleValue&&e.hasOwnProperty("doubleValue")&&(r.doubleValue=t.json&&!isFinite(e.doubleValue)?String(e.doubleValue):e.doubleValue,t.oneofs&&(r.value="doubleValue")),null!=e.arrayValue&&e.hasOwnProperty("arrayValue")&&(r.arrayValue=$root.opentelemetry.proto.common.v1.ArrayValue.toObject(e.arrayValue,t),t.oneofs&&(r.value="arrayValue")),null!=e.kvlistValue&&e.hasOwnProperty("kvlistValue")&&(r.kvlistValue=$root.opentelemetry.proto.common.v1.KeyValueList.toObject(e.kvlistValue,t),t.oneofs&&(r.value="kvlistValue")),null!=e.bytesValue&&e.hasOwnProperty("bytesValue")&&(r.bytesValue=t.bytes===String?$util.base64.encode(e.bytesValue,0,e.bytesValue.length):t.bytes===Array?Array.prototype.slice.call(e.bytesValue):e.bytesValue,t.oneofs&&(r.value="bytesValue")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.common.v1.AnyValue"},e}(),v1.ArrayValue=function(){function e(e){if(this.values=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.values=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.values&&e.values.length)for(var r=0;r<e.values.length;++r)$root.opentelemetry.proto.common.v1.AnyValue.encode(e.values[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.common.v1.ArrayValue;e.pos<r;){var o=e.uint32();o>>>3==1?(n.values&&n.values.length||(n.values=[]),n.values.push($root.opentelemetry.proto.common.v1.AnyValue.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.values&&e.hasOwnProperty("values")){if(!Array.isArray(e.values))return"values: array expected";for(var t=0;t<e.values.length;++t){var r=$root.opentelemetry.proto.common.v1.AnyValue.verify(e.values[t]);if(r)return"values."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.common.v1.ArrayValue)return e;var t=new $root.opentelemetry.proto.common.v1.ArrayValue;if(e.values){if(!Array.isArray(e.values))throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: array expected");t.values=[];for(var r=0;r<e.values.length;++r){if("object"!=typeof e.values[r])throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: object expected");t.values[r]=$root.opentelemetry.proto.common.v1.AnyValue.fromObject(e.values[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.values=[]),e.values&&e.values.length){r.values=[];for(var n=0;n<e.values.length;++n)r.values[n]=$root.opentelemetry.proto.common.v1.AnyValue.toObject(e.values[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.common.v1.ArrayValue"},e}(),v1.KeyValueList=function(){function e(e){if(this.values=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.values=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.values&&e.values.length)for(var r=0;r<e.values.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.values[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.common.v1.KeyValueList;e.pos<r;){var o=e.uint32();o>>>3==1?(n.values&&n.values.length||(n.values=[]),n.values.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.values&&e.hasOwnProperty("values")){if(!Array.isArray(e.values))return"values: array expected";for(var t=0;t<e.values.length;++t){var r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.values[t]);if(r)return"values."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.common.v1.KeyValueList)return e;var t=new $root.opentelemetry.proto.common.v1.KeyValueList;if(e.values){if(!Array.isArray(e.values))throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: array expected");t.values=[];for(var r=0;r<e.values.length;++r){if("object"!=typeof e.values[r])throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: object expected");t.values[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.values[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.values=[]),e.values&&e.values.length){r.values=[];for(var n=0;n<e.values.length;++n)r.values[n]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.values[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.common.v1.KeyValueList"},e}(),v1.KeyValue=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.key=null,e.prototype.value=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.key&&Object.hasOwnProperty.call(e,"key")&&t.uint32(10).string(e.key),null!=e.value&&Object.hasOwnProperty.call(e,"value")&&$root.opentelemetry.proto.common.v1.AnyValue.encode(e.value,t.uint32(18).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.common.v1.KeyValue;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.key=e.string();break;case 2:n.value=$root.opentelemetry.proto.common.v1.AnyValue.decode(e,e.uint32());break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.key&&e.hasOwnProperty("key")&&!$util.isString(e.key))return"key: string expected";if(null!=e.value&&e.hasOwnProperty("value")){var t=$root.opentelemetry.proto.common.v1.AnyValue.verify(e.value);if(t)return"value."+t}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.common.v1.KeyValue)return e;var t=new $root.opentelemetry.proto.common.v1.KeyValue;if(null!=e.key&&(t.key=String(e.key)),null!=e.value){if("object"!=typeof e.value)throw TypeError(".opentelemetry.proto.common.v1.KeyValue.value: object expected");t.value=$root.opentelemetry.proto.common.v1.AnyValue.fromObject(e.value)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.key="",r.value=null),null!=e.key&&e.hasOwnProperty("key")&&(r.key=e.key),null!=e.value&&e.hasOwnProperty("value")&&(r.value=$root.opentelemetry.proto.common.v1.AnyValue.toObject(e.value,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.common.v1.KeyValue"},e}(),v1.InstrumentationScope=function(){function e(e){if(this.attributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.name=null,e.prototype.version=null,e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(10).string(e.name),null!=e.version&&Object.hasOwnProperty.call(e,"version")&&t.uint32(18).string(e.version),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(26).fork()).ldelim();return null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(32).uint32(e.droppedAttributesCount),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.common.v1.InstrumentationScope;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.name=e.string();break;case 2:n.version=e.string();break;case 3:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 4:n.droppedAttributesCount=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.name&&e.hasOwnProperty("name")&&!$util.isString(e.name))return"name: string expected";if(null!=e.version&&e.hasOwnProperty("version")&&!$util.isString(e.version))return"version: string expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t){var r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]);if(r)return"attributes."+r}}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount)?"droppedAttributesCount: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.common.v1.InstrumentationScope)return e;var t=new $root.opentelemetry.proto.common.v1.InstrumentationScope;if(null!=e.name&&(t.name=String(e.name)),null!=e.version&&(t.version=String(e.version)),e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}return null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[]),t.defaults&&(r.name="",r.version="",r.droppedAttributesCount=0),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),null!=e.version&&e.hasOwnProperty("version")&&(r.version=e.version),e.attributes&&e.attributes.length){r.attributes=[];for(var n=0;n<e.attributes.length;++n)r.attributes[n]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[n],t)}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.common.v1.InstrumentationScope"},e}(),v1),common),proto.resource=((resource={}).v1=function(){var e={};return e.Resource=function(){function e(e){if(this.attributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(10).fork()).ldelim();return null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(16).uint32(e.droppedAttributesCount),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.resource.v1.Resource;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.droppedAttributesCount=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t){var r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]);if(r)return"attributes."+r}}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount)?"droppedAttributesCount: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.resource.v1.Resource)return e;var t=new $root.opentelemetry.proto.resource.v1.Resource;if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}return null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[]),t.defaults&&(r.droppedAttributesCount=0),e.attributes&&e.attributes.length){r.attributes=[];for(var n=0;n<e.attributes.length;++n)r.attributes[n]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[n],t)}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.resource.v1.Resource"},e}(),e}(),resource),proto.trace=function(){var e={};return e.v1=function(){var e={};return e.TracesData=function(){function e(e){if(this.resourceSpans=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceSpans=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceSpans&&e.resourceSpans.length)for(var r=0;r<e.resourceSpans.length;++r)$root.opentelemetry.proto.trace.v1.ResourceSpans.encode(e.resourceSpans[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.TracesData;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceSpans&&n.resourceSpans.length||(n.resourceSpans=[]),n.resourceSpans.push($root.opentelemetry.proto.trace.v1.ResourceSpans.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceSpans&&e.hasOwnProperty("resourceSpans")){if(!Array.isArray(e.resourceSpans))return"resourceSpans: array expected";for(var t=0;t<e.resourceSpans.length;++t){var r=$root.opentelemetry.proto.trace.v1.ResourceSpans.verify(e.resourceSpans[t]);if(r)return"resourceSpans."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.TracesData)return e;var t=new $root.opentelemetry.proto.trace.v1.TracesData;if(e.resourceSpans){if(!Array.isArray(e.resourceSpans))throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: array expected");t.resourceSpans=[];for(var r=0;r<e.resourceSpans.length;++r){if("object"!=typeof e.resourceSpans[r])throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: object expected");t.resourceSpans[r]=$root.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(e.resourceSpans[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceSpans=[]),e.resourceSpans&&e.resourceSpans.length){r.resourceSpans=[];for(var n=0;n<e.resourceSpans.length;++n)r.resourceSpans[n]=$root.opentelemetry.proto.trace.v1.ResourceSpans.toObject(e.resourceSpans[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.TracesData"},e}(),e.ResourceSpans=function(){function e(e){if(this.scopeSpans=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resource=null,e.prototype.scopeSpans=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resource&&Object.hasOwnProperty.call(e,"resource")&&$root.opentelemetry.proto.resource.v1.Resource.encode(e.resource,t.uint32(10).fork()).ldelim(),null!=e.scopeSpans&&e.scopeSpans.length)for(var r=0;r<e.scopeSpans.length;++r)$root.opentelemetry.proto.trace.v1.ScopeSpans.encode(e.scopeSpans[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.ResourceSpans;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.resource=$root.opentelemetry.proto.resource.v1.Resource.decode(e,e.uint32());break;case 2:n.scopeSpans&&n.scopeSpans.length||(n.scopeSpans=[]),n.scopeSpans.push($root.opentelemetry.proto.trace.v1.ScopeSpans.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resource&&e.hasOwnProperty("resource")&&(r=$root.opentelemetry.proto.resource.v1.Resource.verify(e.resource)))return"resource."+r;if(null!=e.scopeSpans&&e.hasOwnProperty("scopeSpans")){if(!Array.isArray(e.scopeSpans))return"scopeSpans: array expected";for(var t=0;t<e.scopeSpans.length;++t){var r;if(r=$root.opentelemetry.proto.trace.v1.ScopeSpans.verify(e.scopeSpans[t]))return"scopeSpans."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.ResourceSpans)return e;var t=new $root.opentelemetry.proto.trace.v1.ResourceSpans;if(null!=e.resource){if("object"!=typeof e.resource)throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.resource: object expected");t.resource=$root.opentelemetry.proto.resource.v1.Resource.fromObject(e.resource)}if(e.scopeSpans){if(!Array.isArray(e.scopeSpans))throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: array expected");t.scopeSpans=[];for(var r=0;r<e.scopeSpans.length;++r){if("object"!=typeof e.scopeSpans[r])throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: object expected");t.scopeSpans[r]=$root.opentelemetry.proto.trace.v1.ScopeSpans.fromObject(e.scopeSpans[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.scopeSpans=[]),t.defaults&&(r.resource=null,r.schemaUrl=""),null!=e.resource&&e.hasOwnProperty("resource")&&(r.resource=$root.opentelemetry.proto.resource.v1.Resource.toObject(e.resource,t)),e.scopeSpans&&e.scopeSpans.length){r.scopeSpans=[];for(var n=0;n<e.scopeSpans.length;++n)r.scopeSpans[n]=$root.opentelemetry.proto.trace.v1.ScopeSpans.toObject(e.scopeSpans[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.ResourceSpans"},e}(),e.ScopeSpans=function(){function e(e){if(this.spans=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.scope=null,e.prototype.spans=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.scope&&Object.hasOwnProperty.call(e,"scope")&&$root.opentelemetry.proto.common.v1.InstrumentationScope.encode(e.scope,t.uint32(10).fork()).ldelim(),null!=e.spans&&e.spans.length)for(var r=0;r<e.spans.length;++r)$root.opentelemetry.proto.trace.v1.Span.encode(e.spans[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.ScopeSpans;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.decode(e,e.uint32());break;case 2:n.spans&&n.spans.length||(n.spans=[]),n.spans.push($root.opentelemetry.proto.trace.v1.Span.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.scope&&e.hasOwnProperty("scope")&&(r=$root.opentelemetry.proto.common.v1.InstrumentationScope.verify(e.scope)))return"scope."+r;if(null!=e.spans&&e.hasOwnProperty("spans")){if(!Array.isArray(e.spans))return"spans: array expected";for(var t=0;t<e.spans.length;++t){var r;if(r=$root.opentelemetry.proto.trace.v1.Span.verify(e.spans[t]))return"spans."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.ScopeSpans)return e;var t=new $root.opentelemetry.proto.trace.v1.ScopeSpans;if(null!=e.scope){if("object"!=typeof e.scope)throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.scope: object expected");t.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(e.scope)}if(e.spans){if(!Array.isArray(e.spans))throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: array expected");t.spans=[];for(var r=0;r<e.spans.length;++r){if("object"!=typeof e.spans[r])throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: object expected");t.spans[r]=$root.opentelemetry.proto.trace.v1.Span.fromObject(e.spans[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.spans=[]),t.defaults&&(r.scope=null,r.schemaUrl=""),null!=e.scope&&e.hasOwnProperty("scope")&&(r.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.toObject(e.scope,t)),e.spans&&e.spans.length){r.spans=[];for(var n=0;n<e.spans.length;++n)r.spans[n]=$root.opentelemetry.proto.trace.v1.Span.toObject(e.spans[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.ScopeSpans"},e}(),e.Span=function(){function e(e){if(this.attributes=[],this.events=[],this.links=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t,r;return e.prototype.traceId=null,e.prototype.spanId=null,e.prototype.traceState=null,e.prototype.parentSpanId=null,e.prototype.name=null,e.prototype.kind=null,e.prototype.startTimeUnixNano=null,e.prototype.endTimeUnixNano=null,e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.prototype.events=$util.emptyArray,e.prototype.droppedEventsCount=null,e.prototype.links=$util.emptyArray,e.prototype.droppedLinksCount=null,e.prototype.status=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.traceId&&Object.hasOwnProperty.call(e,"traceId")&&t.uint32(10).bytes(e.traceId),null!=e.spanId&&Object.hasOwnProperty.call(e,"spanId")&&t.uint32(18).bytes(e.spanId),null!=e.traceState&&Object.hasOwnProperty.call(e,"traceState")&&t.uint32(26).string(e.traceState),null!=e.parentSpanId&&Object.hasOwnProperty.call(e,"parentSpanId")&&t.uint32(34).bytes(e.parentSpanId),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(42).string(e.name),null!=e.kind&&Object.hasOwnProperty.call(e,"kind")&&t.uint32(48).int32(e.kind),null!=e.startTimeUnixNano&&Object.hasOwnProperty.call(e,"startTimeUnixNano")&&t.uint32(57).fixed64(e.startTimeUnixNano),null!=e.endTimeUnixNano&&Object.hasOwnProperty.call(e,"endTimeUnixNano")&&t.uint32(65).fixed64(e.endTimeUnixNano),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(74).fork()).ldelim();if(null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(80).uint32(e.droppedAttributesCount),null!=e.events&&e.events.length)for(r=0;r<e.events.length;++r)$root.opentelemetry.proto.trace.v1.Span.Event.encode(e.events[r],t.uint32(90).fork()).ldelim();if(null!=e.droppedEventsCount&&Object.hasOwnProperty.call(e,"droppedEventsCount")&&t.uint32(96).uint32(e.droppedEventsCount),null!=e.links&&e.links.length)for(r=0;r<e.links.length;++r)$root.opentelemetry.proto.trace.v1.Span.Link.encode(e.links[r],t.uint32(106).fork()).ldelim();return null!=e.droppedLinksCount&&Object.hasOwnProperty.call(e,"droppedLinksCount")&&t.uint32(112).uint32(e.droppedLinksCount),null!=e.status&&Object.hasOwnProperty.call(e,"status")&&$root.opentelemetry.proto.trace.v1.Status.encode(e.status,t.uint32(122).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.Span;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.traceId=e.bytes();break;case 2:n.spanId=e.bytes();break;case 3:n.traceState=e.string();break;case 4:n.parentSpanId=e.bytes();break;case 5:n.name=e.string();break;case 6:n.kind=e.int32();break;case 7:n.startTimeUnixNano=e.fixed64();break;case 8:n.endTimeUnixNano=e.fixed64();break;case 9:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 10:n.droppedAttributesCount=e.uint32();break;case 11:n.events&&n.events.length||(n.events=[]),n.events.push($root.opentelemetry.proto.trace.v1.Span.Event.decode(e,e.uint32()));break;case 12:n.droppedEventsCount=e.uint32();break;case 13:n.links&&n.links.length||(n.links=[]),n.links.push($root.opentelemetry.proto.trace.v1.Span.Link.decode(e,e.uint32()));break;case 14:n.droppedLinksCount=e.uint32();break;case 15:n.status=$root.opentelemetry.proto.trace.v1.Status.decode(e,e.uint32());break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.traceId&&e.hasOwnProperty("traceId")&&!(e.traceId&&"number"==typeof e.traceId.length||$util.isString(e.traceId)))return"traceId: buffer expected";if(null!=e.spanId&&e.hasOwnProperty("spanId")&&!(e.spanId&&"number"==typeof e.spanId.length||$util.isString(e.spanId)))return"spanId: buffer expected";if(null!=e.traceState&&e.hasOwnProperty("traceState")&&!$util.isString(e.traceState))return"traceState: string expected";if(null!=e.parentSpanId&&e.hasOwnProperty("parentSpanId")&&!(e.parentSpanId&&"number"==typeof e.parentSpanId.length||$util.isString(e.parentSpanId)))return"parentSpanId: buffer expected";if(null!=e.name&&e.hasOwnProperty("name")&&!$util.isString(e.name))return"name: string expected";if(null!=e.kind&&e.hasOwnProperty("kind"))switch(e.kind){default:return"kind: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&!($util.isInteger(e.startTimeUnixNano)||e.startTimeUnixNano&&$util.isInteger(e.startTimeUnixNano.low)&&$util.isInteger(e.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected";if(null!=e.endTimeUnixNano&&e.hasOwnProperty("endTimeUnixNano")&&!($util.isInteger(e.endTimeUnixNano)||e.endTimeUnixNano&&$util.isInteger(e.endTimeUnixNano.low)&&$util.isInteger(e.endTimeUnixNano.high)))return"endTimeUnixNano: integer|Long expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t)if(r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]))return"attributes."+r}if(null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount))return"droppedAttributesCount: integer expected";if(null!=e.events&&e.hasOwnProperty("events")){if(!Array.isArray(e.events))return"events: array expected";for(t=0;t<e.events.length;++t)if(r=$root.opentelemetry.proto.trace.v1.Span.Event.verify(e.events[t]))return"events."+r}if(null!=e.droppedEventsCount&&e.hasOwnProperty("droppedEventsCount")&&!$util.isInteger(e.droppedEventsCount))return"droppedEventsCount: integer expected";if(null!=e.links&&e.hasOwnProperty("links")){if(!Array.isArray(e.links))return"links: array expected";for(t=0;t<e.links.length;++t)if(r=$root.opentelemetry.proto.trace.v1.Span.Link.verify(e.links[t]))return"links."+r}return null!=e.droppedLinksCount&&e.hasOwnProperty("droppedLinksCount")&&!$util.isInteger(e.droppedLinksCount)?"droppedLinksCount: integer expected":null!=e.status&&e.hasOwnProperty("status")&&(r=$root.opentelemetry.proto.trace.v1.Status.verify(e.status))?"status."+r:null;var r},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.Span)return e;var t=new $root.opentelemetry.proto.trace.v1.Span;switch(null!=e.traceId&&("string"==typeof e.traceId?$util.base64.decode(e.traceId,t.traceId=$util.newBuffer($util.base64.length(e.traceId)),0):e.traceId.length>=0&&(t.traceId=e.traceId)),null!=e.spanId&&("string"==typeof e.spanId?$util.base64.decode(e.spanId,t.spanId=$util.newBuffer($util.base64.length(e.spanId)),0):e.spanId.length>=0&&(t.spanId=e.spanId)),null!=e.traceState&&(t.traceState=String(e.traceState)),null!=e.parentSpanId&&("string"==typeof e.parentSpanId?$util.base64.decode(e.parentSpanId,t.parentSpanId=$util.newBuffer($util.base64.length(e.parentSpanId)),0):e.parentSpanId.length>=0&&(t.parentSpanId=e.parentSpanId)),null!=e.name&&(t.name=String(e.name)),e.kind){default:if("number"==typeof e.kind){t.kind=e.kind;break}break;case"SPAN_KIND_UNSPECIFIED":case 0:t.kind=0;break;case"SPAN_KIND_INTERNAL":case 1:t.kind=1;break;case"SPAN_KIND_SERVER":case 2:t.kind=2;break;case"SPAN_KIND_CLIENT":case 3:t.kind=3;break;case"SPAN_KIND_PRODUCER":case 4:t.kind=4;break;case"SPAN_KIND_CONSUMER":case 5:t.kind=5}if(null!=e.startTimeUnixNano&&($util.Long?(t.startTimeUnixNano=$util.Long.fromValue(e.startTimeUnixNano)).unsigned=!1:"string"==typeof e.startTimeUnixNano?t.startTimeUnixNano=parseInt(e.startTimeUnixNano,10):"number"==typeof e.startTimeUnixNano?t.startTimeUnixNano=e.startTimeUnixNano:"object"==typeof e.startTimeUnixNano&&(t.startTimeUnixNano=new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber())),null!=e.endTimeUnixNano&&($util.Long?(t.endTimeUnixNano=$util.Long.fromValue(e.endTimeUnixNano)).unsigned=!1:"string"==typeof e.endTimeUnixNano?t.endTimeUnixNano=parseInt(e.endTimeUnixNano,10):"number"==typeof e.endTimeUnixNano?t.endTimeUnixNano=e.endTimeUnixNano:"object"==typeof e.endTimeUnixNano&&(t.endTimeUnixNano=new $util.LongBits(e.endTimeUnixNano.low>>>0,e.endTimeUnixNano.high>>>0).toNumber())),e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}if(null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),e.events){if(!Array.isArray(e.events))throw TypeError(".opentelemetry.proto.trace.v1.Span.events: array expected");for(t.events=[],r=0;r<e.events.length;++r){if("object"!=typeof e.events[r])throw TypeError(".opentelemetry.proto.trace.v1.Span.events: object expected");t.events[r]=$root.opentelemetry.proto.trace.v1.Span.Event.fromObject(e.events[r])}}if(null!=e.droppedEventsCount&&(t.droppedEventsCount=e.droppedEventsCount>>>0),e.links){if(!Array.isArray(e.links))throw TypeError(".opentelemetry.proto.trace.v1.Span.links: array expected");for(t.links=[],r=0;r<e.links.length;++r){if("object"!=typeof e.links[r])throw TypeError(".opentelemetry.proto.trace.v1.Span.links: object expected");t.links[r]=$root.opentelemetry.proto.trace.v1.Span.Link.fromObject(e.links[r])}}if(null!=e.droppedLinksCount&&(t.droppedLinksCount=e.droppedLinksCount>>>0),null!=e.status){if("object"!=typeof e.status)throw TypeError(".opentelemetry.proto.trace.v1.Span.status: object expected");t.status=$root.opentelemetry.proto.trace.v1.Status.fromObject(e.status)}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[],r.events=[],r.links=[]),t.defaults){if(t.bytes===String?r.traceId="":(r.traceId=[],t.bytes!==Array&&(r.traceId=$util.newBuffer(r.traceId))),t.bytes===String?r.spanId="":(r.spanId=[],t.bytes!==Array&&(r.spanId=$util.newBuffer(r.spanId))),r.traceState="",t.bytes===String?r.parentSpanId="":(r.parentSpanId=[],t.bytes!==Array&&(r.parentSpanId=$util.newBuffer(r.parentSpanId))),r.name="",r.kind=t.enums===String?"SPAN_KIND_UNSPECIFIED":0,$util.Long){var n=new $util.Long(0,0,!1);r.startTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTimeUnixNano=t.longs===String?"0":0;$util.Long?(n=new $util.Long(0,0,!1),r.endTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.endTimeUnixNano=t.longs===String?"0":0,r.droppedAttributesCount=0,r.droppedEventsCount=0,r.droppedLinksCount=0,r.status=null}if(null!=e.traceId&&e.hasOwnProperty("traceId")&&(r.traceId=t.bytes===String?$util.base64.encode(e.traceId,0,e.traceId.length):t.bytes===Array?Array.prototype.slice.call(e.traceId):e.traceId),null!=e.spanId&&e.hasOwnProperty("spanId")&&(r.spanId=t.bytes===String?$util.base64.encode(e.spanId,0,e.spanId.length):t.bytes===Array?Array.prototype.slice.call(e.spanId):e.spanId),null!=e.traceState&&e.hasOwnProperty("traceState")&&(r.traceState=e.traceState),null!=e.parentSpanId&&e.hasOwnProperty("parentSpanId")&&(r.parentSpanId=t.bytes===String?$util.base64.encode(e.parentSpanId,0,e.parentSpanId.length):t.bytes===Array?Array.prototype.slice.call(e.parentSpanId):e.parentSpanId),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),null!=e.kind&&e.hasOwnProperty("kind")&&(r.kind=t.enums===String?void 0===$root.opentelemetry.proto.trace.v1.Span.SpanKind[e.kind]?e.kind:$root.opentelemetry.proto.trace.v1.Span.SpanKind[e.kind]:e.kind),null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&("number"==typeof e.startTimeUnixNano?r.startTimeUnixNano=t.longs===String?String(e.startTimeUnixNano):e.startTimeUnixNano:r.startTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.startTimeUnixNano):t.longs===Number?new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber():e.startTimeUnixNano),null!=e.endTimeUnixNano&&e.hasOwnProperty("endTimeUnixNano")&&("number"==typeof e.endTimeUnixNano?r.endTimeUnixNano=t.longs===String?String(e.endTimeUnixNano):e.endTimeUnixNano:r.endTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.endTimeUnixNano):t.longs===Number?new $util.LongBits(e.endTimeUnixNano.low>>>0,e.endTimeUnixNano.high>>>0).toNumber():e.endTimeUnixNano),e.attributes&&e.attributes.length){r.attributes=[];for(var o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t)}if(null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),e.events&&e.events.length)for(r.events=[],o=0;o<e.events.length;++o)r.events[o]=$root.opentelemetry.proto.trace.v1.Span.Event.toObject(e.events[o],t);if(null!=e.droppedEventsCount&&e.hasOwnProperty("droppedEventsCount")&&(r.droppedEventsCount=e.droppedEventsCount),e.links&&e.links.length)for(r.links=[],o=0;o<e.links.length;++o)r.links[o]=$root.opentelemetry.proto.trace.v1.Span.Link.toObject(e.links[o],t);return null!=e.droppedLinksCount&&e.hasOwnProperty("droppedLinksCount")&&(r.droppedLinksCount=e.droppedLinksCount),null!=e.status&&e.hasOwnProperty("status")&&(r.status=$root.opentelemetry.proto.trace.v1.Status.toObject(e.status,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.Span"},e.SpanKind=(t={},(r=Object.create(t))[t[0]="SPAN_KIND_UNSPECIFIED"]=0,r[t[1]="SPAN_KIND_INTERNAL"]=1,r[t[2]="SPAN_KIND_SERVER"]=2,r[t[3]="SPAN_KIND_CLIENT"]=3,r[t[4]="SPAN_KIND_PRODUCER"]=4,r[t[5]="SPAN_KIND_CONSUMER"]=5,r),e.Event=function(){function e(e){if(this.attributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.timeUnixNano=null,e.prototype.name=null,e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(9).fixed64(e.timeUnixNano),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(18).string(e.name),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(26).fork()).ldelim();return null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(32).uint32(e.droppedAttributesCount),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.Span.Event;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.timeUnixNano=e.fixed64();break;case 2:n.name=e.string();break;case 3:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 4:n.droppedAttributesCount=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.name&&e.hasOwnProperty("name")&&!$util.isString(e.name))return"name: string expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t){var r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]);if(r)return"attributes."+r}}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount)?"droppedAttributesCount: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.Span.Event)return e;var t=new $root.opentelemetry.proto.trace.v1.Span.Event;if(null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.name&&(t.name=String(e.name)),e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}return null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timeUnixNano=t.longs===String?"0":0;r.name="",r.droppedAttributesCount=0}if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),e.attributes&&e.attributes.length){r.attributes=[];for(var o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t)}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.Span.Event"},e}(),e.Link=function(){function e(e){if(this.attributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.traceId=null,e.prototype.spanId=null,e.prototype.traceState=null,e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.traceId&&Object.hasOwnProperty.call(e,"traceId")&&t.uint32(10).bytes(e.traceId),null!=e.spanId&&Object.hasOwnProperty.call(e,"spanId")&&t.uint32(18).bytes(e.spanId),null!=e.traceState&&Object.hasOwnProperty.call(e,"traceState")&&t.uint32(26).string(e.traceState),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(34).fork()).ldelim();return null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(40).uint32(e.droppedAttributesCount),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.Span.Link;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.traceId=e.bytes();break;case 2:n.spanId=e.bytes();break;case 3:n.traceState=e.string();break;case 4:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 5:n.droppedAttributesCount=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.traceId&&e.hasOwnProperty("traceId")&&!(e.traceId&&"number"==typeof e.traceId.length||$util.isString(e.traceId)))return"traceId: buffer expected";if(null!=e.spanId&&e.hasOwnProperty("spanId")&&!(e.spanId&&"number"==typeof e.spanId.length||$util.isString(e.spanId)))return"spanId: buffer expected";if(null!=e.traceState&&e.hasOwnProperty("traceState")&&!$util.isString(e.traceState))return"traceState: string expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t){var r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]);if(r)return"attributes."+r}}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount)?"droppedAttributesCount: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.Span.Link)return e;var t=new $root.opentelemetry.proto.trace.v1.Span.Link;if(null!=e.traceId&&("string"==typeof e.traceId?$util.base64.decode(e.traceId,t.traceId=$util.newBuffer($util.base64.length(e.traceId)),0):e.traceId.length>=0&&(t.traceId=e.traceId)),null!=e.spanId&&("string"==typeof e.spanId?$util.base64.decode(e.spanId,t.spanId=$util.newBuffer($util.base64.length(e.spanId)),0):e.spanId.length>=0&&(t.spanId=e.spanId)),null!=e.traceState&&(t.traceState=String(e.traceState)),e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}return null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[]),t.defaults&&(t.bytes===String?r.traceId="":(r.traceId=[],t.bytes!==Array&&(r.traceId=$util.newBuffer(r.traceId))),t.bytes===String?r.spanId="":(r.spanId=[],t.bytes!==Array&&(r.spanId=$util.newBuffer(r.spanId))),r.traceState="",r.droppedAttributesCount=0),null!=e.traceId&&e.hasOwnProperty("traceId")&&(r.traceId=t.bytes===String?$util.base64.encode(e.traceId,0,e.traceId.length):t.bytes===Array?Array.prototype.slice.call(e.traceId):e.traceId),null!=e.spanId&&e.hasOwnProperty("spanId")&&(r.spanId=t.bytes===String?$util.base64.encode(e.spanId,0,e.spanId.length):t.bytes===Array?Array.prototype.slice.call(e.spanId):e.spanId),null!=e.traceState&&e.hasOwnProperty("traceState")&&(r.traceState=e.traceState),e.attributes&&e.attributes.length){r.attributes=[];for(var n=0;n<e.attributes.length;++n)r.attributes[n]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[n],t)}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.Span.Link"},e}(),e}(),e.Status=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t,r;return e.prototype.message=null,e.prototype.code=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.message&&Object.hasOwnProperty.call(e,"message")&&t.uint32(18).string(e.message),null!=e.code&&Object.hasOwnProperty.call(e,"code")&&t.uint32(24).int32(e.code),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.trace.v1.Status;e.pos<r;){var o=e.uint32();switch(o>>>3){case 2:n.message=e.string();break;case 3:n.code=e.int32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.message&&e.hasOwnProperty("message")&&!$util.isString(e.message))return"message: string expected";if(null!=e.code&&e.hasOwnProperty("code"))switch(e.code){default:return"code: enum value expected";case 0:case 1:case 2:}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.trace.v1.Status)return e;var t=new $root.opentelemetry.proto.trace.v1.Status;switch(null!=e.message&&(t.message=String(e.message)),e.code){default:if("number"==typeof e.code){t.code=e.code;break}break;case"STATUS_CODE_UNSET":case 0:t.code=0;break;case"STATUS_CODE_OK":case 1:t.code=1;break;case"STATUS_CODE_ERROR":case 2:t.code=2}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.message="",r.code=t.enums===String?"STATUS_CODE_UNSET":0),null!=e.message&&e.hasOwnProperty("message")&&(r.message=e.message),null!=e.code&&e.hasOwnProperty("code")&&(r.code=t.enums===String?void 0===$root.opentelemetry.proto.trace.v1.Status.StatusCode[e.code]?e.code:$root.opentelemetry.proto.trace.v1.Status.StatusCode[e.code]:e.code),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.trace.v1.Status"},e.StatusCode=(t={},(r=Object.create(t))[t[0]="STATUS_CODE_UNSET"]=0,r[t[1]="STATUS_CODE_OK"]=1,r[t[2]="STATUS_CODE_ERROR"]=2,r),e}(),e}(),e}(),proto.collector=((collector={}).trace=function(){var e={};return e.v1=function(){var e={};return e.TraceService=function(){function e(e,t,r){$protobuf.rpc.Service.call(this,e,t,r)}return(e.prototype=Object.create($protobuf.rpc.Service.prototype)).constructor=e,e.create=function(e,t,r){return new this(e,t,r)},Object.defineProperty(e.prototype.export=function e(t,r){return this.rpcCall(e,$root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest,$root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,t,r)},"name",{value:"Export"}),e}(),e.ExportTraceServiceRequest=function(){function e(e){if(this.resourceSpans=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceSpans=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceSpans&&e.resourceSpans.length)for(var r=0;r<e.resourceSpans.length;++r)$root.opentelemetry.proto.trace.v1.ResourceSpans.encode(e.resourceSpans[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceSpans&&n.resourceSpans.length||(n.resourceSpans=[]),n.resourceSpans.push($root.opentelemetry.proto.trace.v1.ResourceSpans.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceSpans&&e.hasOwnProperty("resourceSpans")){if(!Array.isArray(e.resourceSpans))return"resourceSpans: array expected";for(var t=0;t<e.resourceSpans.length;++t){var r=$root.opentelemetry.proto.trace.v1.ResourceSpans.verify(e.resourceSpans[t]);if(r)return"resourceSpans."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest)return e;var t=new $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;if(e.resourceSpans){if(!Array.isArray(e.resourceSpans))throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: array expected");t.resourceSpans=[];for(var r=0;r<e.resourceSpans.length;++r){if("object"!=typeof e.resourceSpans[r])throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: object expected");t.resourceSpans[r]=$root.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(e.resourceSpans[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceSpans=[]),e.resourceSpans&&e.resourceSpans.length){r.resourceSpans=[];for(var n=0;n<e.resourceSpans.length;++n)r.resourceSpans[n]=$root.opentelemetry.proto.trace.v1.ResourceSpans.toObject(e.resourceSpans[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest"},e}(),e.ExportTraceServiceResponse=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.partialSuccess=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.partialSuccess&&Object.hasOwnProperty.call(e,"partialSuccess")&&$root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.encode(e.partialSuccess,t.uint32(10).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;e.pos<r;){var o=e.uint32();o>>>3==1?n.partialSuccess=$root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.decode(e,e.uint32()):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")){var t=$root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.verify(e.partialSuccess);if(t)return"partialSuccess."+t}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse)return e;var t=new $root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;if(null!=e.partialSuccess){if("object"!=typeof e.partialSuccess)throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse.partialSuccess: object expected");t.partialSuccess=$root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.fromObject(e.partialSuccess)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.partialSuccess=null),null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")&&(r.partialSuccess=$root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.toObject(e.partialSuccess,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse"},e}(),e.ExportTracePartialSuccess=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.rejectedSpans=null,e.prototype.errorMessage=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.rejectedSpans&&Object.hasOwnProperty.call(e,"rejectedSpans")&&t.uint32(8).int64(e.rejectedSpans),null!=e.errorMessage&&Object.hasOwnProperty.call(e,"errorMessage")&&t.uint32(18).string(e.errorMessage),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.rejectedSpans=e.int64();break;case 2:n.errorMessage=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.rejectedSpans&&e.hasOwnProperty("rejectedSpans")&&!($util.isInteger(e.rejectedSpans)||e.rejectedSpans&&$util.isInteger(e.rejectedSpans.low)&&$util.isInteger(e.rejectedSpans.high))?"rejectedSpans: integer|Long expected":null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&!$util.isString(e.errorMessage)?"errorMessage: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess)return e;var t=new $root.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;return null!=e.rejectedSpans&&($util.Long?(t.rejectedSpans=$util.Long.fromValue(e.rejectedSpans)).unsigned=!1:"string"==typeof e.rejectedSpans?t.rejectedSpans=parseInt(e.rejectedSpans,10):"number"==typeof e.rejectedSpans?t.rejectedSpans=e.rejectedSpans:"object"==typeof e.rejectedSpans&&(t.rejectedSpans=new $util.LongBits(e.rejectedSpans.low>>>0,e.rejectedSpans.high>>>0).toNumber())),null!=e.errorMessage&&(t.errorMessage=String(e.errorMessage)),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.rejectedSpans=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.rejectedSpans=t.longs===String?"0":0;r.errorMessage=""}return null!=e.rejectedSpans&&e.hasOwnProperty("rejectedSpans")&&("number"==typeof e.rejectedSpans?r.rejectedSpans=t.longs===String?String(e.rejectedSpans):e.rejectedSpans:r.rejectedSpans=t.longs===String?$util.Long.prototype.toString.call(e.rejectedSpans):t.longs===Number?new $util.LongBits(e.rejectedSpans.low>>>0,e.rejectedSpans.high>>>0).toNumber():e.rejectedSpans),null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&(r.errorMessage=e.errorMessage),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess"},e}(),e}(),e}(),collector.metrics=function(){var e={};return e.v1=function(){var e={};return e.MetricsService=function(){function e(e,t,r){$protobuf.rpc.Service.call(this,e,t,r)}return(e.prototype=Object.create($protobuf.rpc.Service.prototype)).constructor=e,e.create=function(e,t,r){return new this(e,t,r)},Object.defineProperty(e.prototype.export=function e(t,r){return this.rpcCall(e,$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest,$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,t,r)},"name",{value:"Export"}),e}(),e.ExportMetricsServiceRequest=function(){function e(e){if(this.resourceMetrics=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceMetrics=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceMetrics&&e.resourceMetrics.length)for(var r=0;r<e.resourceMetrics.length;++r)$root.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(e.resourceMetrics[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceMetrics&&n.resourceMetrics.length||(n.resourceMetrics=[]),n.resourceMetrics.push($root.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceMetrics&&e.hasOwnProperty("resourceMetrics")){if(!Array.isArray(e.resourceMetrics))return"resourceMetrics: array expected";for(var t=0;t<e.resourceMetrics.length;++t){var r=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(e.resourceMetrics[t]);if(r)return"resourceMetrics."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest)return e;var t=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;if(e.resourceMetrics){if(!Array.isArray(e.resourceMetrics))throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: array expected");t.resourceMetrics=[];for(var r=0;r<e.resourceMetrics.length;++r){if("object"!=typeof e.resourceMetrics[r])throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: object expected");t.resourceMetrics[r]=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(e.resourceMetrics[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceMetrics=[]),e.resourceMetrics&&e.resourceMetrics.length){r.resourceMetrics=[];for(var n=0;n<e.resourceMetrics.length;++n)r.resourceMetrics[n]=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(e.resourceMetrics[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest"},e}(),e.ExportMetricsServiceResponse=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.partialSuccess=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.partialSuccess&&Object.hasOwnProperty.call(e,"partialSuccess")&&$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.encode(e.partialSuccess,t.uint32(10).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;e.pos<r;){var o=e.uint32();o>>>3==1?n.partialSuccess=$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.decode(e,e.uint32()):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")){var t=$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.verify(e.partialSuccess);if(t)return"partialSuccess."+t}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse)return e;var t=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;if(null!=e.partialSuccess){if("object"!=typeof e.partialSuccess)throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse.partialSuccess: object expected");t.partialSuccess=$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.fromObject(e.partialSuccess)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.partialSuccess=null),null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")&&(r.partialSuccess=$root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.toObject(e.partialSuccess,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse"},e}(),e.ExportMetricsPartialSuccess=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.rejectedDataPoints=null,e.prototype.errorMessage=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.rejectedDataPoints&&Object.hasOwnProperty.call(e,"rejectedDataPoints")&&t.uint32(8).int64(e.rejectedDataPoints),null!=e.errorMessage&&Object.hasOwnProperty.call(e,"errorMessage")&&t.uint32(18).string(e.errorMessage),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.rejectedDataPoints=e.int64();break;case 2:n.errorMessage=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.rejectedDataPoints&&e.hasOwnProperty("rejectedDataPoints")&&!($util.isInteger(e.rejectedDataPoints)||e.rejectedDataPoints&&$util.isInteger(e.rejectedDataPoints.low)&&$util.isInteger(e.rejectedDataPoints.high))?"rejectedDataPoints: integer|Long expected":null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&!$util.isString(e.errorMessage)?"errorMessage: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess)return e;var t=new $root.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;return null!=e.rejectedDataPoints&&($util.Long?(t.rejectedDataPoints=$util.Long.fromValue(e.rejectedDataPoints)).unsigned=!1:"string"==typeof e.rejectedDataPoints?t.rejectedDataPoints=parseInt(e.rejectedDataPoints,10):"number"==typeof e.rejectedDataPoints?t.rejectedDataPoints=e.rejectedDataPoints:"object"==typeof e.rejectedDataPoints&&(t.rejectedDataPoints=new $util.LongBits(e.rejectedDataPoints.low>>>0,e.rejectedDataPoints.high>>>0).toNumber())),null!=e.errorMessage&&(t.errorMessage=String(e.errorMessage)),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.rejectedDataPoints=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.rejectedDataPoints=t.longs===String?"0":0;r.errorMessage=""}return null!=e.rejectedDataPoints&&e.hasOwnProperty("rejectedDataPoints")&&("number"==typeof e.rejectedDataPoints?r.rejectedDataPoints=t.longs===String?String(e.rejectedDataPoints):e.rejectedDataPoints:r.rejectedDataPoints=t.longs===String?$util.Long.prototype.toString.call(e.rejectedDataPoints):t.longs===Number?new $util.LongBits(e.rejectedDataPoints.low>>>0,e.rejectedDataPoints.high>>>0).toNumber():e.rejectedDataPoints),null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&(r.errorMessage=e.errorMessage),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess"},e}(),e}(),e}(),collector.logs=function(){var e={};return e.v1=function(){var e={};return e.LogsService=function(){function e(e,t,r){$protobuf.rpc.Service.call(this,e,t,r)}return(e.prototype=Object.create($protobuf.rpc.Service.prototype)).constructor=e,e.create=function(e,t,r){return new this(e,t,r)},Object.defineProperty(e.prototype.export=function e(t,r){return this.rpcCall(e,$root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest,$root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,t,r)},"name",{value:"Export"}),e}(),e.ExportLogsServiceRequest=function(){function e(e){if(this.resourceLogs=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceLogs=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceLogs&&e.resourceLogs.length)for(var r=0;r<e.resourceLogs.length;++r)$root.opentelemetry.proto.logs.v1.ResourceLogs.encode(e.resourceLogs[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceLogs&&n.resourceLogs.length||(n.resourceLogs=[]),n.resourceLogs.push($root.opentelemetry.proto.logs.v1.ResourceLogs.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceLogs&&e.hasOwnProperty("resourceLogs")){if(!Array.isArray(e.resourceLogs))return"resourceLogs: array expected";for(var t=0;t<e.resourceLogs.length;++t){var r=$root.opentelemetry.proto.logs.v1.ResourceLogs.verify(e.resourceLogs[t]);if(r)return"resourceLogs."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest)return e;var t=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;if(e.resourceLogs){if(!Array.isArray(e.resourceLogs))throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: array expected");t.resourceLogs=[];for(var r=0;r<e.resourceLogs.length;++r){if("object"!=typeof e.resourceLogs[r])throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: object expected");t.resourceLogs[r]=$root.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(e.resourceLogs[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceLogs=[]),e.resourceLogs&&e.resourceLogs.length){r.resourceLogs=[];for(var n=0;n<e.resourceLogs.length;++n)r.resourceLogs[n]=$root.opentelemetry.proto.logs.v1.ResourceLogs.toObject(e.resourceLogs[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest"},e}(),e.ExportLogsServiceResponse=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.partialSuccess=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.partialSuccess&&Object.hasOwnProperty.call(e,"partialSuccess")&&$root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.encode(e.partialSuccess,t.uint32(10).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;e.pos<r;){var o=e.uint32();o>>>3==1?n.partialSuccess=$root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.decode(e,e.uint32()):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")){var t=$root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.verify(e.partialSuccess);if(t)return"partialSuccess."+t}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse)return e;var t=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;if(null!=e.partialSuccess){if("object"!=typeof e.partialSuccess)throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse.partialSuccess: object expected");t.partialSuccess=$root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.fromObject(e.partialSuccess)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.partialSuccess=null),null!=e.partialSuccess&&e.hasOwnProperty("partialSuccess")&&(r.partialSuccess=$root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.toObject(e.partialSuccess,t)),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse"},e}(),e.ExportLogsPartialSuccess=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.rejectedLogRecords=null,e.prototype.errorMessage=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.rejectedLogRecords&&Object.hasOwnProperty.call(e,"rejectedLogRecords")&&t.uint32(8).int64(e.rejectedLogRecords),null!=e.errorMessage&&Object.hasOwnProperty.call(e,"errorMessage")&&t.uint32(18).string(e.errorMessage),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.rejectedLogRecords=e.int64();break;case 2:n.errorMessage=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.rejectedLogRecords&&e.hasOwnProperty("rejectedLogRecords")&&!($util.isInteger(e.rejectedLogRecords)||e.rejectedLogRecords&&$util.isInteger(e.rejectedLogRecords.low)&&$util.isInteger(e.rejectedLogRecords.high))?"rejectedLogRecords: integer|Long expected":null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&!$util.isString(e.errorMessage)?"errorMessage: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess)return e;var t=new $root.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;return null!=e.rejectedLogRecords&&($util.Long?(t.rejectedLogRecords=$util.Long.fromValue(e.rejectedLogRecords)).unsigned=!1:"string"==typeof e.rejectedLogRecords?t.rejectedLogRecords=parseInt(e.rejectedLogRecords,10):"number"==typeof e.rejectedLogRecords?t.rejectedLogRecords=e.rejectedLogRecords:"object"==typeof e.rejectedLogRecords&&(t.rejectedLogRecords=new $util.LongBits(e.rejectedLogRecords.low>>>0,e.rejectedLogRecords.high>>>0).toNumber())),null!=e.errorMessage&&(t.errorMessage=String(e.errorMessage)),t},e.toObject=function(e,t){t||(t={});var r={};if(t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.rejectedLogRecords=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.rejectedLogRecords=t.longs===String?"0":0;r.errorMessage=""}return null!=e.rejectedLogRecords&&e.hasOwnProperty("rejectedLogRecords")&&("number"==typeof e.rejectedLogRecords?r.rejectedLogRecords=t.longs===String?String(e.rejectedLogRecords):e.rejectedLogRecords:r.rejectedLogRecords=t.longs===String?$util.Long.prototype.toString.call(e.rejectedLogRecords):t.longs===Number?new $util.LongBits(e.rejectedLogRecords.low>>>0,e.rejectedLogRecords.high>>>0).toNumber():e.rejectedLogRecords),null!=e.errorMessage&&e.hasOwnProperty("errorMessage")&&(r.errorMessage=e.errorMessage),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess"},e}(),e}(),e}(),collector),proto.metrics=function(){var e={};return e.v1=function(){var e,t,r={};return r.MetricsData=function(){function e(e){if(this.resourceMetrics=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceMetrics=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceMetrics&&e.resourceMetrics.length)for(var r=0;r<e.resourceMetrics.length;++r)$root.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(e.resourceMetrics[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.MetricsData;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceMetrics&&n.resourceMetrics.length||(n.resourceMetrics=[]),n.resourceMetrics.push($root.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceMetrics&&e.hasOwnProperty("resourceMetrics")){if(!Array.isArray(e.resourceMetrics))return"resourceMetrics: array expected";for(var t=0;t<e.resourceMetrics.length;++t){var r=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(e.resourceMetrics[t]);if(r)return"resourceMetrics."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.MetricsData)return e;var t=new $root.opentelemetry.proto.metrics.v1.MetricsData;if(e.resourceMetrics){if(!Array.isArray(e.resourceMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: array expected");t.resourceMetrics=[];for(var r=0;r<e.resourceMetrics.length;++r){if("object"!=typeof e.resourceMetrics[r])throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: object expected");t.resourceMetrics[r]=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(e.resourceMetrics[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceMetrics=[]),e.resourceMetrics&&e.resourceMetrics.length){r.resourceMetrics=[];for(var n=0;n<e.resourceMetrics.length;++n)r.resourceMetrics[n]=$root.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(e.resourceMetrics[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.MetricsData"},e}(),r.ResourceMetrics=function(){function e(e){if(this.scopeMetrics=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resource=null,e.prototype.scopeMetrics=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resource&&Object.hasOwnProperty.call(e,"resource")&&$root.opentelemetry.proto.resource.v1.Resource.encode(e.resource,t.uint32(10).fork()).ldelim(),null!=e.scopeMetrics&&e.scopeMetrics.length)for(var r=0;r<e.scopeMetrics.length;++r)$root.opentelemetry.proto.metrics.v1.ScopeMetrics.encode(e.scopeMetrics[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.ResourceMetrics;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.resource=$root.opentelemetry.proto.resource.v1.Resource.decode(e,e.uint32());break;case 2:n.scopeMetrics&&n.scopeMetrics.length||(n.scopeMetrics=[]),n.scopeMetrics.push($root.opentelemetry.proto.metrics.v1.ScopeMetrics.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resource&&e.hasOwnProperty("resource")&&(r=$root.opentelemetry.proto.resource.v1.Resource.verify(e.resource)))return"resource."+r;if(null!=e.scopeMetrics&&e.hasOwnProperty("scopeMetrics")){if(!Array.isArray(e.scopeMetrics))return"scopeMetrics: array expected";for(var t=0;t<e.scopeMetrics.length;++t){var r;if(r=$root.opentelemetry.proto.metrics.v1.ScopeMetrics.verify(e.scopeMetrics[t]))return"scopeMetrics."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.ResourceMetrics)return e;var t=new $root.opentelemetry.proto.metrics.v1.ResourceMetrics;if(null!=e.resource){if("object"!=typeof e.resource)throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.resource: object expected");t.resource=$root.opentelemetry.proto.resource.v1.Resource.fromObject(e.resource)}if(e.scopeMetrics){if(!Array.isArray(e.scopeMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: array expected");t.scopeMetrics=[];for(var r=0;r<e.scopeMetrics.length;++r){if("object"!=typeof e.scopeMetrics[r])throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: object expected");t.scopeMetrics[r]=$root.opentelemetry.proto.metrics.v1.ScopeMetrics.fromObject(e.scopeMetrics[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.scopeMetrics=[]),t.defaults&&(r.resource=null,r.schemaUrl=""),null!=e.resource&&e.hasOwnProperty("resource")&&(r.resource=$root.opentelemetry.proto.resource.v1.Resource.toObject(e.resource,t)),e.scopeMetrics&&e.scopeMetrics.length){r.scopeMetrics=[];for(var n=0;n<e.scopeMetrics.length;++n)r.scopeMetrics[n]=$root.opentelemetry.proto.metrics.v1.ScopeMetrics.toObject(e.scopeMetrics[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.ResourceMetrics"},e}(),r.ScopeMetrics=function(){function e(e){if(this.metrics=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.scope=null,e.prototype.metrics=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.scope&&Object.hasOwnProperty.call(e,"scope")&&$root.opentelemetry.proto.common.v1.InstrumentationScope.encode(e.scope,t.uint32(10).fork()).ldelim(),null!=e.metrics&&e.metrics.length)for(var r=0;r<e.metrics.length;++r)$root.opentelemetry.proto.metrics.v1.Metric.encode(e.metrics[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.ScopeMetrics;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.decode(e,e.uint32());break;case 2:n.metrics&&n.metrics.length||(n.metrics=[]),n.metrics.push($root.opentelemetry.proto.metrics.v1.Metric.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.scope&&e.hasOwnProperty("scope")&&(r=$root.opentelemetry.proto.common.v1.InstrumentationScope.verify(e.scope)))return"scope."+r;if(null!=e.metrics&&e.hasOwnProperty("metrics")){if(!Array.isArray(e.metrics))return"metrics: array expected";for(var t=0;t<e.metrics.length;++t){var r;if(r=$root.opentelemetry.proto.metrics.v1.Metric.verify(e.metrics[t]))return"metrics."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.ScopeMetrics)return e;var t=new $root.opentelemetry.proto.metrics.v1.ScopeMetrics;if(null!=e.scope){if("object"!=typeof e.scope)throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.scope: object expected");t.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(e.scope)}if(e.metrics){if(!Array.isArray(e.metrics))throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: array expected");t.metrics=[];for(var r=0;r<e.metrics.length;++r){if("object"!=typeof e.metrics[r])throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: object expected");t.metrics[r]=$root.opentelemetry.proto.metrics.v1.Metric.fromObject(e.metrics[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.metrics=[]),t.defaults&&(r.scope=null,r.schemaUrl=""),null!=e.scope&&e.hasOwnProperty("scope")&&(r.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.toObject(e.scope,t)),e.metrics&&e.metrics.length){r.metrics=[];for(var n=0;n<e.metrics.length;++n)r.metrics[n]=$root.opentelemetry.proto.metrics.v1.Metric.toObject(e.metrics[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.ScopeMetrics"},e}(),r.Metric=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.name=null,e.prototype.description=null,e.prototype.unit=null,e.prototype.gauge=null,e.prototype.sum=null,e.prototype.histogram=null,e.prototype.exponentialHistogram=null,e.prototype.summary=null,Object.defineProperty(e.prototype,"data",{get:$util.oneOfGetter(t=["gauge","sum","histogram","exponentialHistogram","summary"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.name&&Object.hasOwnProperty.call(e,"name")&&t.uint32(10).string(e.name),null!=e.description&&Object.hasOwnProperty.call(e,"description")&&t.uint32(18).string(e.description),null!=e.unit&&Object.hasOwnProperty.call(e,"unit")&&t.uint32(26).string(e.unit),null!=e.gauge&&Object.hasOwnProperty.call(e,"gauge")&&$root.opentelemetry.proto.metrics.v1.Gauge.encode(e.gauge,t.uint32(42).fork()).ldelim(),null!=e.sum&&Object.hasOwnProperty.call(e,"sum")&&$root.opentelemetry.proto.metrics.v1.Sum.encode(e.sum,t.uint32(58).fork()).ldelim(),null!=e.histogram&&Object.hasOwnProperty.call(e,"histogram")&&$root.opentelemetry.proto.metrics.v1.Histogram.encode(e.histogram,t.uint32(74).fork()).ldelim(),null!=e.exponentialHistogram&&Object.hasOwnProperty.call(e,"exponentialHistogram")&&$root.opentelemetry.proto.metrics.v1.ExponentialHistogram.encode(e.exponentialHistogram,t.uint32(82).fork()).ldelim(),null!=e.summary&&Object.hasOwnProperty.call(e,"summary")&&$root.opentelemetry.proto.metrics.v1.Summary.encode(e.summary,t.uint32(90).fork()).ldelim(),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Metric;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.name=e.string();break;case 2:n.description=e.string();break;case 3:n.unit=e.string();break;case 5:n.gauge=$root.opentelemetry.proto.metrics.v1.Gauge.decode(e,e.uint32());break;case 7:n.sum=$root.opentelemetry.proto.metrics.v1.Sum.decode(e,e.uint32());break;case 9:n.histogram=$root.opentelemetry.proto.metrics.v1.Histogram.decode(e,e.uint32());break;case 10:n.exponentialHistogram=$root.opentelemetry.proto.metrics.v1.ExponentialHistogram.decode(e,e.uint32());break;case 11:n.summary=$root.opentelemetry.proto.metrics.v1.Summary.decode(e,e.uint32());break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";var t={};if(null!=e.name&&e.hasOwnProperty("name")&&!$util.isString(e.name))return"name: string expected";if(null!=e.description&&e.hasOwnProperty("description")&&!$util.isString(e.description))return"description: string expected";if(null!=e.unit&&e.hasOwnProperty("unit")&&!$util.isString(e.unit))return"unit: string expected";if(null!=e.gauge&&e.hasOwnProperty("gauge")&&(t.data=1,r=$root.opentelemetry.proto.metrics.v1.Gauge.verify(e.gauge)))return"gauge."+r;if(null!=e.sum&&e.hasOwnProperty("sum")){if(1===t.data)return"data: multiple values";if(t.data=1,r=$root.opentelemetry.proto.metrics.v1.Sum.verify(e.sum))return"sum."+r}if(null!=e.histogram&&e.hasOwnProperty("histogram")){if(1===t.data)return"data: multiple values";if(t.data=1,r=$root.opentelemetry.proto.metrics.v1.Histogram.verify(e.histogram))return"histogram."+r}if(null!=e.exponentialHistogram&&e.hasOwnProperty("exponentialHistogram")){if(1===t.data)return"data: multiple values";if(t.data=1,r=$root.opentelemetry.proto.metrics.v1.ExponentialHistogram.verify(e.exponentialHistogram))return"exponentialHistogram."+r}if(null!=e.summary&&e.hasOwnProperty("summary")){if(1===t.data)return"data: multiple values";var r;if(t.data=1,r=$root.opentelemetry.proto.metrics.v1.Summary.verify(e.summary))return"summary."+r}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Metric)return e;var t=new $root.opentelemetry.proto.metrics.v1.Metric;if(null!=e.name&&(t.name=String(e.name)),null!=e.description&&(t.description=String(e.description)),null!=e.unit&&(t.unit=String(e.unit)),null!=e.gauge){if("object"!=typeof e.gauge)throw TypeError(".opentelemetry.proto.metrics.v1.Metric.gauge: object expected");t.gauge=$root.opentelemetry.proto.metrics.v1.Gauge.fromObject(e.gauge)}if(null!=e.sum){if("object"!=typeof e.sum)throw TypeError(".opentelemetry.proto.metrics.v1.Metric.sum: object expected");t.sum=$root.opentelemetry.proto.metrics.v1.Sum.fromObject(e.sum)}if(null!=e.histogram){if("object"!=typeof e.histogram)throw TypeError(".opentelemetry.proto.metrics.v1.Metric.histogram: object expected");t.histogram=$root.opentelemetry.proto.metrics.v1.Histogram.fromObject(e.histogram)}if(null!=e.exponentialHistogram){if("object"!=typeof e.exponentialHistogram)throw TypeError(".opentelemetry.proto.metrics.v1.Metric.exponentialHistogram: object expected");t.exponentialHistogram=$root.opentelemetry.proto.metrics.v1.ExponentialHistogram.fromObject(e.exponentialHistogram)}if(null!=e.summary){if("object"!=typeof e.summary)throw TypeError(".opentelemetry.proto.metrics.v1.Metric.summary: object expected");t.summary=$root.opentelemetry.proto.metrics.v1.Summary.fromObject(e.summary)}return t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.name="",r.description="",r.unit=""),null!=e.name&&e.hasOwnProperty("name")&&(r.name=e.name),null!=e.description&&e.hasOwnProperty("description")&&(r.description=e.description),null!=e.unit&&e.hasOwnProperty("unit")&&(r.unit=e.unit),null!=e.gauge&&e.hasOwnProperty("gauge")&&(r.gauge=$root.opentelemetry.proto.metrics.v1.Gauge.toObject(e.gauge,t),t.oneofs&&(r.data="gauge")),null!=e.sum&&e.hasOwnProperty("sum")&&(r.sum=$root.opentelemetry.proto.metrics.v1.Sum.toObject(e.sum,t),t.oneofs&&(r.data="sum")),null!=e.histogram&&e.hasOwnProperty("histogram")&&(r.histogram=$root.opentelemetry.proto.metrics.v1.Histogram.toObject(e.histogram,t),t.oneofs&&(r.data="histogram")),null!=e.exponentialHistogram&&e.hasOwnProperty("exponentialHistogram")&&(r.exponentialHistogram=$root.opentelemetry.proto.metrics.v1.ExponentialHistogram.toObject(e.exponentialHistogram,t),t.oneofs&&(r.data="exponentialHistogram")),null!=e.summary&&e.hasOwnProperty("summary")&&(r.summary=$root.opentelemetry.proto.metrics.v1.Summary.toObject(e.summary,t),t.oneofs&&(r.data="summary")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Metric"},e}(),r.Gauge=function(){function e(e){if(this.dataPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.dataPoints=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.dataPoints&&e.dataPoints.length)for(var r=0;r<e.dataPoints.length;++r)$root.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(e.dataPoints[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Gauge;e.pos<r;){var o=e.uint32();o>>>3==1?(n.dataPoints&&n.dataPoints.length||(n.dataPoints=[]),n.dataPoints.push($root.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.dataPoints&&e.hasOwnProperty("dataPoints")){if(!Array.isArray(e.dataPoints))return"dataPoints: array expected";for(var t=0;t<e.dataPoints.length;++t){var r=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(e.dataPoints[t]);if(r)return"dataPoints."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Gauge)return e;var t=new $root.opentelemetry.proto.metrics.v1.Gauge;if(e.dataPoints){if(!Array.isArray(e.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: array expected");t.dataPoints=[];for(var r=0;r<e.dataPoints.length;++r){if("object"!=typeof e.dataPoints[r])throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: object expected");t.dataPoints[r]=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(e.dataPoints[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.dataPoints=[]),e.dataPoints&&e.dataPoints.length){r.dataPoints=[];for(var n=0;n<e.dataPoints.length;++n)r.dataPoints[n]=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(e.dataPoints[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Gauge"},e}(),r.Sum=function(){function e(e){if(this.dataPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.dataPoints=$util.emptyArray,e.prototype.aggregationTemporality=null,e.prototype.isMonotonic=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.dataPoints&&e.dataPoints.length)for(var r=0;r<e.dataPoints.length;++r)$root.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(e.dataPoints[r],t.uint32(10).fork()).ldelim();return null!=e.aggregationTemporality&&Object.hasOwnProperty.call(e,"aggregationTemporality")&&t.uint32(16).int32(e.aggregationTemporality),null!=e.isMonotonic&&Object.hasOwnProperty.call(e,"isMonotonic")&&t.uint32(24).bool(e.isMonotonic),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Sum;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.dataPoints&&n.dataPoints.length||(n.dataPoints=[]),n.dataPoints.push($root.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(e,e.uint32()));break;case 2:n.aggregationTemporality=e.int32();break;case 3:n.isMonotonic=e.bool();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.dataPoints&&e.hasOwnProperty("dataPoints")){if(!Array.isArray(e.dataPoints))return"dataPoints: array expected";for(var t=0;t<e.dataPoints.length;++t){var r=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(e.dataPoints[t]);if(r)return"dataPoints."+r}}if(null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality"))switch(e.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:}return null!=e.isMonotonic&&e.hasOwnProperty("isMonotonic")&&"boolean"!=typeof e.isMonotonic?"isMonotonic: boolean expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Sum)return e;var t=new $root.opentelemetry.proto.metrics.v1.Sum;if(e.dataPoints){if(!Array.isArray(e.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: array expected");t.dataPoints=[];for(var r=0;r<e.dataPoints.length;++r){if("object"!=typeof e.dataPoints[r])throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: object expected");t.dataPoints[r]=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(e.dataPoints[r])}}switch(e.aggregationTemporality){default:if("number"==typeof e.aggregationTemporality){t.aggregationTemporality=e.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:t.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:t.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:t.aggregationTemporality=2}return null!=e.isMonotonic&&(t.isMonotonic=Boolean(e.isMonotonic)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.dataPoints=[]),t.defaults&&(r.aggregationTemporality=t.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0,r.isMonotonic=!1),e.dataPoints&&e.dataPoints.length){r.dataPoints=[];for(var n=0;n<e.dataPoints.length;++n)r.dataPoints[n]=$root.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(e.dataPoints[n],t)}return null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality")&&(r.aggregationTemporality=t.enums===String?void 0===$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]?e.aggregationTemporality:$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]:e.aggregationTemporality),null!=e.isMonotonic&&e.hasOwnProperty("isMonotonic")&&(r.isMonotonic=e.isMonotonic),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Sum"},e}(),r.Histogram=function(){function e(e){if(this.dataPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.dataPoints=$util.emptyArray,e.prototype.aggregationTemporality=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.dataPoints&&e.dataPoints.length)for(var r=0;r<e.dataPoints.length;++r)$root.opentelemetry.proto.metrics.v1.HistogramDataPoint.encode(e.dataPoints[r],t.uint32(10).fork()).ldelim();return null!=e.aggregationTemporality&&Object.hasOwnProperty.call(e,"aggregationTemporality")&&t.uint32(16).int32(e.aggregationTemporality),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Histogram;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.dataPoints&&n.dataPoints.length||(n.dataPoints=[]),n.dataPoints.push($root.opentelemetry.proto.metrics.v1.HistogramDataPoint.decode(e,e.uint32()));break;case 2:n.aggregationTemporality=e.int32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.dataPoints&&e.hasOwnProperty("dataPoints")){if(!Array.isArray(e.dataPoints))return"dataPoints: array expected";for(var t=0;t<e.dataPoints.length;++t){var r=$root.opentelemetry.proto.metrics.v1.HistogramDataPoint.verify(e.dataPoints[t]);if(r)return"dataPoints."+r}}if(null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality"))switch(e.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Histogram)return e;var t=new $root.opentelemetry.proto.metrics.v1.Histogram;if(e.dataPoints){if(!Array.isArray(e.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: array expected");t.dataPoints=[];for(var r=0;r<e.dataPoints.length;++r){if("object"!=typeof e.dataPoints[r])throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: object expected");t.dataPoints[r]=$root.opentelemetry.proto.metrics.v1.HistogramDataPoint.fromObject(e.dataPoints[r])}}switch(e.aggregationTemporality){default:if("number"==typeof e.aggregationTemporality){t.aggregationTemporality=e.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:t.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:t.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:t.aggregationTemporality=2}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.dataPoints=[]),t.defaults&&(r.aggregationTemporality=t.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0),e.dataPoints&&e.dataPoints.length){r.dataPoints=[];for(var n=0;n<e.dataPoints.length;++n)r.dataPoints[n]=$root.opentelemetry.proto.metrics.v1.HistogramDataPoint.toObject(e.dataPoints[n],t)}return null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality")&&(r.aggregationTemporality=t.enums===String?void 0===$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]?e.aggregationTemporality:$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]:e.aggregationTemporality),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Histogram"},e}(),r.ExponentialHistogram=function(){function e(e){if(this.dataPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.dataPoints=$util.emptyArray,e.prototype.aggregationTemporality=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.dataPoints&&e.dataPoints.length)for(var r=0;r<e.dataPoints.length;++r)$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.encode(e.dataPoints[r],t.uint32(10).fork()).ldelim();return null!=e.aggregationTemporality&&Object.hasOwnProperty.call(e,"aggregationTemporality")&&t.uint32(16).int32(e.aggregationTemporality),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogram;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.dataPoints&&n.dataPoints.length||(n.dataPoints=[]),n.dataPoints.push($root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.decode(e,e.uint32()));break;case 2:n.aggregationTemporality=e.int32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.dataPoints&&e.hasOwnProperty("dataPoints")){if(!Array.isArray(e.dataPoints))return"dataPoints: array expected";for(var t=0;t<e.dataPoints.length;++t){var r=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.verify(e.dataPoints[t]);if(r)return"dataPoints."+r}}if(null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality"))switch(e.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.ExponentialHistogram)return e;var t=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogram;if(e.dataPoints){if(!Array.isArray(e.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: array expected");t.dataPoints=[];for(var r=0;r<e.dataPoints.length;++r){if("object"!=typeof e.dataPoints[r])throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: object expected");t.dataPoints[r]=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.fromObject(e.dataPoints[r])}}switch(e.aggregationTemporality){default:if("number"==typeof e.aggregationTemporality){t.aggregationTemporality=e.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:t.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:t.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:t.aggregationTemporality=2}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.dataPoints=[]),t.defaults&&(r.aggregationTemporality=t.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0),e.dataPoints&&e.dataPoints.length){r.dataPoints=[];for(var n=0;n<e.dataPoints.length;++n)r.dataPoints[n]=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.toObject(e.dataPoints[n],t)}return null!=e.aggregationTemporality&&e.hasOwnProperty("aggregationTemporality")&&(r.aggregationTemporality=t.enums===String?void 0===$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]?e.aggregationTemporality:$root.opentelemetry.proto.metrics.v1.AggregationTemporality[e.aggregationTemporality]:e.aggregationTemporality),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.ExponentialHistogram"},e}(),r.Summary=function(){function e(e){if(this.dataPoints=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.dataPoints=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.dataPoints&&e.dataPoints.length)for(var r=0;r<e.dataPoints.length;++r)$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.encode(e.dataPoints[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Summary;e.pos<r;){var o=e.uint32();o>>>3==1?(n.dataPoints&&n.dataPoints.length||(n.dataPoints=[]),n.dataPoints.push($root.opentelemetry.proto.metrics.v1.SummaryDataPoint.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.dataPoints&&e.hasOwnProperty("dataPoints")){if(!Array.isArray(e.dataPoints))return"dataPoints: array expected";for(var t=0;t<e.dataPoints.length;++t){var r=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.verify(e.dataPoints[t]);if(r)return"dataPoints."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Summary)return e;var t=new $root.opentelemetry.proto.metrics.v1.Summary;if(e.dataPoints){if(!Array.isArray(e.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: array expected");t.dataPoints=[];for(var r=0;r<e.dataPoints.length;++r){if("object"!=typeof e.dataPoints[r])throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: object expected");t.dataPoints[r]=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.fromObject(e.dataPoints[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.dataPoints=[]),e.dataPoints&&e.dataPoints.length){r.dataPoints=[];for(var n=0;n<e.dataPoints.length;++n)r.dataPoints[n]=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.toObject(e.dataPoints[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Summary"},e}(),r.AggregationTemporality=(e={},(t=Object.create(e))[e[0]="AGGREGATION_TEMPORALITY_UNSPECIFIED"]=0,t[e[1]="AGGREGATION_TEMPORALITY_DELTA"]=1,t[e[2]="AGGREGATION_TEMPORALITY_CUMULATIVE"]=2,t),r.DataPointFlags=function(){var e={},t=Object.create(e);return t[e[0]="DATA_POINT_FLAGS_DO_NOT_USE"]=0,t[e[1]="DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK"]=1,t}(),r.NumberDataPoint=function(){function e(e){if(this.attributes=[],this.exemplars=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.attributes=$util.emptyArray,e.prototype.startTimeUnixNano=null,e.prototype.timeUnixNano=null,e.prototype.asDouble=null,e.prototype.asInt=null,e.prototype.exemplars=$util.emptyArray,e.prototype.flags=null,Object.defineProperty(e.prototype,"value",{get:$util.oneOfGetter(t=["asDouble","asInt"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.startTimeUnixNano&&Object.hasOwnProperty.call(e,"startTimeUnixNano")&&t.uint32(17).fixed64(e.startTimeUnixNano),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(25).fixed64(e.timeUnixNano),null!=e.asDouble&&Object.hasOwnProperty.call(e,"asDouble")&&t.uint32(33).double(e.asDouble),null!=e.exemplars&&e.exemplars.length)for(var r=0;r<e.exemplars.length;++r)$root.opentelemetry.proto.metrics.v1.Exemplar.encode(e.exemplars[r],t.uint32(42).fork()).ldelim();if(null!=e.asInt&&Object.hasOwnProperty.call(e,"asInt")&&t.uint32(49).sfixed64(e.asInt),null!=e.attributes&&e.attributes.length)for(r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(58).fork()).ldelim();return null!=e.flags&&Object.hasOwnProperty.call(e,"flags")&&t.uint32(64).uint32(e.flags),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.NumberDataPoint;e.pos<r;){var o=e.uint32();switch(o>>>3){case 7:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.startTimeUnixNano=e.fixed64();break;case 3:n.timeUnixNano=e.fixed64();break;case 4:n.asDouble=e.double();break;case 6:n.asInt=e.sfixed64();break;case 5:n.exemplars&&n.exemplars.length||(n.exemplars=[]),n.exemplars.push($root.opentelemetry.proto.metrics.v1.Exemplar.decode(e,e.uint32()));break;case 8:n.flags=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";var t={};if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var r=0;r<e.attributes.length;++r)if(n=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[r]))return"attributes."+n}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&!($util.isInteger(e.startTimeUnixNano)||e.startTimeUnixNano&&$util.isInteger(e.startTimeUnixNano.low)&&$util.isInteger(e.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.asDouble&&e.hasOwnProperty("asDouble")&&(t.value=1,"number"!=typeof e.asDouble))return"asDouble: number expected";if(null!=e.asInt&&e.hasOwnProperty("asInt")){if(1===t.value)return"value: multiple values";if(t.value=1,!($util.isInteger(e.asInt)||e.asInt&&$util.isInteger(e.asInt.low)&&$util.isInteger(e.asInt.high)))return"asInt: integer|Long expected"}if(null!=e.exemplars&&e.hasOwnProperty("exemplars")){if(!Array.isArray(e.exemplars))return"exemplars: array expected";for(r=0;r<e.exemplars.length;++r){var n;if(n=$root.opentelemetry.proto.metrics.v1.Exemplar.verify(e.exemplars[r]))return"exemplars."+n}}return null!=e.flags&&e.hasOwnProperty("flags")&&!$util.isInteger(e.flags)?"flags: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.NumberDataPoint)return e;var t=new $root.opentelemetry.proto.metrics.v1.NumberDataPoint;if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}if(null!=e.startTimeUnixNano&&($util.Long?(t.startTimeUnixNano=$util.Long.fromValue(e.startTimeUnixNano)).unsigned=!1:"string"==typeof e.startTimeUnixNano?t.startTimeUnixNano=parseInt(e.startTimeUnixNano,10):"number"==typeof e.startTimeUnixNano?t.startTimeUnixNano=e.startTimeUnixNano:"object"==typeof e.startTimeUnixNano&&(t.startTimeUnixNano=new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber())),null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.asDouble&&(t.asDouble=Number(e.asDouble)),null!=e.asInt&&($util.Long?(t.asInt=$util.Long.fromValue(e.asInt)).unsigned=!1:"string"==typeof e.asInt?t.asInt=parseInt(e.asInt,10):"number"==typeof e.asInt?t.asInt=e.asInt:"object"==typeof e.asInt&&(t.asInt=new $util.LongBits(e.asInt.low>>>0,e.asInt.high>>>0).toNumber())),e.exemplars){if(!Array.isArray(e.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: array expected");for(t.exemplars=[],r=0;r<e.exemplars.length;++r){if("object"!=typeof e.exemplars[r])throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: object expected");t.exemplars[r]=$root.opentelemetry.proto.metrics.v1.Exemplar.fromObject(e.exemplars[r])}}return null!=e.flags&&(t.flags=e.flags>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.exemplars=[],r.attributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.startTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTimeUnixNano=t.longs===String?"0":0;$util.Long?(n=new $util.Long(0,0,!1),r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.timeUnixNano=t.longs===String?"0":0,r.flags=0}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&("number"==typeof e.startTimeUnixNano?r.startTimeUnixNano=t.longs===String?String(e.startTimeUnixNano):e.startTimeUnixNano:r.startTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.startTimeUnixNano):t.longs===Number?new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber():e.startTimeUnixNano),null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.asDouble&&e.hasOwnProperty("asDouble")&&(r.asDouble=t.json&&!isFinite(e.asDouble)?String(e.asDouble):e.asDouble,t.oneofs&&(r.value="asDouble")),e.exemplars&&e.exemplars.length){r.exemplars=[];for(var o=0;o<e.exemplars.length;++o)r.exemplars[o]=$root.opentelemetry.proto.metrics.v1.Exemplar.toObject(e.exemplars[o],t)}if(null!=e.asInt&&e.hasOwnProperty("asInt")&&("number"==typeof e.asInt?r.asInt=t.longs===String?String(e.asInt):e.asInt:r.asInt=t.longs===String?$util.Long.prototype.toString.call(e.asInt):t.longs===Number?new $util.LongBits(e.asInt.low>>>0,e.asInt.high>>>0).toNumber():e.asInt,t.oneofs&&(r.value="asInt")),e.attributes&&e.attributes.length)for(r.attributes=[],o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t);return null!=e.flags&&e.hasOwnProperty("flags")&&(r.flags=e.flags),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.NumberDataPoint"},e}(),r.HistogramDataPoint=function(){function e(e){if(this.attributes=[],this.bucketCounts=[],this.explicitBounds=[],this.exemplars=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.attributes=$util.emptyArray,e.prototype.startTimeUnixNano=null,e.prototype.timeUnixNano=null,e.prototype.count=null,e.prototype.sum=null,e.prototype.bucketCounts=$util.emptyArray,e.prototype.explicitBounds=$util.emptyArray,e.prototype.exemplars=$util.emptyArray,e.prototype.flags=null,e.prototype.min=null,e.prototype.max=null,Object.defineProperty(e.prototype,"_sum",{get:$util.oneOfGetter(t=["sum"]),set:$util.oneOfSetter(t)}),Object.defineProperty(e.prototype,"_min",{get:$util.oneOfGetter(t=["min"]),set:$util.oneOfSetter(t)}),Object.defineProperty(e.prototype,"_max",{get:$util.oneOfGetter(t=["max"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.startTimeUnixNano&&Object.hasOwnProperty.call(e,"startTimeUnixNano")&&t.uint32(17).fixed64(e.startTimeUnixNano),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(25).fixed64(e.timeUnixNano),null!=e.count&&Object.hasOwnProperty.call(e,"count")&&t.uint32(33).fixed64(e.count),null!=e.sum&&Object.hasOwnProperty.call(e,"sum")&&t.uint32(41).double(e.sum),null!=e.bucketCounts&&e.bucketCounts.length){t.uint32(50).fork();for(var r=0;r<e.bucketCounts.length;++r)t.fixed64(e.bucketCounts[r]);t.ldelim()}if(null!=e.explicitBounds&&e.explicitBounds.length){for(t.uint32(58).fork(),r=0;r<e.explicitBounds.length;++r)t.double(e.explicitBounds[r]);t.ldelim()}if(null!=e.exemplars&&e.exemplars.length)for(r=0;r<e.exemplars.length;++r)$root.opentelemetry.proto.metrics.v1.Exemplar.encode(e.exemplars[r],t.uint32(66).fork()).ldelim();if(null!=e.attributes&&e.attributes.length)for(r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(74).fork()).ldelim();return null!=e.flags&&Object.hasOwnProperty.call(e,"flags")&&t.uint32(80).uint32(e.flags),null!=e.min&&Object.hasOwnProperty.call(e,"min")&&t.uint32(89).double(e.min),null!=e.max&&Object.hasOwnProperty.call(e,"max")&&t.uint32(97).double(e.max),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.HistogramDataPoint;e.pos<r;){var o=e.uint32();switch(o>>>3){case 9:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.startTimeUnixNano=e.fixed64();break;case 3:n.timeUnixNano=e.fixed64();break;case 4:n.count=e.fixed64();break;case 5:n.sum=e.double();break;case 6:if(n.bucketCounts&&n.bucketCounts.length||(n.bucketCounts=[]),2==(7&o))for(var i=e.uint32()+e.pos;e.pos<i;)n.bucketCounts.push(e.fixed64());else n.bucketCounts.push(e.fixed64());break;case 7:if(n.explicitBounds&&n.explicitBounds.length||(n.explicitBounds=[]),2==(7&o))for(i=e.uint32()+e.pos;e.pos<i;)n.explicitBounds.push(e.double());else n.explicitBounds.push(e.double());break;case 8:n.exemplars&&n.exemplars.length||(n.exemplars=[]),n.exemplars.push($root.opentelemetry.proto.metrics.v1.Exemplar.decode(e,e.uint32()));break;case 10:n.flags=e.uint32();break;case 11:n.min=e.double();break;case 12:n.max=e.double();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t)if(r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]))return"attributes."+r}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&!($util.isInteger(e.startTimeUnixNano)||e.startTimeUnixNano&&$util.isInteger(e.startTimeUnixNano.low)&&$util.isInteger(e.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.count&&e.hasOwnProperty("count")&&!($util.isInteger(e.count)||e.count&&$util.isInteger(e.count.low)&&$util.isInteger(e.count.high)))return"count: integer|Long expected";if(null!=e.sum&&e.hasOwnProperty("sum")&&"number"!=typeof e.sum)return"sum: number expected";if(null!=e.bucketCounts&&e.hasOwnProperty("bucketCounts")){if(!Array.isArray(e.bucketCounts))return"bucketCounts: array expected";for(t=0;t<e.bucketCounts.length;++t)if(!($util.isInteger(e.bucketCounts[t])||e.bucketCounts[t]&&$util.isInteger(e.bucketCounts[t].low)&&$util.isInteger(e.bucketCounts[t].high)))return"bucketCounts: integer|Long[] expected"}if(null!=e.explicitBounds&&e.hasOwnProperty("explicitBounds")){if(!Array.isArray(e.explicitBounds))return"explicitBounds: array expected";for(t=0;t<e.explicitBounds.length;++t)if("number"!=typeof e.explicitBounds[t])return"explicitBounds: number[] expected"}if(null!=e.exemplars&&e.hasOwnProperty("exemplars")){if(!Array.isArray(e.exemplars))return"exemplars: array expected";for(t=0;t<e.exemplars.length;++t){var r;if(r=$root.opentelemetry.proto.metrics.v1.Exemplar.verify(e.exemplars[t]))return"exemplars."+r}}return null!=e.flags&&e.hasOwnProperty("flags")&&!$util.isInteger(e.flags)?"flags: integer expected":null!=e.min&&e.hasOwnProperty("min")&&"number"!=typeof e.min?"min: number expected":null!=e.max&&e.hasOwnProperty("max")&&"number"!=typeof e.max?"max: number expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.HistogramDataPoint)return e;var t=new $root.opentelemetry.proto.metrics.v1.HistogramDataPoint;if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}if(null!=e.startTimeUnixNano&&($util.Long?(t.startTimeUnixNano=$util.Long.fromValue(e.startTimeUnixNano)).unsigned=!1:"string"==typeof e.startTimeUnixNano?t.startTimeUnixNano=parseInt(e.startTimeUnixNano,10):"number"==typeof e.startTimeUnixNano?t.startTimeUnixNano=e.startTimeUnixNano:"object"==typeof e.startTimeUnixNano&&(t.startTimeUnixNano=new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber())),null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.count&&($util.Long?(t.count=$util.Long.fromValue(e.count)).unsigned=!1:"string"==typeof e.count?t.count=parseInt(e.count,10):"number"==typeof e.count?t.count=e.count:"object"==typeof e.count&&(t.count=new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber())),null!=e.sum&&(t.sum=Number(e.sum)),e.bucketCounts){if(!Array.isArray(e.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.bucketCounts: array expected");for(t.bucketCounts=[],r=0;r<e.bucketCounts.length;++r)$util.Long?(t.bucketCounts[r]=$util.Long.fromValue(e.bucketCounts[r])).unsigned=!1:"string"==typeof e.bucketCounts[r]?t.bucketCounts[r]=parseInt(e.bucketCounts[r],10):"number"==typeof e.bucketCounts[r]?t.bucketCounts[r]=e.bucketCounts[r]:"object"==typeof e.bucketCounts[r]&&(t.bucketCounts[r]=new $util.LongBits(e.bucketCounts[r].low>>>0,e.bucketCounts[r].high>>>0).toNumber())}if(e.explicitBounds){if(!Array.isArray(e.explicitBounds))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.explicitBounds: array expected");for(t.explicitBounds=[],r=0;r<e.explicitBounds.length;++r)t.explicitBounds[r]=Number(e.explicitBounds[r])}if(e.exemplars){if(!Array.isArray(e.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: array expected");for(t.exemplars=[],r=0;r<e.exemplars.length;++r){if("object"!=typeof e.exemplars[r])throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: object expected");t.exemplars[r]=$root.opentelemetry.proto.metrics.v1.Exemplar.fromObject(e.exemplars[r])}}return null!=e.flags&&(t.flags=e.flags>>>0),null!=e.min&&(t.min=Number(e.min)),null!=e.max&&(t.max=Number(e.max)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.bucketCounts=[],r.explicitBounds=[],r.exemplars=[],r.attributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.startTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTimeUnixNano=t.longs===String?"0":0;$util.Long?(n=new $util.Long(0,0,!1),r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.timeUnixNano=t.longs===String?"0":0,$util.Long?(n=new $util.Long(0,0,!1),r.count=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.count=t.longs===String?"0":0,r.flags=0}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&("number"==typeof e.startTimeUnixNano?r.startTimeUnixNano=t.longs===String?String(e.startTimeUnixNano):e.startTimeUnixNano:r.startTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.startTimeUnixNano):t.longs===Number?new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber():e.startTimeUnixNano),null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.count&&e.hasOwnProperty("count")&&("number"==typeof e.count?r.count=t.longs===String?String(e.count):e.count:r.count=t.longs===String?$util.Long.prototype.toString.call(e.count):t.longs===Number?new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber():e.count),null!=e.sum&&e.hasOwnProperty("sum")&&(r.sum=t.json&&!isFinite(e.sum)?String(e.sum):e.sum,t.oneofs&&(r._sum="sum")),e.bucketCounts&&e.bucketCounts.length){r.bucketCounts=[];for(var o=0;o<e.bucketCounts.length;++o)"number"==typeof e.bucketCounts[o]?r.bucketCounts[o]=t.longs===String?String(e.bucketCounts[o]):e.bucketCounts[o]:r.bucketCounts[o]=t.longs===String?$util.Long.prototype.toString.call(e.bucketCounts[o]):t.longs===Number?new $util.LongBits(e.bucketCounts[o].low>>>0,e.bucketCounts[o].high>>>0).toNumber():e.bucketCounts[o]}if(e.explicitBounds&&e.explicitBounds.length)for(r.explicitBounds=[],o=0;o<e.explicitBounds.length;++o)r.explicitBounds[o]=t.json&&!isFinite(e.explicitBounds[o])?String(e.explicitBounds[o]):e.explicitBounds[o];if(e.exemplars&&e.exemplars.length)for(r.exemplars=[],o=0;o<e.exemplars.length;++o)r.exemplars[o]=$root.opentelemetry.proto.metrics.v1.Exemplar.toObject(e.exemplars[o],t);if(e.attributes&&e.attributes.length)for(r.attributes=[],o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t);return null!=e.flags&&e.hasOwnProperty("flags")&&(r.flags=e.flags),null!=e.min&&e.hasOwnProperty("min")&&(r.min=t.json&&!isFinite(e.min)?String(e.min):e.min,t.oneofs&&(r._min="min")),null!=e.max&&e.hasOwnProperty("max")&&(r.max=t.json&&!isFinite(e.max)?String(e.max):e.max,t.oneofs&&(r._max="max")),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.HistogramDataPoint"},e}(),r.ExponentialHistogramDataPoint=function(){function e(e){if(this.attributes=[],this.exemplars=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.attributes=$util.emptyArray,e.prototype.startTimeUnixNano=null,e.prototype.timeUnixNano=null,e.prototype.count=null,e.prototype.sum=null,e.prototype.scale=null,e.prototype.zeroCount=null,e.prototype.positive=null,e.prototype.negative=null,e.prototype.flags=null,e.prototype.exemplars=$util.emptyArray,e.prototype.min=null,e.prototype.max=null,e.prototype.zeroThreshold=null,Object.defineProperty(e.prototype,"_sum",{get:$util.oneOfGetter(t=["sum"]),set:$util.oneOfSetter(t)}),Object.defineProperty(e.prototype,"_min",{get:$util.oneOfGetter(t=["min"]),set:$util.oneOfSetter(t)}),Object.defineProperty(e.prototype,"_max",{get:$util.oneOfGetter(t=["max"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(10).fork()).ldelim();if(null!=e.startTimeUnixNano&&Object.hasOwnProperty.call(e,"startTimeUnixNano")&&t.uint32(17).fixed64(e.startTimeUnixNano),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(25).fixed64(e.timeUnixNano),null!=e.count&&Object.hasOwnProperty.call(e,"count")&&t.uint32(33).fixed64(e.count),null!=e.sum&&Object.hasOwnProperty.call(e,"sum")&&t.uint32(41).double(e.sum),null!=e.scale&&Object.hasOwnProperty.call(e,"scale")&&t.uint32(48).sint32(e.scale),null!=e.zeroCount&&Object.hasOwnProperty.call(e,"zeroCount")&&t.uint32(57).fixed64(e.zeroCount),null!=e.positive&&Object.hasOwnProperty.call(e,"positive")&&$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(e.positive,t.uint32(66).fork()).ldelim(),null!=e.negative&&Object.hasOwnProperty.call(e,"negative")&&$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(e.negative,t.uint32(74).fork()).ldelim(),null!=e.flags&&Object.hasOwnProperty.call(e,"flags")&&t.uint32(80).uint32(e.flags),null!=e.exemplars&&e.exemplars.length)for(r=0;r<e.exemplars.length;++r)$root.opentelemetry.proto.metrics.v1.Exemplar.encode(e.exemplars[r],t.uint32(90).fork()).ldelim();return null!=e.min&&Object.hasOwnProperty.call(e,"min")&&t.uint32(97).double(e.min),null!=e.max&&Object.hasOwnProperty.call(e,"max")&&t.uint32(105).double(e.max),null!=e.zeroThreshold&&Object.hasOwnProperty.call(e,"zeroThreshold")&&t.uint32(113).double(e.zeroThreshold),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.startTimeUnixNano=e.fixed64();break;case 3:n.timeUnixNano=e.fixed64();break;case 4:n.count=e.fixed64();break;case 5:n.sum=e.double();break;case 6:n.scale=e.sint32();break;case 7:n.zeroCount=e.fixed64();break;case 8:n.positive=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(e,e.uint32());break;case 9:n.negative=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(e,e.uint32());break;case 10:n.flags=e.uint32();break;case 11:n.exemplars&&n.exemplars.length||(n.exemplars=[]),n.exemplars.push($root.opentelemetry.proto.metrics.v1.Exemplar.decode(e,e.uint32()));break;case 12:n.min=e.double();break;case 13:n.max=e.double();break;case 14:n.zeroThreshold=e.double();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t)if(r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]))return"attributes."+r}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&!($util.isInteger(e.startTimeUnixNano)||e.startTimeUnixNano&&$util.isInteger(e.startTimeUnixNano.low)&&$util.isInteger(e.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.count&&e.hasOwnProperty("count")&&!($util.isInteger(e.count)||e.count&&$util.isInteger(e.count.low)&&$util.isInteger(e.count.high)))return"count: integer|Long expected";if(null!=e.sum&&e.hasOwnProperty("sum")&&"number"!=typeof e.sum)return"sum: number expected";if(null!=e.scale&&e.hasOwnProperty("scale")&&!$util.isInteger(e.scale))return"scale: integer expected";if(null!=e.zeroCount&&e.hasOwnProperty("zeroCount")&&!($util.isInteger(e.zeroCount)||e.zeroCount&&$util.isInteger(e.zeroCount.low)&&$util.isInteger(e.zeroCount.high)))return"zeroCount: integer|Long expected";if(null!=e.positive&&e.hasOwnProperty("positive")&&(r=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(e.positive)))return"positive."+r;if(null!=e.negative&&e.hasOwnProperty("negative")&&(r=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(e.negative)))return"negative."+r;if(null!=e.flags&&e.hasOwnProperty("flags")&&!$util.isInteger(e.flags))return"flags: integer expected";if(null!=e.exemplars&&e.hasOwnProperty("exemplars")){if(!Array.isArray(e.exemplars))return"exemplars: array expected";for(t=0;t<e.exemplars.length;++t){var r;if(r=$root.opentelemetry.proto.metrics.v1.Exemplar.verify(e.exemplars[t]))return"exemplars."+r}}return null!=e.min&&e.hasOwnProperty("min")&&"number"!=typeof e.min?"min: number expected":null!=e.max&&e.hasOwnProperty("max")&&"number"!=typeof e.max?"max: number expected":null!=e.zeroThreshold&&e.hasOwnProperty("zeroThreshold")&&"number"!=typeof e.zeroThreshold?"zeroThreshold: number expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint)return e;var t=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}if(null!=e.startTimeUnixNano&&($util.Long?(t.startTimeUnixNano=$util.Long.fromValue(e.startTimeUnixNano)).unsigned=!1:"string"==typeof e.startTimeUnixNano?t.startTimeUnixNano=parseInt(e.startTimeUnixNano,10):"number"==typeof e.startTimeUnixNano?t.startTimeUnixNano=e.startTimeUnixNano:"object"==typeof e.startTimeUnixNano&&(t.startTimeUnixNano=new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber())),null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.count&&($util.Long?(t.count=$util.Long.fromValue(e.count)).unsigned=!1:"string"==typeof e.count?t.count=parseInt(e.count,10):"number"==typeof e.count?t.count=e.count:"object"==typeof e.count&&(t.count=new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber())),null!=e.sum&&(t.sum=Number(e.sum)),null!=e.scale&&(t.scale=0|e.scale),null!=e.zeroCount&&($util.Long?(t.zeroCount=$util.Long.fromValue(e.zeroCount)).unsigned=!1:"string"==typeof e.zeroCount?t.zeroCount=parseInt(e.zeroCount,10):"number"==typeof e.zeroCount?t.zeroCount=e.zeroCount:"object"==typeof e.zeroCount&&(t.zeroCount=new $util.LongBits(e.zeroCount.low>>>0,e.zeroCount.high>>>0).toNumber())),null!=e.positive){if("object"!=typeof e.positive)throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.positive: object expected");t.positive=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(e.positive)}if(null!=e.negative){if("object"!=typeof e.negative)throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.negative: object expected");t.negative=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(e.negative)}if(null!=e.flags&&(t.flags=e.flags>>>0),e.exemplars){if(!Array.isArray(e.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: array expected");for(t.exemplars=[],r=0;r<e.exemplars.length;++r){if("object"!=typeof e.exemplars[r])throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: object expected");t.exemplars[r]=$root.opentelemetry.proto.metrics.v1.Exemplar.fromObject(e.exemplars[r])}}return null!=e.min&&(t.min=Number(e.min)),null!=e.max&&(t.max=Number(e.max)),null!=e.zeroThreshold&&(t.zeroThreshold=Number(e.zeroThreshold)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[],r.exemplars=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.startTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTimeUnixNano=t.longs===String?"0":0;$util.Long?(n=new $util.Long(0,0,!1),r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.timeUnixNano=t.longs===String?"0":0,$util.Long?(n=new $util.Long(0,0,!1),r.count=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.count=t.longs===String?"0":0,r.scale=0,$util.Long?(n=new $util.Long(0,0,!1),r.zeroCount=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.zeroCount=t.longs===String?"0":0,r.positive=null,r.negative=null,r.flags=0,r.zeroThreshold=0}if(e.attributes&&e.attributes.length){r.attributes=[];for(var o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t)}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&("number"==typeof e.startTimeUnixNano?r.startTimeUnixNano=t.longs===String?String(e.startTimeUnixNano):e.startTimeUnixNano:r.startTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.startTimeUnixNano):t.longs===Number?new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber():e.startTimeUnixNano),null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.count&&e.hasOwnProperty("count")&&("number"==typeof e.count?r.count=t.longs===String?String(e.count):e.count:r.count=t.longs===String?$util.Long.prototype.toString.call(e.count):t.longs===Number?new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber():e.count),null!=e.sum&&e.hasOwnProperty("sum")&&(r.sum=t.json&&!isFinite(e.sum)?String(e.sum):e.sum,t.oneofs&&(r._sum="sum")),null!=e.scale&&e.hasOwnProperty("scale")&&(r.scale=e.scale),null!=e.zeroCount&&e.hasOwnProperty("zeroCount")&&("number"==typeof e.zeroCount?r.zeroCount=t.longs===String?String(e.zeroCount):e.zeroCount:r.zeroCount=t.longs===String?$util.Long.prototype.toString.call(e.zeroCount):t.longs===Number?new $util.LongBits(e.zeroCount.low>>>0,e.zeroCount.high>>>0).toNumber():e.zeroCount),null!=e.positive&&e.hasOwnProperty("positive")&&(r.positive=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(e.positive,t)),null!=e.negative&&e.hasOwnProperty("negative")&&(r.negative=$root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(e.negative,t)),null!=e.flags&&e.hasOwnProperty("flags")&&(r.flags=e.flags),e.exemplars&&e.exemplars.length)for(r.exemplars=[],o=0;o<e.exemplars.length;++o)r.exemplars[o]=$root.opentelemetry.proto.metrics.v1.Exemplar.toObject(e.exemplars[o],t);return null!=e.min&&e.hasOwnProperty("min")&&(r.min=t.json&&!isFinite(e.min)?String(e.min):e.min,t.oneofs&&(r._min="min")),null!=e.max&&e.hasOwnProperty("max")&&(r.max=t.json&&!isFinite(e.max)?String(e.max):e.max,t.oneofs&&(r._max="max")),null!=e.zeroThreshold&&e.hasOwnProperty("zeroThreshold")&&(r.zeroThreshold=t.json&&!isFinite(e.zeroThreshold)?String(e.zeroThreshold):e.zeroThreshold),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint"},e.Buckets=function(){function e(e){if(this.bucketCounts=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.offset=null,e.prototype.bucketCounts=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.offset&&Object.hasOwnProperty.call(e,"offset")&&t.uint32(8).sint32(e.offset),null!=e.bucketCounts&&e.bucketCounts.length){t.uint32(18).fork();for(var r=0;r<e.bucketCounts.length;++r)t.uint64(e.bucketCounts[r]);t.ldelim()}return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.offset=e.sint32();break;case 2:if(n.bucketCounts&&n.bucketCounts.length||(n.bucketCounts=[]),2==(7&o))for(var i=e.uint32()+e.pos;e.pos<i;)n.bucketCounts.push(e.uint64());else n.bucketCounts.push(e.uint64());break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.offset&&e.hasOwnProperty("offset")&&!$util.isInteger(e.offset))return"offset: integer expected";if(null!=e.bucketCounts&&e.hasOwnProperty("bucketCounts")){if(!Array.isArray(e.bucketCounts))return"bucketCounts: array expected";for(var t=0;t<e.bucketCounts.length;++t)if(!($util.isInteger(e.bucketCounts[t])||e.bucketCounts[t]&&$util.isInteger(e.bucketCounts[t].low)&&$util.isInteger(e.bucketCounts[t].high)))return"bucketCounts: integer|Long[] expected"}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets)return e;var t=new $root.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;if(null!=e.offset&&(t.offset=0|e.offset),e.bucketCounts){if(!Array.isArray(e.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.bucketCounts: array expected");t.bucketCounts=[];for(var r=0;r<e.bucketCounts.length;++r)$util.Long?(t.bucketCounts[r]=$util.Long.fromValue(e.bucketCounts[r])).unsigned=!0:"string"==typeof e.bucketCounts[r]?t.bucketCounts[r]=parseInt(e.bucketCounts[r],10):"number"==typeof e.bucketCounts[r]?t.bucketCounts[r]=e.bucketCounts[r]:"object"==typeof e.bucketCounts[r]&&(t.bucketCounts[r]=new $util.LongBits(e.bucketCounts[r].low>>>0,e.bucketCounts[r].high>>>0).toNumber(!0))}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.bucketCounts=[]),t.defaults&&(r.offset=0),null!=e.offset&&e.hasOwnProperty("offset")&&(r.offset=e.offset),e.bucketCounts&&e.bucketCounts.length){r.bucketCounts=[];for(var n=0;n<e.bucketCounts.length;++n)"number"==typeof e.bucketCounts[n]?r.bucketCounts[n]=t.longs===String?String(e.bucketCounts[n]):e.bucketCounts[n]:r.bucketCounts[n]=t.longs===String?$util.Long.prototype.toString.call(e.bucketCounts[n]):t.longs===Number?new $util.LongBits(e.bucketCounts[n].low>>>0,e.bucketCounts[n].high>>>0).toNumber(!0):e.bucketCounts[n]}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets"},e}(),e}(),r.SummaryDataPoint=function(){function e(e){if(this.attributes=[],this.quantileValues=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.attributes=$util.emptyArray,e.prototype.startTimeUnixNano=null,e.prototype.timeUnixNano=null,e.prototype.count=null,e.prototype.sum=null,e.prototype.quantileValues=$util.emptyArray,e.prototype.flags=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.startTimeUnixNano&&Object.hasOwnProperty.call(e,"startTimeUnixNano")&&t.uint32(17).fixed64(e.startTimeUnixNano),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(25).fixed64(e.timeUnixNano),null!=e.count&&Object.hasOwnProperty.call(e,"count")&&t.uint32(33).fixed64(e.count),null!=e.sum&&Object.hasOwnProperty.call(e,"sum")&&t.uint32(41).double(e.sum),null!=e.quantileValues&&e.quantileValues.length)for(var r=0;r<e.quantileValues.length;++r)$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.encode(e.quantileValues[r],t.uint32(50).fork()).ldelim();if(null!=e.attributes&&e.attributes.length)for(r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(58).fork()).ldelim();return null!=e.flags&&Object.hasOwnProperty.call(e,"flags")&&t.uint32(64).uint32(e.flags),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.SummaryDataPoint;e.pos<r;){var o=e.uint32();switch(o>>>3){case 7:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.startTimeUnixNano=e.fixed64();break;case 3:n.timeUnixNano=e.fixed64();break;case 4:n.count=e.fixed64();break;case 5:n.sum=e.double();break;case 6:n.quantileValues&&n.quantileValues.length||(n.quantileValues=[]),n.quantileValues.push($root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.decode(e,e.uint32()));break;case 8:n.flags=e.uint32();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t)if(r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]))return"attributes."+r}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&!($util.isInteger(e.startTimeUnixNano)||e.startTimeUnixNano&&$util.isInteger(e.startTimeUnixNano.low)&&$util.isInteger(e.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.count&&e.hasOwnProperty("count")&&!($util.isInteger(e.count)||e.count&&$util.isInteger(e.count.low)&&$util.isInteger(e.count.high)))return"count: integer|Long expected";if(null!=e.sum&&e.hasOwnProperty("sum")&&"number"!=typeof e.sum)return"sum: number expected";if(null!=e.quantileValues&&e.hasOwnProperty("quantileValues")){if(!Array.isArray(e.quantileValues))return"quantileValues: array expected";for(t=0;t<e.quantileValues.length;++t){var r;if(r=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.verify(e.quantileValues[t]))return"quantileValues."+r}}return null!=e.flags&&e.hasOwnProperty("flags")&&!$util.isInteger(e.flags)?"flags: integer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.SummaryDataPoint)return e;var t=new $root.opentelemetry.proto.metrics.v1.SummaryDataPoint;if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}if(null!=e.startTimeUnixNano&&($util.Long?(t.startTimeUnixNano=$util.Long.fromValue(e.startTimeUnixNano)).unsigned=!1:"string"==typeof e.startTimeUnixNano?t.startTimeUnixNano=parseInt(e.startTimeUnixNano,10):"number"==typeof e.startTimeUnixNano?t.startTimeUnixNano=e.startTimeUnixNano:"object"==typeof e.startTimeUnixNano&&(t.startTimeUnixNano=new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber())),null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.count&&($util.Long?(t.count=$util.Long.fromValue(e.count)).unsigned=!1:"string"==typeof e.count?t.count=parseInt(e.count,10):"number"==typeof e.count?t.count=e.count:"object"==typeof e.count&&(t.count=new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber())),null!=e.sum&&(t.sum=Number(e.sum)),e.quantileValues){if(!Array.isArray(e.quantileValues))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: array expected");for(t.quantileValues=[],r=0;r<e.quantileValues.length;++r){if("object"!=typeof e.quantileValues[r])throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: object expected");t.quantileValues[r]=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.fromObject(e.quantileValues[r])}}return null!=e.flags&&(t.flags=e.flags>>>0),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.quantileValues=[],r.attributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.startTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.startTimeUnixNano=t.longs===String?"0":0;$util.Long?(n=new $util.Long(0,0,!1),r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.timeUnixNano=t.longs===String?"0":0,$util.Long?(n=new $util.Long(0,0,!1),r.count=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.count=t.longs===String?"0":0,r.sum=0,r.flags=0}if(null!=e.startTimeUnixNano&&e.hasOwnProperty("startTimeUnixNano")&&("number"==typeof e.startTimeUnixNano?r.startTimeUnixNano=t.longs===String?String(e.startTimeUnixNano):e.startTimeUnixNano:r.startTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.startTimeUnixNano):t.longs===Number?new $util.LongBits(e.startTimeUnixNano.low>>>0,e.startTimeUnixNano.high>>>0).toNumber():e.startTimeUnixNano),null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.count&&e.hasOwnProperty("count")&&("number"==typeof e.count?r.count=t.longs===String?String(e.count):e.count:r.count=t.longs===String?$util.Long.prototype.toString.call(e.count):t.longs===Number?new $util.LongBits(e.count.low>>>0,e.count.high>>>0).toNumber():e.count),null!=e.sum&&e.hasOwnProperty("sum")&&(r.sum=t.json&&!isFinite(e.sum)?String(e.sum):e.sum),e.quantileValues&&e.quantileValues.length){r.quantileValues=[];for(var o=0;o<e.quantileValues.length;++o)r.quantileValues[o]=$root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.toObject(e.quantileValues[o],t)}if(e.attributes&&e.attributes.length)for(r.attributes=[],o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t);return null!=e.flags&&e.hasOwnProperty("flags")&&(r.flags=e.flags),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.SummaryDataPoint"},e.ValueAtQuantile=function(){function e(e){if(e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.quantile=null,e.prototype.value=null,e.create=function(t){return new e(t)},e.encode=function(e,t){return t||(t=$Writer.create()),null!=e.quantile&&Object.hasOwnProperty.call(e,"quantile")&&t.uint32(9).double(e.quantile),null!=e.value&&Object.hasOwnProperty.call(e,"value")&&t.uint32(17).double(e.value),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.quantile=e.double();break;case 2:n.value=e.double();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){return"object"!=typeof e||null===e?"object expected":null!=e.quantile&&e.hasOwnProperty("quantile")&&"number"!=typeof e.quantile?"quantile: number expected":null!=e.value&&e.hasOwnProperty("value")&&"number"!=typeof e.value?"value: number expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile)return e;var t=new $root.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;return null!=e.quantile&&(t.quantile=Number(e.quantile)),null!=e.value&&(t.value=Number(e.value)),t},e.toObject=function(e,t){t||(t={});var r={};return t.defaults&&(r.quantile=0,r.value=0),null!=e.quantile&&e.hasOwnProperty("quantile")&&(r.quantile=t.json&&!isFinite(e.quantile)?String(e.quantile):e.quantile),null!=e.value&&e.hasOwnProperty("value")&&(r.value=t.json&&!isFinite(e.value)?String(e.value):e.value),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile"},e}(),e}(),r.Exemplar=function(){function e(e){if(this.filteredAttributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}var t;return e.prototype.filteredAttributes=$util.emptyArray,e.prototype.timeUnixNano=null,e.prototype.asDouble=null,e.prototype.asInt=null,e.prototype.spanId=null,e.prototype.traceId=null,Object.defineProperty(e.prototype,"value",{get:$util.oneOfGetter(t=["asDouble","asInt"]),set:$util.oneOfSetter(t)}),e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(17).fixed64(e.timeUnixNano),null!=e.asDouble&&Object.hasOwnProperty.call(e,"asDouble")&&t.uint32(25).double(e.asDouble),null!=e.spanId&&Object.hasOwnProperty.call(e,"spanId")&&t.uint32(34).bytes(e.spanId),null!=e.traceId&&Object.hasOwnProperty.call(e,"traceId")&&t.uint32(42).bytes(e.traceId),null!=e.asInt&&Object.hasOwnProperty.call(e,"asInt")&&t.uint32(49).sfixed64(e.asInt),null!=e.filteredAttributes&&e.filteredAttributes.length)for(var r=0;r<e.filteredAttributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.filteredAttributes[r],t.uint32(58).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.metrics.v1.Exemplar;e.pos<r;){var o=e.uint32();switch(o>>>3){case 7:n.filteredAttributes&&n.filteredAttributes.length||(n.filteredAttributes=[]),n.filteredAttributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 2:n.timeUnixNano=e.fixed64();break;case 3:n.asDouble=e.double();break;case 6:n.asInt=e.sfixed64();break;case 4:n.spanId=e.bytes();break;case 5:n.traceId=e.bytes();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";var t={};if(null!=e.filteredAttributes&&e.hasOwnProperty("filteredAttributes")){if(!Array.isArray(e.filteredAttributes))return"filteredAttributes: array expected";for(var r=0;r<e.filteredAttributes.length;++r){var n=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.filteredAttributes[r]);if(n)return"filteredAttributes."+n}}if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.asDouble&&e.hasOwnProperty("asDouble")&&(t.value=1,"number"!=typeof e.asDouble))return"asDouble: number expected";if(null!=e.asInt&&e.hasOwnProperty("asInt")){if(1===t.value)return"value: multiple values";if(t.value=1,!($util.isInteger(e.asInt)||e.asInt&&$util.isInteger(e.asInt.low)&&$util.isInteger(e.asInt.high)))return"asInt: integer|Long expected"}return null!=e.spanId&&e.hasOwnProperty("spanId")&&!(e.spanId&&"number"==typeof e.spanId.length||$util.isString(e.spanId))?"spanId: buffer expected":null!=e.traceId&&e.hasOwnProperty("traceId")&&!(e.traceId&&"number"==typeof e.traceId.length||$util.isString(e.traceId))?"traceId: buffer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.metrics.v1.Exemplar)return e;var t=new $root.opentelemetry.proto.metrics.v1.Exemplar;if(e.filteredAttributes){if(!Array.isArray(e.filteredAttributes))throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: array expected");t.filteredAttributes=[];for(var r=0;r<e.filteredAttributes.length;++r){if("object"!=typeof e.filteredAttributes[r])throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: object expected");t.filteredAttributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.filteredAttributes[r])}}return null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.asDouble&&(t.asDouble=Number(e.asDouble)),null!=e.asInt&&($util.Long?(t.asInt=$util.Long.fromValue(e.asInt)).unsigned=!1:"string"==typeof e.asInt?t.asInt=parseInt(e.asInt,10):"number"==typeof e.asInt?t.asInt=e.asInt:"object"==typeof e.asInt&&(t.asInt=new $util.LongBits(e.asInt.low>>>0,e.asInt.high>>>0).toNumber())),null!=e.spanId&&("string"==typeof e.spanId?$util.base64.decode(e.spanId,t.spanId=$util.newBuffer($util.base64.length(e.spanId)),0):e.spanId.length>=0&&(t.spanId=e.spanId)),null!=e.traceId&&("string"==typeof e.traceId?$util.base64.decode(e.traceId,t.traceId=$util.newBuffer($util.base64.length(e.traceId)),0):e.traceId.length>=0&&(t.traceId=e.traceId)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.filteredAttributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timeUnixNano=t.longs===String?"0":0;t.bytes===String?r.spanId="":(r.spanId=[],t.bytes!==Array&&(r.spanId=$util.newBuffer(r.spanId))),t.bytes===String?r.traceId="":(r.traceId=[],t.bytes!==Array&&(r.traceId=$util.newBuffer(r.traceId)))}if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.asDouble&&e.hasOwnProperty("asDouble")&&(r.asDouble=t.json&&!isFinite(e.asDouble)?String(e.asDouble):e.asDouble,t.oneofs&&(r.value="asDouble")),null!=e.spanId&&e.hasOwnProperty("spanId")&&(r.spanId=t.bytes===String?$util.base64.encode(e.spanId,0,e.spanId.length):t.bytes===Array?Array.prototype.slice.call(e.spanId):e.spanId),null!=e.traceId&&e.hasOwnProperty("traceId")&&(r.traceId=t.bytes===String?$util.base64.encode(e.traceId,0,e.traceId.length):t.bytes===Array?Array.prototype.slice.call(e.traceId):e.traceId),null!=e.asInt&&e.hasOwnProperty("asInt")&&("number"==typeof e.asInt?r.asInt=t.longs===String?String(e.asInt):e.asInt:r.asInt=t.longs===String?$util.Long.prototype.toString.call(e.asInt):t.longs===Number?new $util.LongBits(e.asInt.low>>>0,e.asInt.high>>>0).toNumber():e.asInt,t.oneofs&&(r.value="asInt")),e.filteredAttributes&&e.filteredAttributes.length){r.filteredAttributes=[];for(var o=0;o<e.filteredAttributes.length;++o)r.filteredAttributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.filteredAttributes[o],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.metrics.v1.Exemplar"},e}(),r}(),e}(),proto.logs=function(){var e={};return e.v1=function(){var e,t,r={};return r.LogsData=function(){function e(e){if(this.resourceLogs=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resourceLogs=$util.emptyArray,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resourceLogs&&e.resourceLogs.length)for(var r=0;r<e.resourceLogs.length;++r)$root.opentelemetry.proto.logs.v1.ResourceLogs.encode(e.resourceLogs[r],t.uint32(10).fork()).ldelim();return t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.logs.v1.LogsData;e.pos<r;){var o=e.uint32();o>>>3==1?(n.resourceLogs&&n.resourceLogs.length||(n.resourceLogs=[]),n.resourceLogs.push($root.opentelemetry.proto.logs.v1.ResourceLogs.decode(e,e.uint32()))):e.skipType(7&o)}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resourceLogs&&e.hasOwnProperty("resourceLogs")){if(!Array.isArray(e.resourceLogs))return"resourceLogs: array expected";for(var t=0;t<e.resourceLogs.length;++t){var r=$root.opentelemetry.proto.logs.v1.ResourceLogs.verify(e.resourceLogs[t]);if(r)return"resourceLogs."+r}}return null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.logs.v1.LogsData)return e;var t=new $root.opentelemetry.proto.logs.v1.LogsData;if(e.resourceLogs){if(!Array.isArray(e.resourceLogs))throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: array expected");t.resourceLogs=[];for(var r=0;r<e.resourceLogs.length;++r){if("object"!=typeof e.resourceLogs[r])throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: object expected");t.resourceLogs[r]=$root.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(e.resourceLogs[r])}}return t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.resourceLogs=[]),e.resourceLogs&&e.resourceLogs.length){r.resourceLogs=[];for(var n=0;n<e.resourceLogs.length;++n)r.resourceLogs[n]=$root.opentelemetry.proto.logs.v1.ResourceLogs.toObject(e.resourceLogs[n],t)}return r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.logs.v1.LogsData"},e}(),r.ResourceLogs=function(){function e(e){if(this.scopeLogs=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.resource=null,e.prototype.scopeLogs=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.resource&&Object.hasOwnProperty.call(e,"resource")&&$root.opentelemetry.proto.resource.v1.Resource.encode(e.resource,t.uint32(10).fork()).ldelim(),null!=e.scopeLogs&&e.scopeLogs.length)for(var r=0;r<e.scopeLogs.length;++r)$root.opentelemetry.proto.logs.v1.ScopeLogs.encode(e.scopeLogs[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.logs.v1.ResourceLogs;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.resource=$root.opentelemetry.proto.resource.v1.Resource.decode(e,e.uint32());break;case 2:n.scopeLogs&&n.scopeLogs.length||(n.scopeLogs=[]),n.scopeLogs.push($root.opentelemetry.proto.logs.v1.ScopeLogs.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.resource&&e.hasOwnProperty("resource")&&(r=$root.opentelemetry.proto.resource.v1.Resource.verify(e.resource)))return"resource."+r;if(null!=e.scopeLogs&&e.hasOwnProperty("scopeLogs")){if(!Array.isArray(e.scopeLogs))return"scopeLogs: array expected";for(var t=0;t<e.scopeLogs.length;++t){var r;if(r=$root.opentelemetry.proto.logs.v1.ScopeLogs.verify(e.scopeLogs[t]))return"scopeLogs."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.logs.v1.ResourceLogs)return e;var t=new $root.opentelemetry.proto.logs.v1.ResourceLogs;if(null!=e.resource){if("object"!=typeof e.resource)throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.resource: object expected");t.resource=$root.opentelemetry.proto.resource.v1.Resource.fromObject(e.resource)}if(e.scopeLogs){if(!Array.isArray(e.scopeLogs))throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: array expected");t.scopeLogs=[];for(var r=0;r<e.scopeLogs.length;++r){if("object"!=typeof e.scopeLogs[r])throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: object expected");t.scopeLogs[r]=$root.opentelemetry.proto.logs.v1.ScopeLogs.fromObject(e.scopeLogs[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.scopeLogs=[]),t.defaults&&(r.resource=null,r.schemaUrl=""),null!=e.resource&&e.hasOwnProperty("resource")&&(r.resource=$root.opentelemetry.proto.resource.v1.Resource.toObject(e.resource,t)),e.scopeLogs&&e.scopeLogs.length){r.scopeLogs=[];for(var n=0;n<e.scopeLogs.length;++n)r.scopeLogs[n]=$root.opentelemetry.proto.logs.v1.ScopeLogs.toObject(e.scopeLogs[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.logs.v1.ResourceLogs"},e}(),r.ScopeLogs=function(){function e(e){if(this.logRecords=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.scope=null,e.prototype.logRecords=$util.emptyArray,e.prototype.schemaUrl=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.scope&&Object.hasOwnProperty.call(e,"scope")&&$root.opentelemetry.proto.common.v1.InstrumentationScope.encode(e.scope,t.uint32(10).fork()).ldelim(),null!=e.logRecords&&e.logRecords.length)for(var r=0;r<e.logRecords.length;++r)$root.opentelemetry.proto.logs.v1.LogRecord.encode(e.logRecords[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&Object.hasOwnProperty.call(e,"schemaUrl")&&t.uint32(26).string(e.schemaUrl),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.logs.v1.ScopeLogs;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.decode(e,e.uint32());break;case 2:n.logRecords&&n.logRecords.length||(n.logRecords=[]),n.logRecords.push($root.opentelemetry.proto.logs.v1.LogRecord.decode(e,e.uint32()));break;case 3:n.schemaUrl=e.string();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.scope&&e.hasOwnProperty("scope")&&(r=$root.opentelemetry.proto.common.v1.InstrumentationScope.verify(e.scope)))return"scope."+r;if(null!=e.logRecords&&e.hasOwnProperty("logRecords")){if(!Array.isArray(e.logRecords))return"logRecords: array expected";for(var t=0;t<e.logRecords.length;++t){var r;if(r=$root.opentelemetry.proto.logs.v1.LogRecord.verify(e.logRecords[t]))return"logRecords."+r}}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&!$util.isString(e.schemaUrl)?"schemaUrl: string expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.logs.v1.ScopeLogs)return e;var t=new $root.opentelemetry.proto.logs.v1.ScopeLogs;if(null!=e.scope){if("object"!=typeof e.scope)throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.scope: object expected");t.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(e.scope)}if(e.logRecords){if(!Array.isArray(e.logRecords))throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: array expected");t.logRecords=[];for(var r=0;r<e.logRecords.length;++r){if("object"!=typeof e.logRecords[r])throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: object expected");t.logRecords[r]=$root.opentelemetry.proto.logs.v1.LogRecord.fromObject(e.logRecords[r])}}return null!=e.schemaUrl&&(t.schemaUrl=String(e.schemaUrl)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.logRecords=[]),t.defaults&&(r.scope=null,r.schemaUrl=""),null!=e.scope&&e.hasOwnProperty("scope")&&(r.scope=$root.opentelemetry.proto.common.v1.InstrumentationScope.toObject(e.scope,t)),e.logRecords&&e.logRecords.length){r.logRecords=[];for(var n=0;n<e.logRecords.length;++n)r.logRecords[n]=$root.opentelemetry.proto.logs.v1.LogRecord.toObject(e.logRecords[n],t)}return null!=e.schemaUrl&&e.hasOwnProperty("schemaUrl")&&(r.schemaUrl=e.schemaUrl),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.logs.v1.ScopeLogs"},e}(),r.SeverityNumber=(e={},(t=Object.create(e))[e[0]="SEVERITY_NUMBER_UNSPECIFIED"]=0,t[e[1]="SEVERITY_NUMBER_TRACE"]=1,t[e[2]="SEVERITY_NUMBER_TRACE2"]=2,t[e[3]="SEVERITY_NUMBER_TRACE3"]=3,t[e[4]="SEVERITY_NUMBER_TRACE4"]=4,t[e[5]="SEVERITY_NUMBER_DEBUG"]=5,t[e[6]="SEVERITY_NUMBER_DEBUG2"]=6,t[e[7]="SEVERITY_NUMBER_DEBUG3"]=7,t[e[8]="SEVERITY_NUMBER_DEBUG4"]=8,t[e[9]="SEVERITY_NUMBER_INFO"]=9,t[e[10]="SEVERITY_NUMBER_INFO2"]=10,t[e[11]="SEVERITY_NUMBER_INFO3"]=11,t[e[12]="SEVERITY_NUMBER_INFO4"]=12,t[e[13]="SEVERITY_NUMBER_WARN"]=13,t[e[14]="SEVERITY_NUMBER_WARN2"]=14,t[e[15]="SEVERITY_NUMBER_WARN3"]=15,t[e[16]="SEVERITY_NUMBER_WARN4"]=16,t[e[17]="SEVERITY_NUMBER_ERROR"]=17,t[e[18]="SEVERITY_NUMBER_ERROR2"]=18,t[e[19]="SEVERITY_NUMBER_ERROR3"]=19,t[e[20]="SEVERITY_NUMBER_ERROR4"]=20,t[e[21]="SEVERITY_NUMBER_FATAL"]=21,t[e[22]="SEVERITY_NUMBER_FATAL2"]=22,t[e[23]="SEVERITY_NUMBER_FATAL3"]=23,t[e[24]="SEVERITY_NUMBER_FATAL4"]=24,t),r.LogRecordFlags=function(){var e={},t=Object.create(e);return t[e[0]="LOG_RECORD_FLAGS_DO_NOT_USE"]=0,t[e[255]="LOG_RECORD_FLAGS_TRACE_FLAGS_MASK"]=255,t}(),r.LogRecord=function(){function e(e){if(this.attributes=[],e)for(var t=Object.keys(e),r=0;r<t.length;++r)null!=e[t[r]]&&(this[t[r]]=e[t[r]])}return e.prototype.timeUnixNano=null,e.prototype.observedTimeUnixNano=null,e.prototype.severityNumber=null,e.prototype.severityText=null,e.prototype.body=null,e.prototype.attributes=$util.emptyArray,e.prototype.droppedAttributesCount=null,e.prototype.flags=null,e.prototype.traceId=null,e.prototype.spanId=null,e.create=function(t){return new e(t)},e.encode=function(e,t){if(t||(t=$Writer.create()),null!=e.timeUnixNano&&Object.hasOwnProperty.call(e,"timeUnixNano")&&t.uint32(9).fixed64(e.timeUnixNano),null!=e.severityNumber&&Object.hasOwnProperty.call(e,"severityNumber")&&t.uint32(16).int32(e.severityNumber),null!=e.severityText&&Object.hasOwnProperty.call(e,"severityText")&&t.uint32(26).string(e.severityText),null!=e.body&&Object.hasOwnProperty.call(e,"body")&&$root.opentelemetry.proto.common.v1.AnyValue.encode(e.body,t.uint32(42).fork()).ldelim(),null!=e.attributes&&e.attributes.length)for(var r=0;r<e.attributes.length;++r)$root.opentelemetry.proto.common.v1.KeyValue.encode(e.attributes[r],t.uint32(50).fork()).ldelim();return null!=e.droppedAttributesCount&&Object.hasOwnProperty.call(e,"droppedAttributesCount")&&t.uint32(56).uint32(e.droppedAttributesCount),null!=e.flags&&Object.hasOwnProperty.call(e,"flags")&&t.uint32(69).fixed32(e.flags),null!=e.traceId&&Object.hasOwnProperty.call(e,"traceId")&&t.uint32(74).bytes(e.traceId),null!=e.spanId&&Object.hasOwnProperty.call(e,"spanId")&&t.uint32(82).bytes(e.spanId),null!=e.observedTimeUnixNano&&Object.hasOwnProperty.call(e,"observedTimeUnixNano")&&t.uint32(89).fixed64(e.observedTimeUnixNano),t},e.encodeDelimited=function(e,t){return this.encode(e,t).ldelim()},e.decode=function(e,t){e instanceof $Reader||(e=$Reader.create(e));for(var r=void 0===t?e.len:e.pos+t,n=new $root.opentelemetry.proto.logs.v1.LogRecord;e.pos<r;){var o=e.uint32();switch(o>>>3){case 1:n.timeUnixNano=e.fixed64();break;case 11:n.observedTimeUnixNano=e.fixed64();break;case 2:n.severityNumber=e.int32();break;case 3:n.severityText=e.string();break;case 5:n.body=$root.opentelemetry.proto.common.v1.AnyValue.decode(e,e.uint32());break;case 6:n.attributes&&n.attributes.length||(n.attributes=[]),n.attributes.push($root.opentelemetry.proto.common.v1.KeyValue.decode(e,e.uint32()));break;case 7:n.droppedAttributesCount=e.uint32();break;case 8:n.flags=e.fixed32();break;case 9:n.traceId=e.bytes();break;case 10:n.spanId=e.bytes();break;default:e.skipType(7&o)}}return n},e.decodeDelimited=function(e){return e instanceof $Reader||(e=new $Reader(e)),this.decode(e,e.uint32())},e.verify=function(e){if("object"!=typeof e||null===e)return"object expected";if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&!($util.isInteger(e.timeUnixNano)||e.timeUnixNano&&$util.isInteger(e.timeUnixNano.low)&&$util.isInteger(e.timeUnixNano.high)))return"timeUnixNano: integer|Long expected";if(null!=e.observedTimeUnixNano&&e.hasOwnProperty("observedTimeUnixNano")&&!($util.isInteger(e.observedTimeUnixNano)||e.observedTimeUnixNano&&$util.isInteger(e.observedTimeUnixNano.low)&&$util.isInteger(e.observedTimeUnixNano.high)))return"observedTimeUnixNano: integer|Long expected";if(null!=e.severityNumber&&e.hasOwnProperty("severityNumber"))switch(e.severityNumber){default:return"severityNumber: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:case 24:}if(null!=e.severityText&&e.hasOwnProperty("severityText")&&!$util.isString(e.severityText))return"severityText: string expected";if(null!=e.body&&e.hasOwnProperty("body")&&(r=$root.opentelemetry.proto.common.v1.AnyValue.verify(e.body)))return"body."+r;if(null!=e.attributes&&e.hasOwnProperty("attributes")){if(!Array.isArray(e.attributes))return"attributes: array expected";for(var t=0;t<e.attributes.length;++t){var r;if(r=$root.opentelemetry.proto.common.v1.KeyValue.verify(e.attributes[t]))return"attributes."+r}}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&!$util.isInteger(e.droppedAttributesCount)?"droppedAttributesCount: integer expected":null!=e.flags&&e.hasOwnProperty("flags")&&!$util.isInteger(e.flags)?"flags: integer expected":null!=e.traceId&&e.hasOwnProperty("traceId")&&!(e.traceId&&"number"==typeof e.traceId.length||$util.isString(e.traceId))?"traceId: buffer expected":null!=e.spanId&&e.hasOwnProperty("spanId")&&!(e.spanId&&"number"==typeof e.spanId.length||$util.isString(e.spanId))?"spanId: buffer expected":null},e.fromObject=function(e){if(e instanceof $root.opentelemetry.proto.logs.v1.LogRecord)return e;var t=new $root.opentelemetry.proto.logs.v1.LogRecord;switch(null!=e.timeUnixNano&&($util.Long?(t.timeUnixNano=$util.Long.fromValue(e.timeUnixNano)).unsigned=!1:"string"==typeof e.timeUnixNano?t.timeUnixNano=parseInt(e.timeUnixNano,10):"number"==typeof e.timeUnixNano?t.timeUnixNano=e.timeUnixNano:"object"==typeof e.timeUnixNano&&(t.timeUnixNano=new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber())),null!=e.observedTimeUnixNano&&($util.Long?(t.observedTimeUnixNano=$util.Long.fromValue(e.observedTimeUnixNano)).unsigned=!1:"string"==typeof e.observedTimeUnixNano?t.observedTimeUnixNano=parseInt(e.observedTimeUnixNano,10):"number"==typeof e.observedTimeUnixNano?t.observedTimeUnixNano=e.observedTimeUnixNano:"object"==typeof e.observedTimeUnixNano&&(t.observedTimeUnixNano=new $util.LongBits(e.observedTimeUnixNano.low>>>0,e.observedTimeUnixNano.high>>>0).toNumber())),e.severityNumber){default:if("number"==typeof e.severityNumber){t.severityNumber=e.severityNumber;break}break;case"SEVERITY_NUMBER_UNSPECIFIED":case 0:t.severityNumber=0;break;case"SEVERITY_NUMBER_TRACE":case 1:t.severityNumber=1;break;case"SEVERITY_NUMBER_TRACE2":case 2:t.severityNumber=2;break;case"SEVERITY_NUMBER_TRACE3":case 3:t.severityNumber=3;break;case"SEVERITY_NUMBER_TRACE4":case 4:t.severityNumber=4;break;case"SEVERITY_NUMBER_DEBUG":case 5:t.severityNumber=5;break;case"SEVERITY_NUMBER_DEBUG2":case 6:t.severityNumber=6;break;case"SEVERITY_NUMBER_DEBUG3":case 7:t.severityNumber=7;break;case"SEVERITY_NUMBER_DEBUG4":case 8:t.severityNumber=8;break;case"SEVERITY_NUMBER_INFO":case 9:t.severityNumber=9;break;case"SEVERITY_NUMBER_INFO2":case 10:t.severityNumber=10;break;case"SEVERITY_NUMBER_INFO3":case 11:t.severityNumber=11;break;case"SEVERITY_NUMBER_INFO4":case 12:t.severityNumber=12;break;case"SEVERITY_NUMBER_WARN":case 13:t.severityNumber=13;break;case"SEVERITY_NUMBER_WARN2":case 14:t.severityNumber=14;break;case"SEVERITY_NUMBER_WARN3":case 15:t.severityNumber=15;break;case"SEVERITY_NUMBER_WARN4":case 16:t.severityNumber=16;break;case"SEVERITY_NUMBER_ERROR":case 17:t.severityNumber=17;break;case"SEVERITY_NUMBER_ERROR2":case 18:t.severityNumber=18;break;case"SEVERITY_NUMBER_ERROR3":case 19:t.severityNumber=19;break;case"SEVERITY_NUMBER_ERROR4":case 20:t.severityNumber=20;break;case"SEVERITY_NUMBER_FATAL":case 21:t.severityNumber=21;break;case"SEVERITY_NUMBER_FATAL2":case 22:t.severityNumber=22;break;case"SEVERITY_NUMBER_FATAL3":case 23:t.severityNumber=23;break;case"SEVERITY_NUMBER_FATAL4":case 24:t.severityNumber=24}if(null!=e.severityText&&(t.severityText=String(e.severityText)),null!=e.body){if("object"!=typeof e.body)throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.body: object expected");t.body=$root.opentelemetry.proto.common.v1.AnyValue.fromObject(e.body)}if(e.attributes){if(!Array.isArray(e.attributes))throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: array expected");t.attributes=[];for(var r=0;r<e.attributes.length;++r){if("object"!=typeof e.attributes[r])throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: object expected");t.attributes[r]=$root.opentelemetry.proto.common.v1.KeyValue.fromObject(e.attributes[r])}}return null!=e.droppedAttributesCount&&(t.droppedAttributesCount=e.droppedAttributesCount>>>0),null!=e.flags&&(t.flags=e.flags>>>0),null!=e.traceId&&("string"==typeof e.traceId?$util.base64.decode(e.traceId,t.traceId=$util.newBuffer($util.base64.length(e.traceId)),0):e.traceId.length>=0&&(t.traceId=e.traceId)),null!=e.spanId&&("string"==typeof e.spanId?$util.base64.decode(e.spanId,t.spanId=$util.newBuffer($util.base64.length(e.spanId)),0):e.spanId.length>=0&&(t.spanId=e.spanId)),t},e.toObject=function(e,t){t||(t={});var r={};if((t.arrays||t.defaults)&&(r.attributes=[]),t.defaults){if($util.Long){var n=new $util.Long(0,0,!1);r.timeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n}else r.timeUnixNano=t.longs===String?"0":0;r.severityNumber=t.enums===String?"SEVERITY_NUMBER_UNSPECIFIED":0,r.severityText="",r.body=null,r.droppedAttributesCount=0,r.flags=0,t.bytes===String?r.traceId="":(r.traceId=[],t.bytes!==Array&&(r.traceId=$util.newBuffer(r.traceId))),t.bytes===String?r.spanId="":(r.spanId=[],t.bytes!==Array&&(r.spanId=$util.newBuffer(r.spanId))),$util.Long?(n=new $util.Long(0,0,!1),r.observedTimeUnixNano=t.longs===String?n.toString():t.longs===Number?n.toNumber():n):r.observedTimeUnixNano=t.longs===String?"0":0}if(null!=e.timeUnixNano&&e.hasOwnProperty("timeUnixNano")&&("number"==typeof e.timeUnixNano?r.timeUnixNano=t.longs===String?String(e.timeUnixNano):e.timeUnixNano:r.timeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.timeUnixNano):t.longs===Number?new $util.LongBits(e.timeUnixNano.low>>>0,e.timeUnixNano.high>>>0).toNumber():e.timeUnixNano),null!=e.severityNumber&&e.hasOwnProperty("severityNumber")&&(r.severityNumber=t.enums===String?void 0===$root.opentelemetry.proto.logs.v1.SeverityNumber[e.severityNumber]?e.severityNumber:$root.opentelemetry.proto.logs.v1.SeverityNumber[e.severityNumber]:e.severityNumber),null!=e.severityText&&e.hasOwnProperty("severityText")&&(r.severityText=e.severityText),null!=e.body&&e.hasOwnProperty("body")&&(r.body=$root.opentelemetry.proto.common.v1.AnyValue.toObject(e.body,t)),e.attributes&&e.attributes.length){r.attributes=[];for(var o=0;o<e.attributes.length;++o)r.attributes[o]=$root.opentelemetry.proto.common.v1.KeyValue.toObject(e.attributes[o],t)}return null!=e.droppedAttributesCount&&e.hasOwnProperty("droppedAttributesCount")&&(r.droppedAttributesCount=e.droppedAttributesCount),null!=e.flags&&e.hasOwnProperty("flags")&&(r.flags=e.flags),null!=e.traceId&&e.hasOwnProperty("traceId")&&(r.traceId=t.bytes===String?$util.base64.encode(e.traceId,0,e.traceId.length):t.bytes===Array?Array.prototype.slice.call(e.traceId):e.traceId),null!=e.spanId&&e.hasOwnProperty("spanId")&&(r.spanId=t.bytes===String?$util.base64.encode(e.spanId,0,e.spanId.length):t.bytes===Array?Array.prototype.slice.call(e.spanId):e.spanId),null!=e.observedTimeUnixNano&&e.hasOwnProperty("observedTimeUnixNano")&&("number"==typeof e.observedTimeUnixNano?r.observedTimeUnixNano=t.longs===String?String(e.observedTimeUnixNano):e.observedTimeUnixNano:r.observedTimeUnixNano=t.longs===String?$util.Long.prototype.toString.call(e.observedTimeUnixNano):t.longs===Number?new $util.LongBits(e.observedTimeUnixNano.low>>>0,e.observedTimeUnixNano.high>>>0).toNumber():e.observedTimeUnixNano),r},e.prototype.toJSON=function(){return this.constructor.toObject(this,$protobuf.util.toJSONOptions)},e.getTypeUrl=function(e){return void 0===e&&(e="type.googleapis.com"),e+"/opentelemetry.proto.logs.v1.LogRecord"},e}(),r}(),e}(),proto),opentelemetry);var root=$root;root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,root.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest,root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,root.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;var traceResponseType=root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,traceRequestType=root.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest,ProtobufTraceSerializer={serializeRequest:function(e){var t=createExportTraceServiceRequest(e);return traceRequestType.encode(t).finish()},deserializeResponse:function(e){return traceResponseType.decode(e)}},__extends$4=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),DEFAULT_COLLECTOR_RESOURCE_PATH="v1/traces",OTLPTraceExporter=function(e){function t(t){return void 0===t&&(t={}),e.call(this,t,ProtobufTraceSerializer,{"Content-Type":"application/x-protobuf"},DEFAULT_COLLECTOR_RESOURCE_PATH)||this}return __extends$4(t,e),t}(OTLPExporterBrowserBase),EventNames$1;!function(e){e.METHOD_OPEN="open",e.METHOD_SEND="send",e.EVENT_ABORT="abort",e.EVENT_ERROR="error",e.EVENT_LOAD="loaded",e.EVENT_TIMEOUT="timeout"}(EventNames$1||(EventNames$1={}));var __values$1=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},__read$2=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},DIAG_LOGGER$1=diag.createComponentLogger({namespace:"@opentelemetry/opentelemetry-instrumentation-xml-http-request/utils"});function getXHRBodyLength$1(e){return"undefined"!=typeof Document&&e instanceof Document?(new XMLSerializer).serializeToString(document).length:e instanceof Blob?e.size:void 0!==e.byteLength?e.byteLength:e instanceof FormData?getFormDataSize$1(e):e instanceof URLSearchParams?getByteLength$1(e.toString()):"string"==typeof e?getByteLength$1(e):void DIAG_LOGGER$1.warn("unknown body type")}var TEXT_ENCODER$1=new TextEncoder;function getByteLength$1(e){return TEXT_ENCODER$1.encode(e).byteLength}function getFormDataSize$1(e){var t,r,n=0;try{for(var o=__values$1(e.entries()),i=o.next();!i.done;i=o.next()){var a=__read$2(i.value,2),s=a[0],u=a[1];n+=s.length,u instanceof Blob?n+=u.size:n+=u.length}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return n}var VERSION$1="0.55.0",AttributeNames$3;!function(e){e.HTTP_STATUS_TEXT="http.status_text"}(AttributeNames$3||(AttributeNames$3={}));var __extends$3=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),OBSERVER_WAIT_TIME_MS$1=300,XMLHttpRequestInstrumentation=function(e){function t(t){void 0===t&&(t={});var r=e.call(this,"@opentelemetry/instrumentation-xml-http-request",VERSION$1,t)||this;return r.component="xml-http-request",r.version=VERSION$1,r.moduleName=r.component,r._tasksCount=0,r._xhrMem=new WeakMap,r._usedResources=new WeakSet,r}return __extends$3(t,e),t.prototype.init=function(){},t.prototype._addHeaders=function(e,t){if(!shouldPropagateTraceHeaders(parseUrl(t).href,this.getConfig().propagateTraceHeaderCorsUrls)){var r={};return propagation.inject(context.active(),r),void(Object.keys(r).length>0&&this._diag.debug("headers inject skipped due to CORS policy"))}var n={};propagation.inject(context.active(),n),Object.keys(n).forEach((function(t){e.setRequestHeader(t,String(n[t]))}))},t.prototype._addChildSpan=function(e,t){var r=this;context.with(trace.setSpan(context.active(),e),(function(){var e=r.tracer.startSpan("CORS Preflight",{startTime:t[PerformanceTimingNames.FETCH_START]});r.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(e,t),e.end(t[PerformanceTimingNames.RESPONSE_END])}))},t.prototype._addFinalSpanAttributes=function(e,t,r){if("string"==typeof r){var n=parseUrl(r);void 0!==t.status&&e.setAttribute(SEMATTRS_HTTP_STATUS_CODE,t.status),void 0!==t.statusText&&e.setAttribute(AttributeNames$3.HTTP_STATUS_TEXT,t.statusText),e.setAttribute(SEMATTRS_HTTP_HOST,n.host),e.setAttribute(SEMATTRS_HTTP_SCHEME,n.protocol.replace(":","")),e.setAttribute(SEMATTRS_HTTP_USER_AGENT,navigator.userAgent)}},t.prototype._applyAttributesAfterXHR=function(e,t){var r=this,n=this.getConfig().applyCustomAttributesOnSpan;"function"==typeof n&&safeExecuteInTheMiddle((function(){return n(e,t)}),(function(e){e&&r._diag.error("applyCustomAttributesOnSpan",e)}),!0)},t.prototype._addResourceObserver=function(e,t){var r=this._xhrMem.get(e);r&&"function"==typeof PerformanceObserver&&"function"==typeof PerformanceResourceTiming&&(r.createdResources={observer:new PerformanceObserver((function(e){var n=e.getEntries(),o=parseUrl(t);n.forEach((function(e){"xmlhttprequest"===e.initiatorType&&e.name===o.href&&r.createdResources&&r.createdResources.entries.push(e)}))})),entries:[]},r.createdResources.observer.observe({entryTypes:["resource"]}))},t.prototype._clearResources=function(){0===this._tasksCount&&this.getConfig().clearTimingResources&&(otperformance.clearResourceTimings(),this._xhrMem=new WeakMap,this._usedResources=new WeakSet)},t.prototype._findResourceAndAddNetworkEvents=function(e,t,r,n,o){if(r&&n&&o&&e.createdResources){var i=e.createdResources.entries;i&&i.length||(i=otperformance.getEntriesByType("resource"));var a=getResource(parseUrl(r).href,n,o,i,this._usedResources);if(a.mainRequest){var s=a.mainRequest;this._markResourceAsUsed(s);var u=a.corsPreFlightRequest;u&&(this._addChildSpan(t,u),this._markResourceAsUsed(u)),this.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(t,s)}}},t.prototype._cleanPreviousSpanInformation=function(e){var t=this._xhrMem.get(e);if(t){var r=t.callbackToRemoveEvents;r&&r(),this._xhrMem.delete(e)}},t.prototype._createSpan=function(e,t,r){var n;if(!isUrlIgnored(t,this.getConfig().ignoreUrls)){var o=r.toUpperCase(),i=this.tracer.startSpan(o,{kind:SpanKind.CLIENT,attributes:(n={},n[SEMATTRS_HTTP_METHOD]=r,n[SEMATTRS_HTTP_URL]=parseUrl(t).toString(),n)});return i.addEvent(EventNames$1.METHOD_OPEN),this._cleanPreviousSpanInformation(e),this._xhrMem.set(e,{span:i,spanUrl:t}),i}this._diag.debug("ignoring span as url matches ignored url")},t.prototype._markResourceAsUsed=function(e){this._usedResources.add(e)},t.prototype._patchOpen=function(){var e=this;return function(t){var r=e;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var o=e[0],i=e[1];return r._createSpan(this,i,o),t.apply(this,e)}}},t.prototype._patchSend=function(){var e=this;function t(t,r){var n=e._xhrMem.get(r);if(n){n.status=r.status,n.statusText=r.statusText,e._xhrMem.delete(r),n.span&&e._applyAttributesAfterXHR(n.span,r);var o=hrTime(),i=Date.now();setTimeout((function(){!function(t,r,n,o){var i=r.callbackToRemoveEvents;"function"==typeof i&&i();var a=r.span,s=r.spanUrl,u=r.sendStartTime;a&&(e._findResourceAndAddNetworkEvents(r,a,s,u,n),a.addEvent(t,o),e._addFinalSpanAttributes(a,r,s),a.end(o),e._tasksCount--),e._clearResources()}(t,n,o,i)}),OBSERVER_WAIT_TIME_MS$1)}}function r(){t(EventNames$1.EVENT_ERROR,this)}function n(){t(EventNames$1.EVENT_ABORT,this)}function o(){t(EventNames$1.EVENT_TIMEOUT,this)}function i(){this.status<299?t(EventNames$1.EVENT_LOAD,this):t(EventNames$1.EVENT_ERROR,this)}return function(t){return function(){for(var a=this,s=[],u=0;u<arguments.length;u++)s[u]=arguments[u];var c=e._xhrMem.get(this);if(!c)return t.apply(this,s);var l=c.span,p=c.spanUrl;if(l&&p){if(e.getConfig().measureRequestSize&&(null==s?void 0:s[0])){var f=getXHRBodyLength$1(s[0]);void 0!==f&&l.setAttribute(SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,f)}context.with(trace.setSpan(context.active(),l),(function(){e._tasksCount++,c.sendStartTime=hrTime(),l.addEvent(EventNames$1.METHOD_SEND),a.addEventListener("abort",n),a.addEventListener("error",r),a.addEventListener("load",i),a.addEventListener("timeout",o),c.callbackToRemoveEvents=function(){!function(t){t.removeEventListener("abort",n),t.removeEventListener("error",r),t.removeEventListener("load",i),t.removeEventListener("timeout",o);var a=e._xhrMem.get(t);a&&(a.callbackToRemoveEvents=void 0)}(a),c.createdResources&&c.createdResources.observer.disconnect()},e._addHeaders(a,p),e._addResourceObserver(a,p)}))}return t.apply(this,s)}}},t.prototype.enable=function(){this._diag.debug("applying patch to",this.moduleName,this.version),isWrapped(XMLHttpRequest.prototype.open)&&(this._unwrap(XMLHttpRequest.prototype,"open"),this._diag.debug("removing previous patch from method open")),isWrapped(XMLHttpRequest.prototype.send)&&(this._unwrap(XMLHttpRequest.prototype,"send"),this._diag.debug("removing previous patch from method send")),this._wrap(XMLHttpRequest.prototype,"open",this._patchOpen()),this._wrap(XMLHttpRequest.prototype,"send",this._patchSend())},t.prototype.disable=function(){this._diag.debug("removing patch from",this.moduleName,this.version),this._unwrap(XMLHttpRequest.prototype,"open"),this._unwrap(XMLHttpRequest.prototype,"send"),this._tasksCount=0,this._xhrMem=new WeakMap,this._usedResources=new WeakSet},t}(InstrumentationBase),AttributeNames$2;!function(e){e.COMPONENT="component",e.HTTP_ERROR_NAME="http.error_name",e.HTTP_STATUS_TEXT="http.status_text"}(AttributeNames$2||(AttributeNames$2={}));var __awaiter=window&&window.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){e.done?o(e.value):function(e){return e instanceof r?e:new r((function(t){t(e)}))}(e.value).then(a,s)}u((n=n.apply(e,t||[])).next())}))},__generator=window&&window.__generator||function(e,t){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},__values=window&&window.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},__read$1=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},DIAG_LOGGER=diag.createComponentLogger({namespace:"@opentelemetry/opentelemetry-instrumentation-fetch/utils"});function getFetchBodyLength(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(e[0]instanceof URL||"string"==typeof e[0]){var r=e[1];if(!(null==r?void 0:r.body))return Promise.resolve();if(r.body instanceof ReadableStream){var n=_getBodyNonDestructively(r.body),o=n.body,i=n.length;return r.body=o,i}return Promise.resolve(getXHRBodyLength(r.body))}var a=e[0];return(null==a?void 0:a.body)?a.clone().text().then((function(e){return getByteLength(e)})):Promise.resolve()}function _getBodyNonDestructively(e){if(!e.pipeThrough)return DIAG_LOGGER.warn("Platform has ReadableStream but not pipeThrough!"),{body:e,length:Promise.resolve(void 0)};var t,r=0,n=new Promise((function(e){t=e})),o=new TransformStream({start:function(){},transform:function(e,t){return __awaiter(this,void 0,void 0,(function(){var n;return __generator(this,(function(o){switch(o.label){case 0:return[4,e];case 1:return n=o.sent(),r+=n.byteLength,t.enqueue(e),[2]}}))}))},flush:function(){t(r)}});return{body:e.pipeThrough(o),length:n}}function getXHRBodyLength(e){return"undefined"!=typeof Document&&e instanceof Document?(new XMLSerializer).serializeToString(document).length:e instanceof Blob?e.size:void 0!==e.byteLength?e.byteLength:e instanceof FormData?getFormDataSize(e):e instanceof URLSearchParams?getByteLength(e.toString()):"string"==typeof e?getByteLength(e):void DIAG_LOGGER.warn("unknown body type")}var TEXT_ENCODER=new TextEncoder;function getByteLength(e){return TEXT_ENCODER.encode(e).byteLength}function getFormDataSize(e){var t,r,n=0;try{for(var o=__values(e.entries()),i=o.next();!i.done;i=o.next()){var a=__read$1(i.value,2),s=a[0],u=a[1];n+=s.length,u instanceof Blob?n+=u.size:n+=u.length}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return n}var VERSION="0.55.0",__extends$2=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),__read=window&&window.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},__spreadArray=window&&window.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))},_a,OBSERVER_WAIT_TIME_MS=300,isNode$1="object"==typeof process&&"node"===(null===(_a=process.release)||void 0===_a?void 0:_a.name),FetchInstrumentation=function(e){function t(t){void 0===t&&(t={});var r=e.call(this,"@opentelemetry/instrumentation-fetch",VERSION,t)||this;return r.component="fetch",r.version=VERSION,r.moduleName=r.component,r._usedResources=new WeakSet,r._tasksCount=0,r}return __extends$2(t,e),t.prototype.init=function(){},t.prototype._addChildSpan=function(e,t){var r=this.tracer.startSpan("CORS Preflight",{startTime:t[PerformanceTimingNames.FETCH_START]},trace.setSpan(context.active(),e));this.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(r,t),r.end(t[PerformanceTimingNames.RESPONSE_END])},t.prototype._addFinalSpanAttributes=function(e,t){var r=parseUrl(t.url);e.setAttribute(SEMATTRS_HTTP_STATUS_CODE,t.status),null!=t.statusText&&e.setAttribute(AttributeNames$2.HTTP_STATUS_TEXT,t.statusText),e.setAttribute(SEMATTRS_HTTP_HOST,r.host),e.setAttribute(SEMATTRS_HTTP_SCHEME,r.protocol.replace(":","")),"undefined"!=typeof navigator&&e.setAttribute(SEMATTRS_HTTP_USER_AGENT,navigator.userAgent)},t.prototype._addHeaders=function(e,t){if(!shouldPropagateTraceHeaders(t,this.getConfig().propagateTraceHeaderCorsUrls)){var r={};return propagation.inject(context.active(),r),void(Object.keys(r).length>0&&this._diag.debug("headers inject skipped due to CORS policy"))}if(e instanceof Request)propagation.inject(context.active(),e.headers,{set:function(e,t,r){return e.set(t,"string"==typeof r?r:String(r))}});else if(e.headers instanceof Headers)propagation.inject(context.active(),e.headers,{set:function(e,t,r){return e.set(t,"string"==typeof r?r:String(r))}});else if(e.headers instanceof Map)propagation.inject(context.active(),e.headers,{set:function(e,t,r){return e.set(t,"string"==typeof r?r:String(r))}});else{r={};propagation.inject(context.active(),r),e.headers=Object.assign({},r,e.headers||{})}},t.prototype._clearResources=function(){0===this._tasksCount&&this.getConfig().clearTimingResources&&(performance.clearResourceTimings(),this._usedResources=new WeakSet)},t.prototype._createSpan=function(e,t){var r;if(void 0===t&&(t={}),!isUrlIgnored(e,this.getConfig().ignoreUrls)){var n=(t.method||"GET").toUpperCase(),o="HTTP "+n;return this.tracer.startSpan(o,{kind:SpanKind.CLIENT,attributes:(r={},r[AttributeNames$2.COMPONENT]=this.moduleName,r[SEMATTRS_HTTP_METHOD]=n,r[SEMATTRS_HTTP_URL]=e,r)})}this._diag.debug("ignoring span as url matches ignored url")},t.prototype._findResourceAndAddNetworkEvents=function(e,t,r){var n=t.entries;if(!n.length){if(!performance.getEntriesByType)return;n=performance.getEntriesByType("resource")}var o=getResource(t.spanUrl,t.startTime,r,n,this._usedResources,"fetch");if(o.mainRequest){var i=o.mainRequest;this._markResourceAsUsed(i);var a=o.corsPreFlightRequest;a&&(this._addChildSpan(e,a),this._markResourceAsUsed(a)),this.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(e,i)}},t.prototype._markResourceAsUsed=function(e){this._usedResources.add(e)},t.prototype._endSpan=function(e,t,r){var n=this,o=millisToHrTime(Date.now()),i=hrTime();this._addFinalSpanAttributes(e,r),setTimeout((function(){var r;null===(r=t.observer)||void 0===r||r.disconnect(),n._findResourceAndAddNetworkEvents(e,t,i),n._tasksCount--,n._clearResources(),e.end(o)}),OBSERVER_WAIT_TIME_MS)},t.prototype._patchConstructor=function(){var e=this;return function(t){var r=e;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var o=this,i=parseUrl(e[0]instanceof Request?e[0].url:String(e[0])).href,a=e[0]instanceof Request?e[0]:e[1]||{},s=r._createSpan(i,a);if(!s)return t.apply(this,e);var u=r._prepareSpanData(i);function c(e,t){r._applyAttributesAfterFetch(e,a,t),r._endSpan(e,u,{status:t.status||0,statusText:t.message,url:i})}function l(e,t){r._applyAttributesAfterFetch(e,a,t),t.status>=200&&t.status<400?r._endSpan(e,u,t):r._endSpan(e,u,{status:t.status,statusText:t.statusText,url:i})}function p(e,t,r){try{var n=r.clone(),o=r.clone(),i=n.body;if(i){var a=i.getReader(),s=function(){a.read().then((function(t){t.done?l(e,o):s()}),(function(t){c(e,t)}))};s()}else l(e,r)}finally{t(r)}}function f(e,t,r){try{c(e,r)}finally{t(r)}}return r.getConfig().measureRequestSize&&getFetchBodyLength.apply(void 0,__spreadArray([],__read(e),!1)).then((function(e){e&&s.setAttribute(SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,e)})).catch((function(e){r._diag.warn("getFetchBodyLength",e)})),new Promise((function(e,n){return context.with(trace.setSpan(context.active(),s),(function(){return r._addHeaders(a,i),r._tasksCount++,t.apply(o,a instanceof Request?[a]:[i,a]).then(p.bind(o,s,e),f.bind(o,s,n))}))}))}}},t.prototype._applyAttributesAfterFetch=function(e,t,r){var n=this,o=this.getConfig().applyCustomAttributesOnSpan;o&&safeExecuteInTheMiddle((function(){return o(e,t,r)}),(function(e){e&&n._diag.error("applyCustomAttributesOnSpan",e)}),!0)},t.prototype._prepareSpanData=function(e){var t=hrTime(),r=[];if("function"!=typeof PerformanceObserver)return{entries:r,startTime:t,spanUrl:e};var n=new PerformanceObserver((function(t){t.getEntries().forEach((function(t){"fetch"===t.initiatorType&&t.name===e&&r.push(t)}))}));return n.observe({entryTypes:["resource"]}),{entries:r,observer:n,startTime:t,spanUrl:e}},t.prototype.enable=function(){isNode$1?this._diag.warn("this instrumentation is intended for web usage only, it does not instrument Node.js's fetch()"):(isWrapped(fetch)&&(this._unwrap(_globalThis$1,"fetch"),this._diag.debug("removing previous patch for constructor")),this._wrap(_globalThis$1,"fetch",this._patchConstructor()))},t.prototype.disable=function(){isNode$1||(this._unwrap(_globalThis$1,"fetch"),this._usedResources=new WeakSet)},t}(InstrumentationBase),AttributeNames$1;!function(e){e.EVENT_TYPE="event_type",e.TARGET_ELEMENT="target_element",e.TARGET_XPATH="target_xpath",e.HTTP_URL="http.url"}(AttributeNames$1||(AttributeNames$1={}));var PACKAGE_VERSION$1="0.42.0",PACKAGE_NAME$1="@opentelemetry/instrumentation-user-interaction",__extends$1=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ZONE_CONTEXT_KEY$1="OT_ZONE_CONTEXT",EVENT_NAVIGATION_NAME="Navigation:",DEFAULT_EVENT_NAMES=["click"];function defaultShouldPreventSpanCreation(){return!1}var UserInteractionInstrumentation=function(e){function t(t){var r;void 0===t&&(t={});var n=e.call(this,PACKAGE_NAME$1,PACKAGE_VERSION$1,t)||this;return n.version=PACKAGE_VERSION$1,n.moduleName="user-interaction",n._spansData=new WeakMap,n._wrappedListeners=new WeakMap,n._eventsSpanMap=new WeakMap,n._eventNames=new Set(null!==(r=null==t?void 0:t.eventNames)&&void 0!==r?r:DEFAULT_EVENT_NAMES),n._shouldPreventSpanCreation="function"==typeof(null==t?void 0:t.shouldPreventSpanCreation)?t.shouldPreventSpanCreation:defaultShouldPreventSpanCreation,n}return __extends$1(t,e),t.prototype.init=function(){},t.prototype._checkForTimeout=function(e,t){var r=this._spansData.get(t);r&&("setTimeout"===e.source?r.hrTimeLastTimeout=hrTime():"Promise.then"!==e.source&&"setTimeout"!==e.source&&(r.hrTimeLastTimeout=void 0))},t.prototype._allowEventName=function(e){return this._eventNames.has(e)},t.prototype._createSpan=function(e,t,r){var n;if(e instanceof HTMLElement&&e.getAttribute&&!e.hasAttribute("disabled")&&this._allowEventName(t)){var o=getElementXPath(e,!0);try{var i=this.tracer.startSpan(t,{attributes:(n={},n[AttributeNames$1.EVENT_TYPE]=t,n[AttributeNames$1.TARGET_ELEMENT]=e.tagName,n[AttributeNames$1.TARGET_XPATH]=o,n[AttributeNames$1.HTTP_URL]=window.location.href,n)},r?trace.setSpan(context.active(),r):void 0);if(!0===this._shouldPreventSpanCreation(t,e,i))return;return this._spansData.set(i,{taskCount:0}),i}catch(e){this._diag.error("failed to start create new user interaction span",e)}}},t.prototype._decrementTask=function(e){var t=this._spansData.get(e);t&&(t.taskCount--,0===t.taskCount&&this._tryToEndSpan(e,t.hrTimeLastTimeout))},t.prototype._getCurrentSpan=function(e){var t=e.get(ZONE_CONTEXT_KEY$1);return t?trace.getSpan(t):t},t.prototype._incrementTask=function(e){var t=this._spansData.get(e);t&&t.taskCount++},t.prototype.addPatchedListener=function(e,t,r,n){var o=this._wrappedListeners.get(r);o||(o=new Map,this._wrappedListeners.set(r,o));var i=o.get(t);return i||(i=new Map,o.set(t,i)),!i.has(e)&&(i.set(e,n),!0)},t.prototype.removePatchedListener=function(e,t,r){var n=this._wrappedListeners.get(r);if(n){var o=n.get(t);if(o){var i=o.get(e);return i&&(o.delete(e),0===o.size&&(n.delete(t),0===n.size&&this._wrappedListeners.delete(r))),i}}},t.prototype._invokeListener=function(e,t,r){return"function"==typeof e?e.apply(t,r):e.handleEvent(r[0])},t.prototype._patchAddEventListener=function(){var e=this;return function(t){return function(r,n,o){if(!n)return t.call(this,r,n,o);var i=o&&"object"==typeof o&&o.once,a=function(){for(var t,o=this,a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];var u=a[0],c=null==u?void 0:u.target;u&&(t=e._eventsSpanMap.get(u)),i&&e.removePatchedListener(this,r,n);var l=e._createSpan(c,r,t);return l?(u&&e._eventsSpanMap.set(u,l),context.with(trace.setSpan(context.active(),l),(function(){var t=e._invokeListener(n,o,a);return l.end(),t}))):e._invokeListener(n,this,a)};return e.addPatchedListener(this,r,n,a)?t.call(this,r,a,o):void 0}}},t.prototype._patchRemoveEventListener=function(){var e=this;return function(t){return function(r,n,o){var i=e.removePatchedListener(this,r,n);return i?t.call(this,r,i,o):t.call(this,r,n,o)}}},t.prototype._getPatchableEventTargets=function(){return window.EventTarget?[EventTarget.prototype]:[Node.prototype,Window.prototype]},t.prototype._patchHistoryApi=function(){this._unpatchHistoryApi(),this._wrap(history,"replaceState",this._patchHistoryMethod()),this._wrap(history,"pushState",this._patchHistoryMethod()),this._wrap(history,"back",this._patchHistoryMethod()),this._wrap(history,"forward",this._patchHistoryMethod()),this._wrap(history,"go",this._patchHistoryMethod())},t.prototype._patchHistoryMethod=function(){var e=this;return function(t){return function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var o=""+location.pathname+location.hash+location.search,i=t.apply(this,r),a=""+location.pathname+location.hash+location.search;return o!==a&&e._updateInteractionName(a),i}}},t.prototype._unpatchHistoryApi=function(){isWrapped(history.replaceState)&&this._unwrap(history,"replaceState"),isWrapped(history.pushState)&&this._unwrap(history,"pushState"),isWrapped(history.back)&&this._unwrap(history,"back"),isWrapped(history.forward)&&this._unwrap(history,"forward"),isWrapped(history.go)&&this._unwrap(history,"go")},t.prototype._updateInteractionName=function(e){var t=trace.getSpan(context.active());t&&"function"==typeof t.updateName&&t.updateName(EVENT_NAVIGATION_NAME+" "+e)},t.prototype._patchZoneCancelTask=function(){var e=this;return function(t){return function(r){var n=Zone.current,o=e._getCurrentSpan(n);return o&&e._shouldCountTask(r,n)&&e._decrementTask(o),t.call(this,r)}}},t.prototype._patchZoneScheduleTask=function(){var e=this;return function(t){return function(r){var n=Zone.current,o=e._getCurrentSpan(n);return o&&e._shouldCountTask(r,n)&&(e._incrementTask(o),e._checkForTimeout(r,o)),t.call(this,r)}}},t.prototype._patchZoneRunTask=function(){var e=this;return function(t){return function(r,n,o){var i,a=Array.isArray(o)&&o[0]instanceof Event?o[0]:void 0,s=null==a?void 0:a.target,u=this;if(s){if(i=e._createSpan(s,r.eventName))return e._incrementTask(i),u.run((function(){try{return context.with(trace.setSpan(context.active(),i),(function(){var e=Zone.current;return r._zone=e,t.call(e,r,n,o)}))}finally{e._decrementTask(i)}}))}else i=e._getCurrentSpan(u);try{return t.call(u,r,n,o)}finally{i&&e._shouldCountTask(r,u)&&e._decrementTask(i)}}}},t.prototype._shouldCountTask=function(e,t){if(e._zone&&(t=e._zone),!t||!e.data||e.data.isPeriodic)return!1;var r=this._getCurrentSpan(t);return!!r&&(!!this._spansData.get(r)&&("macroTask"===e.type||"microTask"===e.type))},t.prototype._tryToEndSpan=function(e,t){e&&(this._spansData.get(e)&&(e.end(t),this._spansData.delete(e)))},t.prototype.enable=function(){var e=this,t=this.getZoneWithPrototype();(this._diag.debug("applying patch to",this.moduleName,this.version,"zone:",!!t),t)?(isWrapped(t.prototype.runTask)&&(this._unwrap(t.prototype,"runTask"),this._diag.debug("removing previous patch from method runTask")),isWrapped(t.prototype.scheduleTask)&&(this._unwrap(t.prototype,"scheduleTask"),this._diag.debug("removing previous patch from method scheduleTask")),isWrapped(t.prototype.cancelTask)&&(this._unwrap(t.prototype,"cancelTask"),this._diag.debug("removing previous patch from method cancelTask")),this._zonePatched=!0,this._wrap(t.prototype,"runTask",this._patchZoneRunTask()),this._wrap(t.prototype,"scheduleTask",this._patchZoneScheduleTask()),this._wrap(t.prototype,"cancelTask",this._patchZoneCancelTask())):(this._zonePatched=!1,this._getPatchableEventTargets().forEach((function(t){isWrapped(t.addEventListener)&&(e._unwrap(t,"addEventListener"),e._diag.debug("removing previous patch from method addEventListener")),isWrapped(t.removeEventListener)&&(e._unwrap(t,"removeEventListener"),e._diag.debug("removing previous patch from method removeEventListener")),e._wrap(t,"addEventListener",e._patchAddEventListener()),e._wrap(t,"removeEventListener",e._patchRemoveEventListener())})));this._patchHistoryApi()},t.prototype.disable=function(){var e=this,t=this.getZoneWithPrototype();(this._diag.debug("removing patch from",this.moduleName,this.version,"zone:",!!t),t&&this._zonePatched)?(isWrapped(t.prototype.runTask)&&this._unwrap(t.prototype,"runTask"),isWrapped(t.prototype.scheduleTask)&&this._unwrap(t.prototype,"scheduleTask"),isWrapped(t.prototype.cancelTask)&&this._unwrap(t.prototype,"cancelTask")):this._getPatchableEventTargets().forEach((function(t){isWrapped(t.addEventListener)&&e._unwrap(t,"addEventListener"),isWrapped(t.removeEventListener)&&e._unwrap(t,"removeEventListener")}));this._unpatchHistoryApi()},t.prototype.getZoneWithPrototype=function(){return window.Zone},t}(InstrumentationBase),AttributeNames;!function(e){e.DOCUMENT_LOAD="documentLoad",e.DOCUMENT_FETCH="documentFetch",e.RESOURCE_FETCH="resourceFetch"}(AttributeNames||(AttributeNames={}));var PACKAGE_VERSION="0.42.0",PACKAGE_NAME="@opentelemetry/instrumentation-document-load",EventNames;!function(e){e.FIRST_PAINT="firstPaint",e.FIRST_CONTENTFUL_PAINT="firstContentfulPaint"}(EventNames||(EventNames={}));var getPerformanceNavigationEntries=function(){var e,t,r={},n=null===(t=(e=otperformance).getEntriesByType)||void 0===t?void 0:t.call(e,"navigation")[0];if(n){Object.values(PerformanceTimingNames).forEach((function(e){if(hasKey(n,e)){var t=n[e];"number"==typeof t&&(r[e]=t)}}))}else{var o=otperformance.timing;if(o)Object.values(PerformanceTimingNames).forEach((function(e){if(hasKey(o,e)){var t=o[e];"number"==typeof t&&(r[e]=t)}}))}return r},performancePaintNames={"first-paint":EventNames.FIRST_PAINT,"first-contentful-paint":EventNames.FIRST_CONTENTFUL_PAINT},addSpanPerformancePaintEvents=function(e){var t,r,n=null===(r=(t=otperformance).getEntriesByType)||void 0===r?void 0:r.call(t,"paint");n&&n.forEach((function(t){var r=t.name,n=t.startTime;hasKey(performancePaintNames,r)&&e.addEvent(performancePaintNames[r],n)}))},__extends=window&&window.__extends||function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),DocumentLoadInstrumentation=function(e){function t(t){void 0===t&&(t={});var r=e.call(this,PACKAGE_NAME,PACKAGE_VERSION,t)||this;return r.component="document-load",r.version="1",r.moduleName=r.component,r}return __extends(t,e),t.prototype.init=function(){},t.prototype._onDocumentLoaded=function(){var e=this;window.setTimeout((function(){e._collectPerformance()}))},t.prototype._addResourcesSpans=function(e){var t,r,n=this,o=null===(r=(t=otperformance).getEntriesByType)||void 0===r?void 0:r.call(t,"resource");o&&o.forEach((function(t){n._initResourceSpan(t,e)}))},t.prototype._collectPerformance=function(){var e=this,t=Array.from(document.getElementsByTagName("meta")).find((function(e){return e.getAttribute("name")===TRACE_PARENT_HEADER})),r=getPerformanceNavigationEntries(),n=t&&t.content||"";context.with(propagation.extract(ROOT_CONTEXT,{traceparent:n}),(function(){var t,n=e._startSpan(AttributeNames.DOCUMENT_LOAD,PerformanceTimingNames.FETCH_START,r);n&&(context.with(trace.setSpan(context.active(),n),(function(){var t=e._startSpan(AttributeNames.DOCUMENT_FETCH,PerformanceTimingNames.FETCH_START,r);t&&(t.setAttribute(SEMATTRS_HTTP_URL,location.href),context.with(trace.setSpan(context.active(),t),(function(){var n;e.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(t,r),e._addCustomAttributesOnSpan(t,null===(n=e.getConfig().applyCustomAttributesOnSpan)||void 0===n?void 0:n.documentFetch),e._endSpan(t,PerformanceTimingNames.RESPONSE_END,r)})))})),n.setAttribute(SEMATTRS_HTTP_URL,location.href),n.setAttribute(SEMATTRS_HTTP_USER_AGENT,navigator.userAgent),e._addResourcesSpans(n),e.getConfig().ignoreNetworkEvents||(addSpanNetworkEvent(n,PerformanceTimingNames.FETCH_START,r),addSpanNetworkEvent(n,PerformanceTimingNames.UNLOAD_EVENT_START,r),addSpanNetworkEvent(n,PerformanceTimingNames.UNLOAD_EVENT_END,r),addSpanNetworkEvent(n,PerformanceTimingNames.DOM_INTERACTIVE,r),addSpanNetworkEvent(n,PerformanceTimingNames.DOM_CONTENT_LOADED_EVENT_START,r),addSpanNetworkEvent(n,PerformanceTimingNames.DOM_CONTENT_LOADED_EVENT_END,r),addSpanNetworkEvent(n,PerformanceTimingNames.DOM_COMPLETE,r),addSpanNetworkEvent(n,PerformanceTimingNames.LOAD_EVENT_START,r),addSpanNetworkEvent(n,PerformanceTimingNames.LOAD_EVENT_END,r)),e.getConfig().ignorePerformancePaintEvents||addSpanPerformancePaintEvents(n),e._addCustomAttributesOnSpan(n,null===(t=e.getConfig().applyCustomAttributesOnSpan)||void 0===t?void 0:t.documentLoad),e._endSpan(n,PerformanceTimingNames.LOAD_EVENT_END,r))}))},t.prototype._endSpan=function(e,t,r){e&&(hasKey(r,t)?e.end(r[t]):e.end())},t.prototype._initResourceSpan=function(e,t){var r,n=this._startSpan(AttributeNames.RESOURCE_FETCH,PerformanceTimingNames.FETCH_START,e,t);n&&(n.setAttribute(SEMATTRS_HTTP_URL,e.name),this.getConfig().ignoreNetworkEvents||addSpanNetworkEvents(n,e),this._addCustomAttributesOnResourceSpan(n,e,null===(r=this.getConfig().applyCustomAttributesOnSpan)||void 0===r?void 0:r.resourceFetch),this._endSpan(n,PerformanceTimingNames.RESPONSE_END,e))},t.prototype._startSpan=function(e,t,r,n){if(hasKey(r,t)&&"number"==typeof r[t])return this.tracer.startSpan(e,{startTime:r[t]},n?trace.setSpan(context.active(),n):void 0)},t.prototype._waitForPageLoad=function(){"complete"===window.document.readyState?this._onDocumentLoaded():(this._onDocumentLoaded=this._onDocumentLoaded.bind(this),window.addEventListener("load",this._onDocumentLoaded))},t.prototype._addCustomAttributesOnSpan=function(e,t){var r=this;t&&safeExecuteInTheMiddle((function(){return t(e)}),(function(e){e&&r._diag.error("addCustomAttributesOnSpan",e)}),!0)},t.prototype._addCustomAttributesOnResourceSpan=function(e,t,r){var n=this;r&&safeExecuteInTheMiddle((function(){return r(e,t)}),(function(e){e&&n._diag.error("addCustomAttributesOnResourceSpan",e)}),!0)},t.prototype.enable=function(){window.removeEventListener("load",this._onDocumentLoaded),this._waitForPageLoad()},t.prototype.disable=function(){window.removeEventListener("load",this._onDocumentLoaded)},t}(InstrumentationBase);function isListenerObject(e){return void 0===e&&(e={}),"function"==typeof e.addEventListener&&"function"==typeof e.removeEventListener}var ZONE_CONTEXT_KEY="OT_ZONE_CONTEXT",ZoneContextManager=function(){function e(){this._enabled=!1,this._zoneCounter=0}return e.prototype._activeContextFromZone=function(e){return e&&e.get(ZONE_CONTEXT_KEY)||ROOT_CONTEXT},e.prototype._bindFunction=function(e,t){var r=this,n=function(){for(var n=this,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];return r.with(e,(function(){return t.apply(n,o)}))};return Object.defineProperty(n,"length",{enumerable:!1,configurable:!0,writable:!1,value:t.length}),n},e.prototype._bindListener=function(e,t){var r=t;return void 0!==r.__ot_listeners||(r.__ot_listeners={},"function"==typeof r.addEventListener&&(r.addEventListener=this._patchAddEventListener(r,r.addEventListener,e)),"function"==typeof r.removeEventListener&&(r.removeEventListener=this._patchRemoveEventListener(r,r.removeEventListener))),t},e.prototype._createZoneName=function(){this._zoneCounter++;var e=Math.random();return this._zoneCounter+"-"+e},e.prototype._createZone=function(e,t){var r;return Zone.current.fork({name:e,properties:(r={},r[ZONE_CONTEXT_KEY]=t,r)})},e.prototype._getActiveZone=function(){return Zone.current},e.prototype._patchAddEventListener=function(e,t,r){var n=this;return function(o,i,a){void 0===e.__ot_listeners&&(e.__ot_listeners={});var s=e.__ot_listeners[o];void 0===s&&(s=new WeakMap,e.__ot_listeners[o]=s);var u=n.bind(r,i);return s.set(i,u),t.call(this,o,u,a)}},e.prototype._patchRemoveEventListener=function(e,t){return function(r,n){if(void 0===e.__ot_listeners||void 0===e.__ot_listeners[r])return t.call(this,r,n);var o=e.__ot_listeners[r],i=o.get(n);return o.delete(n),t.call(this,r,i||n)}},e.prototype.active=function(){if(!this._enabled)return ROOT_CONTEXT;var e=this._getActiveZone(),t=this._activeContextFromZone(e);return t||ROOT_CONTEXT},e.prototype.bind=function(e,t){return void 0===e&&(e=this.active()),"function"==typeof t?this._bindFunction(e,t):(isListenerObject(t)&&this._bindListener(e,t),t)},e.prototype.disable=function(){return this._enabled=!1,this},e.prototype.enable=function(){return this._enabled=!0,this},e.prototype.with=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var i=this._createZoneName();return this._createZone(i,e).run(t,r,n)},e}();
/**
     * @license Angular v12.0.0-next.0
     * (c) 2010-2020 Google LLC. https://angular.io/
     * License: MIT
     */
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
!function(e){const t=e.performance;function r(e){t&&t.mark&&t.mark(e)}function n(e,r){t&&t.measure&&t.measure(e,r)}r("Zone");const o=e.__Zone_symbol_prefix||"__zone_symbol__";function i(e){return o+e}const a=!0===e[i("forceDuplicateZoneCheck")];if(e.Zone){if(a||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}class s{constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new c(this,this._parent&&this._parent._zoneDelegate,t)}static assertZonePatched(){if(e.Promise!==A.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=s.current;for(;e.parent;)e=e.parent;return e}static get current(){return I.zone}static get currentTask(){return L}static __load_patch(t,o,i=!1){if(A.hasOwnProperty(t)){if(!i&&a)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const i="Zone:"+t;r(i),A[t]=o(e,s,x),n(i,i)}}get parent(){return this._parent}get name(){return this._name}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const r=this._zoneDelegate.intercept(this,e,t),n=this;return function(){return n.runGuarded(r,this,arguments,t)}}run(e,t,r,n){I={parent:I,zone:this};try{return this._zoneDelegate.invoke(this,e,t,r,n)}finally{I=I.parent}}runGuarded(e,t=null,r,n){I={parent:I,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,r,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{I=I.parent}}runTask(e,t,r){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||v).name+"; Execution: "+this.name+")");if(e.state===b&&(e.type===P||e.type===R))return;const n=e.state!=S;n&&e._transitionTo(S,T),e.runCount++;const o=L;L=e,I={parent:I,zone:this};try{e.type==R&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,r)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{e.state!==b&&e.state!==w&&(e.type==P||e.data&&e.data.isPeriodic?n&&e._transitionTo(T,S):(e.runCount=0,this._updateTaskCount(e,-1),n&&e._transitionTo(b,S,b))),I=I.parent,L=o}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(E,b);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(w,E,b),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==E&&e._transitionTo(T,E),e}scheduleMicroTask(e,t,r,n){return this.scheduleTask(new l(N,e,t,r,n,void 0))}scheduleMacroTask(e,t,r,n,o){return this.scheduleTask(new l(R,e,t,r,n,o))}scheduleEventTask(e,t,r,n,o){return this.scheduleTask(new l(P,e,t,r,n,o))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||v).name+"; Execution: "+this.name+")");e._transitionTo(O,T,S);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(w,O),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(b,O),e.runCount=0,e}_updateTaskCount(e,t){const r=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let n=0;n<r.length;n++)r[n]._updateTaskCount(e.type,t)}}s.__symbol__=i;const u={name:"",onHasTask:(e,t,r,n)=>e.hasTask(r,n),onScheduleTask:(e,t,r,n)=>e.scheduleTask(r,n),onInvokeTask:(e,t,r,n,o,i)=>e.invokeTask(r,n,o,i),onCancelTask:(e,t,r,n)=>e.cancelTask(r,n)};class c{constructor(e,t,r){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=r&&(r&&r.onFork?r:t._forkZS),this._forkDlgt=r&&(r.onFork?t:t._forkDlgt),this._forkCurrZone=r&&(r.onFork?this.zone:t._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:t._interceptZS),this._interceptDlgt=r&&(r.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:t._invokeZS),this._invokeDlgt=r&&(r.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:t._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:t._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:t._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:t._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const n=r&&r.onHasTask,o=t&&t._hasTaskZS;(n||o)&&(this._hasTaskZS=n?r:u,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,r.onScheduleTask||(this._scheduleTaskZS=u,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),r.onInvokeTask||(this._invokeTaskZS=u,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),r.onCancelTask||(this._cancelTaskZS=u,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new s(e,t)}intercept(e,t,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,r):t}invoke(e,t,r,n,o){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,r,n,o):t.apply(r,n)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let r=t;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),r||(r=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=N)throw new Error("Task is missing scheduleFn.");y(t)}return r}invokeTask(e,t,r,n){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,r,n):t.callback.apply(r,n)}cancelTask(e,t){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");r=t.cancelFn(t)}return r}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const r=this._taskCounts,n=r[e],o=r[e]=n+t;if(o<0)throw new Error("More tasks executed then were scheduled.");if(0==n||0==o){const t={microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:e};this.hasTask(this.zone,t)}}}class l{constructor(t,r,n,o,i,a){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=t,this.source=r,this.data=o,this.scheduleFn=i,this.cancelFn=a,!n)throw new Error("callback is not defined");this.callback=n;const s=this;t===P&&o&&o.useG?this.invoke=l.invokeTask:this.invoke=function(){return l.invokeTask.call(e,s,this,arguments)}}static invokeTask(e,t,r){e||(e=this),$++;try{return e.runCount++,e.zone.runTask(e,t,r)}finally{1==$&&_(),$--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(b,E)}_transitionTo(e,t,r){if(this._state!==t&&this._state!==r)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${r?" or '"+r+"'":""}, was '${this._state}'.`);this._state=e,e==b&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const p=i("setTimeout"),f=i("Promise"),d=i("then");let m,g=[],h=!1;function y(t){if(0===$&&0===g.length)if(m||e[f]&&(m=e[f].resolve(0)),m){let e=m[d];e||(e=m.then),e.call(m,_)}else e[p](_,0);t&&g.push(t)}function _(){if(!h){for(h=!0;g.length;){const e=g;g=[];for(let t=0;t<e.length;t++){const r=e[t];try{r.zone.runTask(r,null,null)}catch(e){x.onUnhandledError(e)}}}x.microtaskDrainDone(),h=!1}}const v={name:"NO ZONE"},b="notScheduled",E="scheduling",T="scheduled",S="running",O="canceling",w="unknown",N="microTask",R="macroTask",P="eventTask",A={},x={symbol:i,currentZoneFrame:()=>I,onUnhandledError:C,microtaskDrainDone:C,scheduleMicroTask:y,showUncaughtError:()=>!s[i("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:C,patchMethod:()=>C,bindArguments:()=>[],patchThen:()=>C,patchMacroTask:()=>C,patchEventPrototype:()=>C,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>C,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>C,wrapWithCurrentZone:()=>C,filterProperties:()=>[],attachOriginToPatched:()=>C,_redefineProperty:()=>C,patchCallbacks:()=>C};let I={parent:null,zone:new s(null,null)},L=null,$=0;function C(){}n("Zone","Zone"),e.Zone=s}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||commonjsGlobal);
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ObjectCreate=Object.create,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener",ZONE_SYMBOL_ADD_EVENT_LISTENER=Zone.__symbol__(ADD_EVENT_LISTENER_STR),ZONE_SYMBOL_REMOVE_EVENT_LISTENER=Zone.__symbol__(REMOVE_EVENT_LISTENER_STR),TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=Zone.__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,r,n,o){return Zone.current.scheduleMacroTask(e,t,r,n,o)}const zoneSymbol=Zone.__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||"object"==typeof self&&self||commonjsGlobal,REMOVE_ATTRIBUTE="removeAttribute",NULL_ON_PROP_VALUE=[null];function bindArguments(e,t){for(let r=e.length-1;r>=0;r--)"function"==typeof e[r]&&(e[r]=wrapWithCurrentZone(e[r],t+"_"+r));return e}function patchPrototype(e,t){const r=e.constructor.name;for(let n=0;n<t.length;n++){const o=t[n],i=e[o];if(i){if(!isPropertyWritable(ObjectGetOwnPropertyDescriptor(e,o)))continue;e[o]=(e=>{const t=function(){return e.apply(this,bindArguments(arguments,r+"."+o))};return attachOriginToPatched(t,e),t})(i)}}}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process)&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames={},wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames[e.type];t||(t=zoneSymbolEventNames[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const r=this||e.target||_global,n=r[t];let o;if(isBrowser&&r===internalWindow&&"error"===e.type){const t=e;o=n&&n.call(this,t.message,t.filename,t.lineno,t.colno,t.error),!0===o&&e.preventDefault()}else o=n&&n.apply(this,arguments),null==o||o||e.preventDefault();return o};function patchProperty(e,t,r){let n=ObjectGetOwnPropertyDescriptor(e,t);if(!n&&r){ObjectGetOwnPropertyDescriptor(r,t)&&(n={enumerable:!0,configurable:!0})}if(!n||!n.configurable)return;const o=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(o)&&e[o])return;delete n.writable,delete n.value;const i=n.get,a=n.set,s=t.substr(2);let u=zoneSymbolEventNames[s];u||(u=zoneSymbolEventNames[s]=zoneSymbol("ON_PROPERTY"+s)),n.set=function(t){let r=this;r||e!==_global||(r=_global),r&&(r[u]&&r.removeEventListener(s,wrapFn),a&&a.apply(r,NULL_ON_PROP_VALUE),"function"==typeof t?(r[u]=t,r.addEventListener(s,wrapFn,!1)):r[u]=null)},n.get=function(){let r=this;if(r||e!==_global||(r=_global),!r)return null;const o=r[u];if(o)return o;if(i){let e=i&&i.call(this);if(e)return n.set.call(this,e),"function"==typeof r[REMOVE_ATTRIBUTE]&&r.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,n),e[o]=!0}function patchOnProperties(e,t,r){if(t)for(let n=0;n<t.length;n++)patchProperty(e,"on"+t[n],r);else{const t=[];for(const r in e)"on"==r.substr(0,2)&&t.push(r);for(let n=0;n<t.length;n++)patchProperty(e,t[n],r)}}const originalInstanceKey=zoneSymbol("originalInstance");function patchClass(e){const t=_global[e];if(!t)return;_global[zoneSymbol(e)]=t,_global[e]=function(){const r=bindArguments(arguments,e);switch(r.length){case 0:this[originalInstanceKey]=new t;break;case 1:this[originalInstanceKey]=new t(r[0]);break;case 2:this[originalInstanceKey]=new t(r[0],r[1]);break;case 3:this[originalInstanceKey]=new t(r[0],r[1],r[2]);break;case 4:this[originalInstanceKey]=new t(r[0],r[1],r[2],r[3]);break;default:throw new Error("Arg list too long.")}},attachOriginToPatched(_global[e],t);const r=new t((function(){}));let n;for(n in r)"XMLHttpRequest"===e&&"responseBlob"===n||function(t){"function"==typeof r[t]?_global[e].prototype[t]=function(){return this[originalInstanceKey][t].apply(this[originalInstanceKey],arguments)}:ObjectDefineProperty(_global[e].prototype,t,{set:function(r){"function"==typeof r?(this[originalInstanceKey][t]=wrapWithCurrentZone(r,e+"."+t),attachOriginToPatched(this[originalInstanceKey][t],r)):this[originalInstanceKey][t]=r},get:function(){return this[originalInstanceKey][t]}})}(n);for(n in t)"prototype"!==n&&t.hasOwnProperty(n)&&(_global[e][n]=t[n])}function patchMethod(e,t,r){let n=e;for(;n&&!n.hasOwnProperty(t);)n=ObjectGetPrototypeOf(n);!n&&e[t]&&(n=e);const o=zoneSymbol(t);let i=null;if(n&&(!(i=n[o])||!n.hasOwnProperty(o))){i=n[o]=n[t];if(isPropertyWritable(n&&ObjectGetOwnPropertyDescriptor(n,t))){const e=r(i,o,t);n[t]=function(){return e(this,arguments)},attachOriginToPatched(n[t],i)}}return i}function patchMacroTask(e,t,r){let n=null;function o(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},n.apply(t.target,t.args),e}n=patchMethod(e,t,(e=>function(t,n){const i=r(t,n);return i.cbIdx>=0&&"function"==typeof n[i.cbIdx]?scheduleMacroTaskWithCurrentZone(i.name,n[i.cbIdx],i,o):e.apply(t,n)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}let isDetectedIEOrEdge=!1,ieOrEdge=!1;function isIE(){try{const e=internalWindow.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}function isIEOrEdge(){if(isDetectedIEOrEdge)return ieOrEdge;isDetectedIEOrEdge=!0;try{const e=internalWindow.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(ieOrEdge=!0)}catch(e){}return ieOrEdge}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */Zone.__load_patch("ZoneAwarePromise",((e,t,r)=>{const n=Object.getOwnPropertyDescriptor,o=Object.defineProperty;const i=r.symbol,a=[],s=!0===e[i("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],u=i("Promise"),c=i("then"),l="__creationTrace__";r.onUnhandledError=e=>{if(r.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},r.microtaskDrainDone=()=>{for(;a.length;){const e=a.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){f(e)}}};const p=i("unhandledPromiseRejectionHandler");function f(e){r.onUnhandledError(e);try{const r=t[p];"function"==typeof r&&r.call(this,e)}catch(e){}}function d(e){return e&&e.then}function m(e){return e}function g(e){return U.reject(e)}const h=i("state"),y=i("value"),_=i("finally"),v=i("parentPromiseValue"),b=i("parentPromiseState"),E="Promise.then",T=null,S=!0,O=!1,w=0;function N(e,t){return r=>{try{x(e,t,r)}catch(t){x(e,!1,t)}}}const R=function(){let e=!1;return function(t){return function(){e||(e=!0,t.apply(null,arguments))}}},P="Promise resolved with itself",A=i("currentTaskTrace");function x(e,n,i){const u=R();if(e===i)throw new TypeError(P);if(e[h]===T){let c=null;try{"object"!=typeof i&&"function"!=typeof i||(c=i&&i.then)}catch(t){return u((()=>{x(e,!1,t)}))(),e}if(n!==O&&i instanceof U&&i.hasOwnProperty(h)&&i.hasOwnProperty(y)&&i[h]!==T)L(i),x(e,i[h],i[y]);else if(n!==O&&"function"==typeof c)try{c.call(i,u(N(e,n)),u(N(e,!1)))}catch(t){u((()=>{x(e,!1,t)}))()}else{e[h]=n;const u=e[y];if(e[y]=i,e[_]===_&&n===S&&(e[h]=e[b],e[y]=e[v]),n===O&&i instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data[l];e&&o(i,A,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<u.length;)$(e,u[t++],u[t++],u[t++],u[t++]);if(0==u.length&&n==O){e[h]=w;let n=i;try{throw new Error("Uncaught (in promise): "+function(e){if(e&&e.toString===Object.prototype.toString){return(e.constructor&&e.constructor.name||"")+": "+JSON.stringify(e)}return e?e.toString():Object.prototype.toString.call(e)}(i)+(i&&i.stack?"\n"+i.stack:""))}catch(e){n=e}s&&(n.throwOriginal=!0),n.rejection=i,n.promise=e,n.zone=t.current,n.task=t.currentTask,a.push(n),r.scheduleMicroTask()}}}return e}const I=i("rejectionHandledHandler");function L(e){if(e[h]===w){try{const r=t[I];r&&"function"==typeof r&&r.call(this,{rejection:e[y],promise:e})}catch(e){}e[h]=O;for(let t=0;t<a.length;t++)e===a[t].promise&&a.splice(t,1)}}function $(e,t,r,n,o){L(e);const i=e[h],a=i?"function"==typeof n?n:m:"function"==typeof o?o:g;t.scheduleMicroTask(E,(()=>{try{const n=e[y],o=!!r&&_===r[_];o&&(r[v]=n,r[b]=i);const s=t.run(a,void 0,o&&a!==g&&a!==m?[]:[n]);x(r,!0,s)}catch(e){x(r,!1,e)}}),r)}const C=function(){};class U{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return x(new this(null),S,e)}static reject(e){return x(new this(null),O,e)}static race(e){let t,r,n=new this(((e,n)=>{t=e,r=n}));function o(e){t(e)}function i(e){r(e)}for(let t of e)d(t)||(t=this.resolve(t)),t.then(o,i);return n}static all(e){return U.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof U?this:U).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let r,n,o=new this(((e,t)=>{r=e,n=t})),i=2,a=0;const s=[];for(let o of e){d(o)||(o=this.resolve(o));const e=a;try{o.then((n=>{s[e]=t?t.thenCallback(n):n,i--,0===i&&r(s)}),(o=>{t?(s[e]=t.errorCallback(o),i--,0===i&&r(s)):n(o)}))}catch(e){n(e)}i++,a++}return i-=2,0===i&&r(s),o}constructor(e){const t=this;if(!(t instanceof U))throw new Error("Must be an instanceof Promise.");t[h]=T,t[y]=[];try{e&&e(N(t,S),N(t,O))}catch(e){x(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return U}then(e,r){let n=this.constructor[Symbol.species];n&&"function"==typeof n||(n=this.constructor||U);const o=new n(C),i=t.current;return this[h]==T?this[y].push(i,o,e,r):$(this,i,o,e,r),o}catch(e){return this.then(null,e)}finally(e){let r=this.constructor[Symbol.species];r&&"function"==typeof r||(r=U);const n=new r(C);n[_]=_;const o=t.current;return this[h]==T?this[y].push(o,n,e,e):$(this,o,n,e,e),n}}U.resolve=U.resolve,U.reject=U.reject,U.race=U.race,U.all=U.all;const k=e[u]=e.Promise;e.Promise=U;const j=i("thenPatched");function M(e){const t=e.prototype,r=n(t,"then");if(r&&(!1===r.writable||!r.configurable))return;const o=t.then;t[c]=o,e.prototype.then=function(e,t){return new U(((e,t)=>{o.call(this,e,t)})).then(e,t)},e[j]=!0}return r.patchThen=M,k&&(M(k),patchMethod(e,"fetch",(e=>{return t=e,function(e,r){let n=t.apply(e,r);if(n instanceof U)return n;let o=n.constructor;return o[j]||M(o),n};var t}))),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,U})),
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
Zone.__load_patch("toString",(e=>{const t=Function.prototype.toString,r=zoneSymbol("OriginalDelegate"),n=zoneSymbol("Promise"),o=zoneSymbol("Error"),i=function(){if("function"==typeof this){const i=this[r];if(i)return"function"==typeof i?t.call(i):Object.prototype.toString.call(i);if(this===Promise){const r=e[n];if(r)return t.call(r)}if(this===Error){const r=e[o];if(r)return t.call(r)}}return t.call(this)};i[r]=t,Function.prototype.toString=i;const a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}));
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
let passiveSupported=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){passiveSupported=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames$1={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const r=(t?t(e):e)+FALSE_STR,n=(t?t(e):e)+TRUE_STR,o=ZONE_SYMBOL_PREFIX+r,i=ZONE_SYMBOL_PREFIX+n;zoneSymbolEventNames$1[e]={},zoneSymbolEventNames$1[e][FALSE_STR]=o,zoneSymbolEventNames$1[e][TRUE_STR]=i}function patchEventTarget(e,t,r){const n=r&&r.add||ADD_EVENT_LISTENER_STR,o=r&&r.rm||REMOVE_EVENT_LISTENER_STR,i=r&&r.listeners||"eventListeners",a=r&&r.rmAll||"removeAllListeners",s=zoneSymbol(n),u="."+n+":",c="prependListener",l="."+c+":",p=function(e,t,r){if(e.isRemoved)return;const n=e.callback;"object"==typeof n&&n.handleEvent&&(e.callback=e=>n.handleEvent(e),e.originalDelegate=n),e.invoke(e,t,[r]);const i=e.options;if(i&&"object"==typeof i&&i.once){const n=e.originalDelegate?e.originalDelegate:e.callback;t[o].call(t,r.type,n,i)}},f=function(t){if(!(t=t||e.event))return;const r=this||t.target||e,n=r[zoneSymbolEventNames$1[t.type][FALSE_STR]];if(n)if(1===n.length)p(n[0],r,t);else{const e=n.slice();for(let n=0;n<e.length&&(!t||!0!==t[IMMEDIATE_PROPAGATION_SYMBOL]);n++)p(e[n],r,t)}},d=function(t){if(!(t=t||e.event))return;const r=this||t.target||e,n=r[zoneSymbolEventNames$1[t.type][TRUE_STR]];if(n)if(1===n.length)p(n[0],r,t);else{const e=n.slice();for(let n=0;n<e.length&&(!t||!0!==t[IMMEDIATE_PROPAGATION_SYMBOL]);n++)p(e[n],r,t)}};function m(t,r){if(!t)return!1;let p=!0;r&&void 0!==r.useG&&(p=r.useG);const m=r&&r.vh;let g=!0;r&&void 0!==r.chkDup&&(g=r.chkDup);let h=!1;r&&void 0!==r.rt&&(h=r.rt);let y=t;for(;y&&!y.hasOwnProperty(n);)y=ObjectGetPrototypeOf(y);if(!y&&t[n]&&(y=t),!y)return!1;if(y[s])return!1;const _=r&&r.eventNameToString,v={},b=y[s]=y[n],E=y[zoneSymbol(o)]=y[o],T=y[zoneSymbol(i)]=y[i],S=y[zoneSymbol(a)]=y[a];let O;r&&r.prepend&&(O=y[zoneSymbol(r.prepend)]=y[r.prepend]);const w=function(e){return O.call(v.target,v.eventName,e.invoke,v.options)},N=p?function(e){if(!v.isExisting)return b.call(v.target,v.eventName,v.capture?d:f,v.options)}:function(e){return b.call(v.target,v.eventName,e.invoke,v.options)},R=p?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames$1[e.eventName];let r;t&&(r=t[e.capture?TRUE_STR:FALSE_STR]);const n=r&&e.target[r];if(n)for(let t=0;t<n.length;t++){if(n[t]===e){n.splice(t,1),e.isRemoved=!0,0===n.length&&(e.allRemoved=!0,e.target[r]=null);break}}}if(e.allRemoved)return E.call(e.target,e.eventName,e.capture?d:f,e.options)}:function(e){return E.call(e.target,e.eventName,e.invoke,e.options)},P=r&&r.diff?r.diff:function(e,t){const r=typeof t;return"function"===r&&e.callback===t||"object"===r&&e.originalDelegate===t},A=Zone[zoneSymbol("UNPATCHED_EVENTS")],x=e[zoneSymbol("PASSIVE_EVENTS")],I=function(t,n,o,i,a=!1,s=!1){return function(){const u=this||e;let c=arguments[0];r&&r.transferEventName&&(c=r.transferEventName(c));let l=arguments[1];if(!l)return t.apply(this,arguments);if(isNode&&"uncaughtException"===c)return t.apply(this,arguments);let f=!1;if("function"!=typeof l){if(!l.handleEvent)return t.apply(this,arguments);f=!0}if(m&&!m(t,l,u,arguments))return;const d=passiveSupported&&!!x&&-1!==x.indexOf(c),h=function(e,t){return!passiveSupported&&"object"==typeof e&&e?!!e.capture:passiveSupported&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?Object.assign(Object.assign({},e),{passive:!0}):e:{passive:!0}:e}(arguments[2],d);if(A)for(let e=0;e<A.length;e++)if(c===A[e])return d?t.call(u,c,l,h):t.apply(this,arguments);const y=!!h&&("boolean"==typeof h||h.capture),b=!(!h||"object"!=typeof h)&&h.once,E=Zone.current;let T=zoneSymbolEventNames$1[c];T||(prepareEventNames(c,_),T=zoneSymbolEventNames$1[c]);const S=T[y?TRUE_STR:FALSE_STR];let O,w=u[S],N=!1;if(w){if(N=!0,g)for(let e=0;e<w.length;e++)if(P(w[e],l))return}else w=u[S]=[];const R=u.constructor.name,I=globalSources[R];I&&(O=I[c]),O||(O=R+n+(_?_(c):c)),v.options=h,b&&(v.options.once=!1),v.target=u,v.capture=y,v.eventName=c,v.isExisting=N;const L=p?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;L&&(L.taskData=v);const $=E.scheduleEventTask(O,l,L,o,i);return v.target=null,L&&(L.taskData=null),b&&(h.once=!0),(passiveSupported||"boolean"!=typeof $.options)&&($.options=h),$.target=u,$.capture=y,$.eventName=c,f&&($.originalDelegate=l),s?w.unshift($):w.push($),a?u:void 0}};return y[n]=I(b,u,N,R,h),O&&(y[c]=I(O,l,w,R,h,!0)),y[o]=function(){const t=this||e;let n=arguments[0];r&&r.transferEventName&&(n=r.transferEventName(n));const o=arguments[2],i=!!o&&("boolean"==typeof o||o.capture),a=arguments[1];if(!a)return E.apply(this,arguments);if(m&&!m(E,a,t,arguments))return;const s=zoneSymbolEventNames$1[n];let u;s&&(u=s[i?TRUE_STR:FALSE_STR]);const c=u&&t[u];if(c)for(let e=0;e<c.length;e++){const r=c[e];if(P(r,a)){if(c.splice(e,1),r.isRemoved=!0,0===c.length&&(r.allRemoved=!0,t[u]=null,"string"==typeof n)){t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+n]=null}return r.zone.cancelTask(r),h?t:void 0}}return E.apply(this,arguments)},y[i]=function(){const t=this||e;let n=arguments[0];r&&r.transferEventName&&(n=r.transferEventName(n));const o=[],i=findEventTasks(t,_?_(n):n);for(let e=0;e<i.length;e++){const t=i[e];let r=t.originalDelegate?t.originalDelegate:t.callback;o.push(r)}return o},y[a]=function(){const t=this||e;let n=arguments[0];if(n){r&&r.transferEventName&&(n=r.transferEventName(n));const e=zoneSymbolEventNames$1[n];if(e){const r=e[FALSE_STR],i=e[TRUE_STR],a=t[r],s=t[i];if(a){const e=a.slice();for(let t=0;t<e.length;t++){const r=e[t];let i=r.originalDelegate?r.originalDelegate:r.callback;this[o].call(this,n,i,r.options)}}if(s){const e=s.slice();for(let t=0;t<e.length;t++){const r=e[t];let i=r.originalDelegate?r.originalDelegate:r.callback;this[o].call(this,n,i,r.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const r=e[t],n=EVENT_NAME_SYMBOL_REGX.exec(r);let o=n&&n[1];o&&"removeListener"!==o&&this[a].call(this,o)}this[a].call(this,"removeListener")}if(h)return this},attachOriginToPatched(y[n],b),attachOriginToPatched(y[o],E),S&&attachOriginToPatched(y[a],S),T&&attachOriginToPatched(y[i],T),!0}let g=[];for(let e=0;e<t.length;e++)g[e]=m(t[e],r);return g}function findEventTasks(e,t){if(!t){const r=[];for(let n in e){const o=EVENT_NAME_SYMBOL_REGX.exec(n);let i=o&&o[1];if(i&&(!t||i===t)){const t=e[n];if(t)for(let e=0;e<t.length;e++)r.push(t[e])}}return r}let r=zoneSymbolEventNames$1[t];r||(prepareEventNames(t),r=zoneSymbolEventNames$1[t]);const n=e[r[FALSE_STR]],o=e[r[TRUE_STR]];return n?o?n.concat(o):n.slice():o?o.slice():[]}function patchEventPrototype(e,t){const r=e.Event;r&&r.prototype&&t.patchMethod(r.prototype,"stopImmediatePropagation",(e=>function(t,r){t[IMMEDIATE_PROPAGATION_SYMBOL]=!0,e&&e.apply(t,r)}))}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function patchCallbacks(e,t,r,n,o){const i=Zone.__symbol__(n);if(t[i])return;const a=t[i]=t[n];t[n]=function(i,s,u){return s&&s.prototype&&o.forEach((function(t){const o=`${r}.${n}::`+t,i=s.prototype;if(i.hasOwnProperty(t)){const r=e.ObjectGetOwnPropertyDescriptor(i,t);r&&r.value?(r.value=e.wrapWithCurrentZone(r.value,o),e._redefineProperty(s.prototype,t,r)):i[t]&&(i[t]=e.wrapWithCurrentZone(i[t],o))}else i[t]&&(i[t]=e.wrapWithCurrentZone(i[t],o))})),a.call(t,i,s,u)},e.attachOriginToPatched(t[n],a)}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */const globalEventHandlersEventNames=["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"],documentEventNames=["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],windowEventNames=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],htmlElementEventNames=["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],mediaElementEventNames=["encrypted","waitingforkey","msneedkey","mozinterruptbegin","mozinterruptend"],ieElementEventNames=["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"],webglEventNames=["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],formEventNames=["autocomplete","autocompleteerror"],detailEventNames=["toggle"],frameEventNames=["load"],frameSetEventNames=["blur","error","focus","load","resize","scroll","messageerror"],marqueeEventNames=["bounce","finish","start"],XMLHttpRequestEventNames=["loadstart","progress","abort","error","load","progress","timeout","loadend","readystatechange"],IDBIndexEventNames=["upgradeneeded","complete","abort","success","error","blocked","versionchange","close"],websocketEventNames=["close","error","open","message"],workerEventNames=["error","message"],eventNames=globalEventHandlersEventNames.concat(webglEventNames,formEventNames,detailEventNames,documentEventNames,windowEventNames,htmlElementEventNames,ieElementEventNames);function filterProperties(e,t,r){if(!r||0===r.length)return t;const n=r.filter((t=>t.target===e));if(!n||0===n.length)return t;const o=n[0].ignoreProperties;return t.filter((e=>-1===o.indexOf(e)))}function patchFilteredProperties(e,t,r,n){if(!e)return;patchOnProperties(e,filterProperties(e,t,r),n)}function propertyDescriptorPatch(e,t){if(isNode&&!isMix)return;if(Zone[e.symbol("patchEvents")])return;const r="undefined"!=typeof WebSocket,n=t.__Zone_ignore_on_properties;if(isBrowser){const e=window,t=isIE()?[{target:e,ignoreProperties:["error"]}]:[];patchFilteredProperties(e,eventNames.concat(["messageerror"]),n?n.concat(t):n,ObjectGetPrototypeOf(e)),patchFilteredProperties(Document.prototype,eventNames,n),void 0!==e.SVGElement&&patchFilteredProperties(e.SVGElement.prototype,eventNames,n),patchFilteredProperties(Element.prototype,eventNames,n),patchFilteredProperties(HTMLElement.prototype,eventNames,n),patchFilteredProperties(HTMLMediaElement.prototype,mediaElementEventNames,n),patchFilteredProperties(HTMLFrameSetElement.prototype,windowEventNames.concat(frameSetEventNames),n),patchFilteredProperties(HTMLBodyElement.prototype,windowEventNames.concat(frameSetEventNames),n),patchFilteredProperties(HTMLFrameElement.prototype,frameEventNames,n),patchFilteredProperties(HTMLIFrameElement.prototype,frameEventNames,n);const r=e.HTMLMarqueeElement;r&&patchFilteredProperties(r.prototype,marqueeEventNames,n);const o=e.Worker;o&&patchFilteredProperties(o.prototype,workerEventNames,n)}const o=t.XMLHttpRequest;o&&patchFilteredProperties(o.prototype,XMLHttpRequestEventNames,n);const i=t.XMLHttpRequestEventTarget;i&&patchFilteredProperties(i&&i.prototype,XMLHttpRequestEventNames,n),"undefined"!=typeof IDBIndex&&(patchFilteredProperties(IDBIndex.prototype,IDBIndexEventNames,n),patchFilteredProperties(IDBRequest.prototype,IDBIndexEventNames,n),patchFilteredProperties(IDBOpenDBRequest.prototype,IDBIndexEventNames,n),patchFilteredProperties(IDBDatabase.prototype,IDBIndexEventNames,n),patchFilteredProperties(IDBTransaction.prototype,IDBIndexEventNames,n),patchFilteredProperties(IDBCursor.prototype,IDBIndexEventNames,n)),r&&patchFilteredProperties(WebSocket.prototype,websocketEventNames,n)}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */Zone.__load_patch("util",((e,t,r)=>{r.patchOnProperties=patchOnProperties,r.patchMethod=patchMethod,r.bindArguments=bindArguments,r.patchMacroTask=patchMacroTask;const n=t.__symbol__("BLACK_LISTED_EVENTS"),o=t.__symbol__("UNPATCHED_EVENTS");e[o]&&(e[n]=e[o]),e[n]&&(t[n]=t[o]=e[n]),r.patchEventPrototype=patchEventPrototype,r.patchEventTarget=patchEventTarget,r.isIEOrEdge=isIEOrEdge,r.ObjectDefineProperty=ObjectDefineProperty,r.ObjectGetOwnPropertyDescriptor=ObjectGetOwnPropertyDescriptor,r.ObjectCreate=ObjectCreate,r.ArraySlice=ArraySlice,r.patchClass=patchClass,r.wrapWithCurrentZone=wrapWithCurrentZone,r.filterProperties=filterProperties,r.attachOriginToPatched=attachOriginToPatched,r._redefineProperty=Object.defineProperty,r.patchCallbacks=patchCallbacks,r.getGlobalObjects=()=>({globalSources:globalSources,zoneSymbolEventNames:zoneSymbolEventNames$1,eventNames:eventNames,isBrowser:isBrowser,isMix:isMix,isNode:isNode,TRUE_STR:TRUE_STR,FALSE_STR:FALSE_STR,ZONE_SYMBOL_PREFIX:ZONE_SYMBOL_PREFIX,ADD_EVENT_LISTENER_STR:ADD_EVENT_LISTENER_STR,REMOVE_EVENT_LISTENER_STR:REMOVE_EVENT_LISTENER_STR})}));
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */
const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,r,n){let o=null,i=null;r+=n;const a={};function s(t){const r=t.data;return r.args[0]=function(){return t.invoke.apply(this,arguments)},r.handleId=o.apply(e,r.args),t}function u(t){return i.call(e,t.data.handleId)}o=patchMethod(e,t+=n,(r=>function(o,i){if("function"==typeof i[0]){const e={isPeriodic:"Interval"===n,delay:"Timeout"===n||"Interval"===n?i[1]||0:void 0,args:i},r=i[0];i[0]=function(){try{return r.apply(this,arguments)}finally{e.isPeriodic||("number"==typeof e.handleId?delete a[e.handleId]:e.handleId&&(e.handleId[taskSymbol]=null))}};const o=scheduleMacroTaskWithCurrentZone(t,i[0],e,s,u);if(!o)return o;const c=o.data.handleId;return"number"==typeof c?a[c]=o:c&&(c[taskSymbol]=o),c&&c.ref&&c.unref&&"function"==typeof c.ref&&"function"==typeof c.unref&&(o.ref=c.ref.bind(c),o.unref=c.unref.bind(c)),"number"==typeof c||c?c:o}return r.apply(e,i)})),i=patchMethod(e,r,(t=>function(r,n){const o=n[0];let i;"number"==typeof o?i=a[o]:(i=o&&o[taskSymbol],i||(i=o)),i&&"string"==typeof i.type?"notScheduled"!==i.state&&(i.cancelFn&&i.data.isPeriodic||0===i.runCount)&&("number"==typeof o?delete a[o]:o&&(o[taskSymbol]=null),i.zone.cancelTask(i)):t.apply(e,n)}))}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function patchCustomElements(e,t){const{isBrowser:r,isMix:n}=t.getGlobalObjects();if(!r&&!n||!e.customElements||!("customElements"in e))return;t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */function eventTargetPatch(e,t){if(Zone[t.symbol("patchEventTarget")])return;const{eventNames:r,zoneSymbolEventNames:n,TRUE_STR:o,FALSE_STR:i,ZONE_SYMBOL_PREFIX:a}=t.getGlobalObjects();for(let e=0;e<r.length;e++){const t=r[e],s=a+(t+i),u=a+(t+o);n[t]={},n[t][i]=s,n[t][o]=u}const s=e.EventTarget;return s&&s.prototype?(t.patchEventTarget(e,[s&&s.prototype]),!0):void 0}function patchEvent(e,t){t.patchEventPrototype(e,t)}
/**
     * @license
     * Copyright Google LLC All Rights Reserved.
     *
     * Use of this source code is governed by an MIT-style license that can be
     * found in the LICENSE file at https://angular.io/license
     */Zone.__load_patch("legacy",(e=>{const t=e[Zone.__symbol__("legacyPatch")];t&&t()})),Zone.__load_patch("queueMicrotask",((e,t,r)=>{r.patchMethod(e,"queueMicrotask",(e=>function(e,r){t.current.scheduleMicroTask("queueMicrotask",r[0])}))})),Zone.__load_patch("timers",(e=>{const t="set",r="clear";patchTimer(e,t,r,"Timeout"),patchTimer(e,t,r,"Interval"),patchTimer(e,t,r,"Immediate")})),Zone.__load_patch("requestAnimationFrame",(e=>{patchTimer(e,"request","cancel","AnimationFrame"),patchTimer(e,"mozRequest","mozCancel","AnimationFrame"),patchTimer(e,"webkitRequest","webkitCancel","AnimationFrame")})),Zone.__load_patch("blocking",((e,t)=>{const r=["alert","prompt","confirm"];for(let n=0;n<r.length;n++){patchMethod(e,r[n],((r,n,o)=>function(n,i){return t.current.run(r,e,i,o)}))}})),Zone.__load_patch("EventTarget",((e,t,r)=>{patchEvent(e,r),eventTargetPatch(e,r);const n=e.XMLHttpRequestEventTarget;n&&n.prototype&&r.patchEventTarget(e,[n.prototype])})),Zone.__load_patch("MutationObserver",((e,t,r)=>{patchClass("MutationObserver"),patchClass("WebKitMutationObserver")})),Zone.__load_patch("IntersectionObserver",((e,t,r)=>{patchClass("IntersectionObserver")})),Zone.__load_patch("FileReader",((e,t,r)=>{patchClass("FileReader")})),Zone.__load_patch("on_property",((e,t,r)=>{propertyDescriptorPatch(r,e)})),Zone.__load_patch("customElements",((e,t,r)=>{patchCustomElements(e,r)})),Zone.__load_patch("XHR",((e,t)=>{!function(e){const u=e.XMLHttpRequest;if(!u)return;const c=u.prototype;let l=c[ZONE_SYMBOL_ADD_EVENT_LISTENER],p=c[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];if(!l){const t=e.XMLHttpRequestEventTarget;if(t){const e=t.prototype;l=e[ZONE_SYMBOL_ADD_EVENT_LISTENER],p=e[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]}}const f="readystatechange",d="scheduled";function m(e){const n=e.data,a=n.target;a[i]=!1,a[s]=!1;const u=a[o];l||(l=a[ZONE_SYMBOL_ADD_EVENT_LISTENER],p=a[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]),u&&p.call(a,f,u);const c=a[o]=()=>{if(a.readyState===a.DONE)if(!n.aborted&&a[i]&&e.state===d){const r=a[t.__symbol__("loadfalse")];if(0!==a.status&&r&&r.length>0){const o=e.invoke;e.invoke=function(){const r=a[t.__symbol__("loadfalse")];for(let t=0;t<r.length;t++)r[t]===e&&r.splice(t,1);n.aborted||e.state!==d||o.call(e)},r.push(e)}else e.invoke()}else n.aborted||!1!==a[i]||(a[s]=!0)};l.call(a,f,c);return a[r]||(a[r]=e),b.apply(a,n.args),a[i]=!0,e}function g(){}function h(e){const t=e.data;return t.aborted=!0,E.apply(t.target,t.args)}const y=patchMethod(c,"open",(()=>function(e,t){return e[n]=0==t[2],e[a]=t[1],y.apply(e,t)})),_=zoneSymbol("fetchTaskAborting"),v=zoneSymbol("fetchTaskScheduling"),b=patchMethod(c,"send",(()=>function(e,r){if(!0===t.current[v])return b.apply(e,r);if(e[n])return b.apply(e,r);{const t={target:e,url:e[a],isPeriodic:!1,args:r,aborted:!1},n=scheduleMacroTaskWithCurrentZone("XMLHttpRequest.send",g,t,m,h);e&&!0===e[s]&&!t.aborted&&n.state===d&&n.invoke()}})),E=patchMethod(c,"abort",(()=>function(e,n){const o=e[r];if(o&&"string"==typeof o.type){if(null==o.cancelFn||o.data&&o.data.aborted)return;o.zone.cancelTask(o)}else if(!0===t.current[_])return E.apply(e,n)}))}(e);const r=zoneSymbol("xhrTask"),n=zoneSymbol("xhrSync"),o=zoneSymbol("xhrListener"),i=zoneSymbol("xhrScheduled"),a=zoneSymbol("xhrURL"),s=zoneSymbol("xhrErrorBeforeScheduled")})),Zone.__load_patch("geolocation",(e=>{e.navigator&&e.navigator.geolocation&&patchPrototype(e.navigator.geolocation,["getCurrentPosition","watchPosition"])})),Zone.__load_patch("PromiseRejectionEvent",((e,t)=>{function r(t){return function(r){findEventTasks(e,t).forEach((n=>{const o=e.PromiseRejectionEvent;if(o){const e=new o(t,{promise:r.promise,reason:r.rejection});n.invoke(e)}}))}}e.PromiseRejectionEvent&&(t[zoneSymbol("unhandledPromiseRejectionHandler")]=r("unhandledrejection"),t[zoneSymbol("rejectionHandledHandler")]=r("rejectionhandled"))}));var PROD_OTLP_URL="https://telemetry.hailiangedu.com/web/v1/traces",TEST_OTLP_URL="http://10.30.5.34:40002/v1/traces",PROD_CORS_URL_PATTERN=/^https?:\/\/\w+\.hailiangedu\.com/,TEST_CORS_URL_PATTERN=/.+/;Zone[Zone.__symbol__("ignoreConsoleErrorUncaughtError")]=!0;var otlpUserInfoKeys=["tenantId","schoolId","campusId","userId","staffId","deviceId","userName"],OpenTelemetryManger=function(){function e(t){if(this.config=t,this.resource={},e.instance)return e.instance;e.instance=this;var r=t.userInfo,n=t.projectInfo;this.init(t,__assign$4(__assign$4({},this.createOtlpUserInfoResource(r)),n))}return e.prototype.createOtlpUserInfoResource=function(e){if("object"!=typeof e)return{};var t={};return otlpUserInfoKeys.forEach((function(r){var n=e[r];"string"==typeof n&&(t["user.".concat(r)]=n)})),t},e.prototype.init=function(t,r){var n;try{if(e.provider)return console.warn("opentelemetry 已经初始化"),!1;var o=t.env,i=t.serviceName,a=t.otlpUrl,s=t.maxExportBatchSize,u=t.scheduledDelayMillis,c=t.exportTimeoutMillis,l=t.maxQueueSize,p=t.concurrencyLimit,f=t.sampler,d=new OTLPTraceExporter({url:a||("production"===o?PROD_OTLP_URL:TEST_OTLP_URL),concurrencyLimit:p||2});this.resource=__assign$4(((n={})[ATTR_SERVICE_NAME]=i,n["user.uuid"]="".concat(localStorage.getItem("uuid")),n),r);var m=new Resource(this.resource),g=new WebTracerProvider({sampler:f,resource:m,spanProcessors:[new BatchSpanProcessor(d,{maxExportBatchSize:s||50,scheduledDelayMillis:u,exportTimeoutMillis:c,maxQueueSize:l})]});return g.register({contextManager:new ZoneContextManager}),this.initInstrumentation(t),e.provider=g,console.info("openTelemetry初始化成功"),!0}catch(e){return console.error("opentelemetry初始化失败:",e),!1}},e.prototype.initInstrumentation=function(e){var t=e.env,r=e.xhr,n=e.fetch,o=e.page,i=e.userInteraction,a=e.corsUrls,s=e.eventNames,u=e.ignoreUrls,c=e.ignoreUserInteractionMethods,l=a;l||(l="production"===t?[PROD_CORS_URL_PATTERN]:[TEST_CORS_URL_PATTERN]);var p=[];if(r&&p.push(new XMLHttpRequestInstrumentation({propagateTraceHeaderCorsUrls:l,ignoreUrls:u})),n&&p.push(new FetchInstrumentation({propagateTraceHeaderCorsUrls:l,ignoreUrls:u})),o&&p.push(new DocumentLoadInstrumentation),i){var f="function"==typeof c;p.push(new UserInteractionInstrumentation({eventNames:s||["click"],shouldPreventSpanCreation:function(e,t,r){try{r.setAttribute("target.id",t.id),r.setAttribute("target.name",t.innerText.slice(0,100));var n=t.dataset;for(var o in n){var i=n[o];i&&r.setAttribute("data.".concat(o),i)}if(f)return c(e,t,r)}catch(e){console.error("[openTelemetry] 设置元素属性失败",e)}}}))}registerInstrumentations({instrumentations:p})},e.prototype.getProvider=function(){if(!e.provider)throw new Error("请先初始化opentelemetry");return e.provider},e.getInstance=function(){if(!e.instance)throw new Error("请先初始化opentelemetry");return e.instance},e.prototype.updateUserInfo=function(t){var r;return __awaiter$3(this,void 0,void 0,(function(){var n,o,i,a=this;return __generator$3(this,(function(s){switch(s.label){case 0:return s.trys.push([0,4,,5]),t="object"==typeof t?t:{},n={},o=!1,otlpUserInfoKeys.forEach((function(e){var r=t[e];"string"==typeof r?(n["user.".concat(e)]=r,r!==a.resource["user.".concat(e)]&&(o=!0)):n["user.".concat(e)]=void 0})),o?[4,null===(r=e.provider)||void 0===r?void 0:r.shutdown()]:[3,2];case 1:return s.sent(),console.log("旧的 TracerProvider 已销毁"),e.provider=void 0,this.init(this.config,__assign$4(__assign$4({},this.resource),n)),console.log("新的 TracerProvider 已注册"),[3,3];case 2:console.log("用户信息未变更"),s.label=3;case 3:return[3,5];case 4:return i=s.sent(),console.error("销毁失败: ",i),[3,5];case 5:return[2]}}))}))},e}(),mechanismType,trackType,monitoringCode,levelType;!function(e){e.JS="js",e.RS="resource",e.UJ="unhandledrejection",e.HP="http",e.CS="cors",e.VUE="vue"}(mechanismType||(mechanismType={})),function(e){e[e.burialPoint=1]="burialPoint",e[e.monitor=2]="monitor",e[e.log=3]="log",e[e.full_buried_point=4]="full_buried_point"}(trackType||(trackType={})),function(e){e.JS="MONITOR_JS",e.PG="MONITOR_PG",e.RS="MONITOR_RS",e.PV="MONITOR_PV",e.HP="MONITOR_HP"}(monitoringCode||(monitoringCode={})),function(e){e.INFO="INFO",e.WARN="WARN",e.ERROR="ERROR",e.DEBUG="DEBUG"}(levelType||(levelType={}));var WebTrack=function(e){function t(r){var n=e.call(this,r)||this;if(n.openTelemetryManger=null,n.watchEvents=[],t.instance)return t.instance;t.instance=n,n.originalFetch=window.fetch,n.watchEvents=n.formatWatchEvents(r.watchEvents),n.terminal=r.terminal||"web";var o=localStorage.getItem("uuid");o?n.uuid=o:(n.uuid=v4(),localStorage.setItem("uuid",n.uuid));var i=__assign$4({host:"production"===r.env?"cn-hangzhou.log.aliyuncs.com":"cn-guangzhou.log.aliyuncs.com",project:"production"===r.env?"hl-webapp":"hl-webapp-sit",logstore:"production"===r.env?"hl-webapp-log":"hl-webapp-log-sit"},r.config);return i.project||console.warn("数据采集插件必须设置 project"),i.logstore||console.warn("数据采集插件必须设置 logstore"),n.track=new SlsTracker(i),n.init(),n.initOpenTelemetry(r.env,i.logstore,r.openTelemetry,r.businessInfo),n}return __extends$b(t,e),t.prototype.addEvent=function(e,t,r,n){e&&(e.addEventListener?e.addEventListener(t,r,n):e.attachEvent?e.attachEvent("on"+t,r):e["on"+t]=r)},t.prototype.formatWatchEvents=function(e){var t={resource:!0,js:!0,page:!0,pv:!0,xhr:!0,fetch:!0,clickMount:!1,pageView:!1};if(console.log("events",e),!e)return Object.keys(t).filter((function(e){return t[e]}));var r=Object.prototype.toString,n=function(e){return function(e,t){return r.call(e)==="[object ".concat(t,"]")}(e,"Boolean")},o="[object Object]"===r.call(e);if(Array.isArray(e))return e;if(o){var i=__assign$4(__assign$4({},t),e);return console.log("eventsInfo",i),Object.keys(i).filter((function(e){return!!n(i[e])&&i[e]}))}return Object.keys(t).filter((function(e){return t[e]}))},t.prototype.init=function(){var e=this,t={resource:this.recordResourceLoad,js:this.recordJSError,page:this.recordPageLoad,pv:this.recordCustomerPv,xhr:this.proxyXmlHttp,fetch:this.proxyFetch,clickMount:this.initClickHandler,pageView:this.initTimeOnPage};this.watchEvents.forEach((function(r){t[r].apply(e)}))},t.prototype.initOpenTelemetry=function(e,t,r,n){try{if(r){console.log(r);var o="object"==typeof n?n:{},i=this.globalData||{},a={};(null==i?void 0:i._project_)&&(a["project.name"]=i._project_),(null==i?void 0:i._project_name_)&&(a["project.cn"]=i._project_name_);var s=new OpenTelemetryManger(__assign$4(__assign$4({env:e,serviceName:t},r),{userInfo:o,projectInfo:a}));this.openTelemetryManger=s}else console.log("未开启openTelemetry配置")}catch(e){console.error("初始化openTelemetry失败"+e)}},t.prototype.getOpenTracerProvider=function(){var e;return null===(e=this.openTelemetryManger)||void 0===e?void 0:e.getProvider()},t.prototype.initTimeOnPage=function(){var e=this,t={url:window.location.href,loginTime:(new Date).getTime(),outTime:0,stayTime:0};function r(){var r=(new Date).getTime();t.outTime=r,t.stayTime=r-t.loginTime,t.stayTime>=1e3&&e.sendCustomerEvent({code:"VIEW",level:levelType.INFO,info:t,type:trackType.full_buried_point}),t.loginTime=r,t.url=window.location.href}window.addEventListener("load",(function(){t.loginTime=(new Date).getTime(),t.url=window.location.href})),window.addEventListener("popstate",(function(){r()})),this.addEvent(window,"hashchange",r),window.addHistoryListener("history",(function(){r()})),window.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&r(),"visible"===document.visibilityState&&(t.loginTime=(new Date).getTime(),t.url=window.location.href)}))},t.prototype.initClickHandler=function(){var e=this;function t(e){if(e)return e.matches(".hl-sls-track")?e:e.parentElement?t(e.parentElement):void 0}var r=function(r){try{var n=(null==r?void 0:r.target)||void 0,o=t(n);if(o&&(n=o),!n)return;var i=(null==n?void 0:n.dataset)||{},a=String((null==n?void 0:n.textContent)||"").slice(0,500);void 0===i.text&&(i.text=a);var s={id:(null==n?void 0:n.id)||"",classList:Array.from((null==n?void 0:n.classList)||""),tagName:(null==n?void 0:n.tagName)||"",text:a,dataset:i};e.sendCustomerEvent({code:"CLICK",level:levelType.INFO,info:s,type:trackType.full_buried_point})}catch(e){console.log(e)}};window.addEventListener("click",(function(e){r(e)}),!0)},t.prototype.getErrorKey=function(e){return e instanceof ErrorEvent?"Script error."===e.message?mechanismType.CS:mechanismType.JS:mechanismType.RS},t.prototype.recordResourceLoad=function(){var e=this;window.addEventListener("error",(function(t){try{if(!t)return;var r=t.target||t.srcElement;if(!(r instanceof HTMLElement))return;var n=r.localName,o="";if("link"===n)o=r.href;else o=r.src;e.sendCustomerEvent({code:monitoringCode.RS,level:levelType.ERROR,info:{resource_error_message:t,resource_url:o}})}catch(e){console.log(e)}}),!0)},t.prototype.recordJSError=function(){var e=console.error,t=!1,r=this;function n(e){var t;return e.level===levelType.WARN&&isNativeErrorMessage(null===(t=e.info)||void 0===t?void 0:t.js_error_obj)&&(e.level=levelType.ERROR),e}console.error=function(o){try{var i=arguments[0]&&arguments[0].message||o,a=null===location||void 0===location?void 0:location.href,s=arguments[0]&&arguments[0].stack;s||(s=arguments[0]),t||r.sendCustomerEvent(n({code:monitoringCode.JS,level:levelType.WARN,info:{js_type:"console.error",js_message:i,js_url:a,js_line_no:0,js_column_no:0,js_error_obj:s}}))}catch(e){console.log(e)}return e.apply(console,arguments)},window.onerror=function(e,n,o,i,a){try{var s=levelType.ERROR;("string"==typeof e&&"Script error."===e||"string"!=typeof e&&r.getErrorKey(e))&&(s=levelType.WARN),t=!0;var u=a?a.stack:null;r.sendCustomerEvent({code:monitoringCode.JS,level:s,info:{js_type:"window.onerror",js_message:e,js_url:n,js_line_no:o,js_column_no:i,js_error_obj:u}})}catch(e){console.log(e)}},window.onunhandledrejection=function(e){try{var t=e.reason instanceof Error?e.reason:new Error(JSON.stringify(e.reason)),o=JSON.stringify(t.stack).split("\\n");o=o?o[1]:o;var i=0,a=0;o&&(i=(o=o.match(/(\d+):(\d+)(\)")?$/))&&o[1]||0,a=o&&o[2]||0),function(e,t,o,i,a){var s=a||"",u="";if(e||""){u=JSON.stringify(s).split(": ")[0].replace('"',"")}r.sendCustomerEvent(n({code:monitoringCode.JS,level:levelType.WARN,info:{js_message:e,js_url:t,js_line_no:o,js_column_no:i,js_error_obj:a,js_error_type:u,js_type:"window.onunhandledrejection"}}))}(t.message,location.href,i,a,t.stack)}catch(t){console.log(t)}}},t.prototype.recordPageLoad=function(){var e=(new Date).getTime(),t=0,r=this;this.addEvent(window,"DOMContentLoaded",(function(){t=(new Date).getTime()-e})),this.addEvent(window,"load",(function(){try{var n=(new Date).getTime()-e,o={};o.pg_fT=t,o.pg_pT=n;var i=window.performance||window.msPerformance||window.webkitPerformance||window.mozPerformance;if(i){var a=i.timing,s=a;if("function"==typeof window.PerformanceNavigationTiming)try{var u=i.getEntriesByType("navigation")[0];u&&(a=u)}catch(e){}o.pg_reT=a.redirectEnd-a.redirectStart,o.pg_dnsT=a.domainLookupEnd-a.domainLookupStart,o.pg_tcpT=a.connectEnd-a.connectStart,o.pg_ttfbT=a.navigationStart?a.responseStart-a.navigationStart:s.responseStart-s.navigationStart,o.pg_reqT=a.responseEnd-a.responseStart,o.pg_dnsCT=a.domainLookupStart-a.fetchStart,o.pg_domT=a.domComplete-a.domInteractive,o.pg_wT=(a.domInteractive||a.domLoading)-a.fetchStart,o.pg_domRT=a.domContentLoadedEventEnd-a.fetchStart,i&&i.navigation&&(o.accessType=i.navigation.type),i.memory&&(o.pg_mTotal=i.memory.totalJSHeapSize,o.pg_mUse=i.memory.usedJSHeapSize)}o.pg_referrer=document.referrer,r.sendCustomerEvent({code:monitoringCode.PG,level:levelType.INFO,info:__assign$4({},o)})}catch(e){console.log("pg统计错误",e)}}))},t.prototype.recordCustomerPv=function(){var e=window.location,t=e.href,r=e.hash,n=this;function o(){var o=e.href,i=e.hash;n.sendCustomerEvent({code:monitoringCode.PV,level:levelType.INFO,info:{pv_old_url:t,pv_old_hash:r,pv_new_url:o,pv_new_hash:i}}),t=o,r=i}window.addHistoryListener("history",(function(){o()})),"onhashchange"in window?this.addEvent(window,"hashchange",o):setInterval((function(){r!==e.hash&&o()}),100),o()},t.prototype.proxyXmlHttp=function(){var e=this,t=window.XMLHttpRequest,r=t.prototype.open;t.prototype.open=function(e,t,n,o,i){return this.logData={method:e,url:t,async:n},r.apply(this,arguments)};var n=t.prototype.send;t.prototype.send=function(t){var r=this;try{var o=Date.now();this.addEventListener("error",(function(n){var i=Date.now()-o;if(/^https:\/\/hl-webapp(-sit)?\.cn-(guangzhou|hangzhou)\.log\.aliyuncs\.com/.test(r.logData.url))return!1;e.sendCustomerEvent({code:monitoringCode.HP,level:levelType.ERROR,info:{ajax_type:"xhr",ajax_url:r.logData.url,ajax_method:r.logData.method,ajax_status:r.status,ajax_duration:i,ajax_response:r.response?JSON.stringify(r.response):"",ajax_params:t||""}})}),!1)}catch(e){console.log("xhr",e)}return n.apply(this,arguments)}},t.prototype.proxyFetch=function(){var e=this;window.fetch=function(t,r){var n,o,i=this;r?(n=r.method||"get",o=r.body||""):(n="get",o="");var a={startTime:Date.now(),url:t,method:n,params:o};return e.originalFetch.apply(window,arguments).then((function(e){return __awaiter$3(i,void 0,void 0,(function(){return __generator$3(this,(function(t){return[2,e]}))}))})).catch((function(t){try{var r=Date.now()-a.startTime;e.sendCustomerEvent({code:monitoringCode.HP,level:levelType.ERROR,info:{ajax_type:"fetch",ajax_url:a.url,ajax_method:a.method,ajax_status:"error",ajax_duration:r,ajax_response:t.message,ajax_params:a.params||""}})}catch(e){console.log("fetch",e)}throw t}))}},t.prototype.setBusinessInfo=function(t){var r;e.prototype.setBusinessInfo.call(this,t);try{"object"==typeof this.businessInfo&&(null===(r=this.openTelemetryManger)||void 0===r||r.updateUserInfo(this.businessInfo))}catch(e){console.error("更新openTelemetry的resource报错：",e)}},t}(Track);return WebTrack}));
