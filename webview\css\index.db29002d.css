.quit-class[data-v-14e3147a] {
  text-align: center;
}
.quit-class .quit-class-title[data-v-14e3147a] {
  font-size: 1.58333rem;
  color: #ffffff;
  line-height: 1.58333rem;
  letter-spacing: 0.08333rem;
}
.quit-class .quit-class-line[data-v-14e3147a] {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 1.66667rem auto;
}
.quit-class .quit-class-line .quit-class-line-item[data-v-14e3147a] {
  width: 0.41667rem;
  height: 0.08333rem;
  background: #ffffff;
  margin-right: 0.41667rem;
}
.quit-class .quit-class-btns .el-button[data-v-14e3147a] {
  width: 12.08333rem;
  height: 4.33333rem;
  background: rgba(255, 255, 255, 0.45);
  border-radius: 1.58333rem;
  font-weight: 500;
  font-size: 1.58333rem;
  color: #5f52e3;
  letter-spacing: 0.16667rem;
  line-height: 0rem;
  border: unset;
}
.quit-class .quit-class-btns .quit-button[data-v-14e3147a] {
  background: #29da80;
  color: #ffffff;
}
.quit-class-dialog[data-v-49b97567] .dialog-container {
  position: absolute;
  bottom: 15.33333rem;
  right: 12.5rem;
  width: 31.25rem;
  min-height: unset;
  padding: 2rem 0 1.66667rem;
}
.quit-class-dialog[data-v-49b97567] .dialog-title {
  display: none;
}
