const baseSize = 12;
function calcHtmlFontSize(autoset = false) {
  let scale = window.screen.width / 1920;
  scale = Math.min(scale, 3);
  const fontSize = baseSize * scale;
  if (autoset) {
    document.documentElement.style.fontSize = `${fontSize}px`;
  }
  return {
    scale,
    fontSize
  };
}
function get4kScaling() {
  return window.screen.width / 4096;
}
export {
  calcHtmlFontSize as c,
  get4kScaling as g
};
