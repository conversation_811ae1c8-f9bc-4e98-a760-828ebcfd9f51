#include "WindowActivationBlocker.h"
#include <QEvent>
#include <QApplication>
#include <QDebug>

#ifdef Q_OS_WIN
#include <windows.h>
#endif


// WindowActivationBlocker 实现

WindowActivationBlocker::WindowActivationBlocker(bool allowActivation, QObject *parent)
    : QObject(parent)
    , m_allowActivation(allowActivation)
    , m_windowManager(nullptr)
    , m_isDestroying(false)
{
    // 安装原生事件过滤器
    QApplication::instance()->installNativeEventFilter(this);
}

WindowActivationBlocker::~WindowActivationBlocker()
{
    // 清除窗口管理器引用，防止野指针
    m_windowManager = nullptr;

    // 移除原生事件过滤器
    if (QApplication::instance()) {
        QApplication::instance()->removeNativeEventFilter(this);
    }
}

void WindowActivationBlocker::setAllowActivation(bool allow)
{
    m_allowActivation = allow;
}

bool WindowActivationBlocker::eventFilter(QObject *obj, QEvent *event)
{
    // 这里可以处理Qt层面的事件
    return QObject::eventFilter(obj, event);
}

bool WindowActivationBlocker::nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result)
{
#ifdef Q_OS_WIN
    if (eventType == "windows_generic_MSG") {
        MSG *msg = static_cast<MSG*>(message);

        // 检查是否是ZIndexManager注册的窗口
        if (!isZIndexManagedWindow(msg->hwnd)) {
            // 对于非ZIndexManager管理的窗口（如QFileDialog），不拦截任何消息
            return false;
        }

        if (!m_allowActivation) {
            switch (msg->message) {
                case WM_WINDOWPOSCHANGING: {
                    WINDOWPOS* wp = (WINDOWPOS*)msg->lParam;
                    if (wp && !(wp->flags & SWP_NOZORDER)) {
                        wp->flags |= SWP_NOZORDER;  // 阻止Z-order变化
                    }
                    return true;  // 标记为已处理
                }

                case WM_SHOWWINDOW: {
                    // 窗口显示/隐藏时，通知ZIndexManager重新应用层级
                    emit windowVisibilityChanged();
                    break;  // 不拦截，让系统正常处理
                }

                // case WM_ACTIVATE:
                //     *result = 0;
                //     return true;

                // case WM_NCACTIVATE:
                //     *result = 0;
                //     return true;

                // case WM_ACTIVATEAPP:
                //     *result = 0;
                //     return true;

                // case WM_MOUSEACTIVATE:
                //     *result = MA_NOACTIVATE;
                //     return true;

                // case WM_SETFOCUS:
                //     SetFocus(nullptr);  // 强制失去焦点
                //     *result = 0;
                //     return true;
            }
        }
    }
#endif

    return false;
}

#ifdef Q_OS_WIN
bool WindowActivationBlocker::isZIndexManagedWindow(HWND hwnd)
{
    // 如果没有设置窗口管理器引用，则不拦截任何窗口
    if (!m_windowManager) {
        return false;
    }

    // 使用窗口管理器接口检查窗口是否被管理
    return m_windowManager->isWindowManaged(hwnd);
}
#endif




