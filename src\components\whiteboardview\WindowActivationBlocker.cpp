#include "WindowActivationBlocker.h"
#include <QEvent>
#include <QApplication>
#include <QDebug>
#include <QTimer>

#ifdef Q_OS_WIN
#include <windows.h>
#endif


// WindowActivationBlocker 实现

WindowActivationBlocker::WindowActivationBlocker(bool allowActivation, QObject *parent)
    : QObject(parent)
    , m_allowActivation(allowActivation)
    , m_windowManager(nullptr)
    , m_isDestroying(false)
{
    // 安装原生事件过滤器
    QApplication::instance()->installNativeEventFilter(this);
}

WindowActivationBlocker::~WindowActivationBlocker()
{
    // 清除窗口管理器引用，防止野指针
    m_windowManager = nullptr;

    // 移除原生事件过滤器
    if (QApplication::instance()) {
        QApplication::instance()->removeNativeEventFilter(this);
    }
}

void WindowActivationBlocker::setAllowActivation(bool allow)
{
    m_allowActivation = allow;
}

bool WindowActivationBlocker::eventFilter(QObject *obj, QEvent *event)
{
    // 这里可以处理Qt层面的事件
    return QObject::eventFilter(obj, event);
}

bool WindowActivationBlocker::nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result)
{
#ifdef Q_OS_WIN
    if (eventType == "windows_generic_MSG") {
        MSG *msg = static_cast<MSG*>(message);

        // 检查是否是ZIndexManager注册的窗口
        if (!isZIndexManagedWindow(msg->hwnd)) {
            // 对于非ZIndexManager管理的窗口（如QFileDialog），不拦截任何消息
            return false;
        }

        // 方案三：消息类型细化拦截
        // 只拦截影响Z-order的消息，不拦截影响焦点和输入的消息
        // 这样既能维护窗口层级管理，又能确保QCefView等组件正常接收键盘输入

        if (!m_allowActivation) {
            switch (msg->message) {
                case WM_WINDOWPOSCHANGING: {
                    WINDOWPOS* wp = (WINDOWPOS*)msg->lParam;
                    if (wp && !(wp->flags & SWP_NOZORDER)) {
                        wp->flags |= SWP_NOZORDER;  // 阻止Z-order变化
                    }
                    return true;  // 标记为已处理
                }

                case WM_SHOWWINDOW: {
                    // 窗口显示/隐藏时，通知ZIndexManager重新应用层级
                    emit windowVisibilityChanged();
                    break;  // 不拦截，让系统正常处理
                }

                case WM_MOUSEACTIVATE: {
                    // 关键解决方案：允许激活但不改变Z-order
                    // MA_ACTIVATEANDEAT: 激活窗口但不传递鼠标消息给子窗口
                    // 这样既能获得焦点支持键盘输入，又能通过WM_WINDOWPOSCHANGING阻止Z-order变化
                    *result = MA_ACTIVATEANDEAT;
                    return true;
                }

                case WM_ACTIVATE: {
                    // 允许窗口激活，但激活后立即重新应用Z-order
                    // 使用定时器延迟处理，确保激活完成后再恢复层级
                    QTimer::singleShot(10, [this]() {
                        emit windowVisibilityChanged();
                    });
                    break;  // 不拦截，让系统正常处理激活
                }

                // case WM_NCACTIVATE:
                //     // 非客户区激活，通常不影响Z-order，允许正常处理
                //     break;

                // case WM_ACTIVATEAPP:
                //     // 应用程序级激活，不拦截
                //     break;

                // case WM_SETFOCUS:
                //     // 焦点设置，不拦截以支持键盘输入
                //     break;
            }
        }
    }
#endif

    return false;
}

#ifdef Q_OS_WIN
bool WindowActivationBlocker::isZIndexManagedWindow(HWND hwnd)
{
    // 如果没有设置窗口管理器引用，则不拦截任何窗口
    if (!m_windowManager) {
        return false;
    }

    // 使用窗口管理器接口检查窗口是否被管理
    return m_windowManager->isWindowManaged(hwnd);
}
#endif




