import { a as Bridge, _ as _export_sfc, d as initLoggerWidget, p as pinia } from "./index.2e7c5603.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
/* empty css                     */import { d as defineComponent, r as ref, b as openBlock, e as createBlock, f as withCtx, j as createBaseVNode, p as createVNode, G as createTextVNode, D as createCommentVNode, ad as createApp } from "./bootstrap.ab073eb8.js";
import { D as Dialog } from "./index.533b593c.js";
import "./base.676dddc3.js";
import { E as ElButton } from "./el-button.a9e8e4ae.js";
import { Q as QT_CEF_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.1c1fd1ce.js";
import "./use-form-common-props.6b0d7cd2.js";
const _hoisted_1 = { class: "class-summary-btns flex flex-ac flex-jc" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const settingDialogVisible = ref(true);
    function onCloseAISummary() {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.CLOSE);
    }
    async function onSetting() {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.QT_OPEN_CLASSROOM_SETTING_DIALOG);
      onCloseAISummary();
    }
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      const _component_Dialog = Dialog;
      return settingDialogVisible.value ? (openBlock(), createBlock(_component_Dialog, {
        key: 0,
        class: "class-summary-dialog",
        title: "AI课堂小结",
        visible: settingDialogVisible.value,
        "show-close": "",
        onClose: onCloseAISummary
      }, {
        default: withCtx(() => [
          _cache[2] || (_cache[2] = createBaseVNode("div", { class: "class-summary-text" }, "功能未开启，请在【设置】中启用【AI课堂小结】功能", -1)),
          createBaseVNode("div", _hoisted_1, [
            createVNode(_component_el_button, { onClick: onCloseAISummary }, {
              default: withCtx(() => _cache[0] || (_cache[0] = [
                createTextVNode("取消")
              ])),
              _: 1
            }),
            createVNode(_component_el_button, {
              class: "set-button",
              onClick: onSetting
            }, {
              default: withCtx(() => _cache[1] || (_cache[1] = [
                createTextVNode("去设置")
              ])),
              _: 1
            })
          ])
        ]),
        _: 1
      }, 8, ["visible"])) : createCommentVNode("", true);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_55f88620_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-55f88620"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
