.event-pointer-none[data-v-9e6adfe8] {
  pointer-events: none;
}
.classroom-container[data-v-9e6adfe8] {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
  touch-action: none;
}
.classroom-office-view[data-v-9e6adfe8] {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.whiteboard[data-v-9e6adfe8] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.whiteboard-toolbar[data-v-9e6adfe8] {
  position: absolute;
  bottom: 1.58333rem;
  left: 0;
  right: 0;
}
.tool-bar[data-v-9e6adfe8] {
  position: absolute;
  right: 0;
  top: 11.91667rem;
  z-index: var(--z-index-toolbar);
}
.save-black-board-dialog[data-v-9e6adfe8] {
  z-index: var(--z-index-dialog);
}
.save-black-board-dialog[data-v-9e6adfe8] .dialog-title {
  height: 1.91667rem;
  font-size: 1.58333rem;
  line-height: 1.91667rem;
  letter-spacing: 0.08333rem;
  padding-bottom: 1.91667rem;
  padding-top: 1.58333rem;
}
.save-black-board-dialog[data-v-9e6adfe8] .dialog-container {
  min-width: 29.16667rem;
  min-height: 17.91667rem;
}
.save-black-board-dialog.positino-right[data-v-9e6adfe8] .dialog-container {
  position: absolute;
  right: 10.16667rem;
  top: 27.16667rem;
}
.whiteboard-wrap[data-v-9e6adfe8] {
  position: relative;
  width: 100%;
  height: 100%;
}
.whiteboard-green[data-v-9e6adfe8] {
  background-color: #234641;
}
.whiteboard-white[data-v-9e6adfe8] {
  background-color: #fff;
}
.qt-connect-error[data-v-9e6adfe8] {
  position: absolute;
  right: 1.58333rem;
  bottom: 1.58333rem;
  height: 3.66667rem;
  border-radius: 3.66667rem;
  background-color: var(--color-black-light-65);
  font-size: 1.33333rem;
  line-height: 3.66667rem;
  display: flex;
}
.qt-connect-error-button[data-v-9e6adfe8] {
  color: #fff;
  border-radius: 3.66667rem;
  cursor: pointer;
  white-space: nowrap;
  height: 3.66667rem;
  padding: 0 1.58333rem;
}
.qt-connect-error-button[data-v-9e6adfe8]:hover {
  background-color: var(--color-black-light-45);
}
.qt-connect-error-button[data-v-9e6adfe8]:active {
  background-color: var(--color-black-light-45);
}
.qt-connect-error-button.active[data-v-9e6adfe8] {
  background-color: var(--color-primary);
}
.generate-img-box[data-v-9e6adfe8] {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: -999;
  top: -100%;
  right: -100%;
}
