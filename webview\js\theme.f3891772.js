import { d as defineComponent, c as computed, b as openBlock, m as createElementBlock, j as createBaseVNode, a1 as defineStore } from "./bootstrap.ab073eb8.js";
import { _ as _export_sfc } from "./index.2e7c5603.js";
const checkPng = "" + new URL("../png/icon-radio-check.9736d182.png", import.meta.url).href;
const unCheckPng = "data:image/png;base64,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";
const _hoisted_1 = { class: "radio-container" };
const _hoisted_2 = ["src"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const handleClick = () => {
      emit("update:modelValue", !props.modelValue);
    };
    const icon = computed(() => {
      return props.modelValue ? checkPng : unCheckPng;
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createBaseVNode("img", {
          class: "radio-icon",
          src: icon.value,
          alt: "",
          onClick: handleClick
        }, null, 8, _hoisted_2)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_a759653e_lang = "";
const Radio = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-a759653e"]]);
const baseSize = 12;
const useThemeStore = defineStore("zhhb-theme", {
  state: () => ({
    scale: 1,
    htmlFontSize: 12,
    theme: "ocean"
  }),
  actions: {
    /**
     * 计算 html 字体大小
     */
    calcHtmlFontSize() {
      let scale = document.documentElement.clientWidth / 1920;
      scale = Math.min(scale, 2);
      this.scale = scale;
      const fontSize = baseSize * scale;
      this.htmlFontSize = fontSize;
      document.documentElement.style.fontSize = `${fontSize}px`;
    },
    setTheme(theme) {
      this.theme = theme;
      document.documentElement.setAttribute("data-theme", theme);
    }
  }
});
export {
  Radio as R,
  useThemeStore as u
};
