.tooltip-container[data-v-f683b4b3] {
  width: 6.25rem;
  background: rgba(0, 0, 0, 0.65);
  border-radius: 3.16667rem 0 0 3.16667rem;
  padding: 1.58333rem 0.91667rem;
  box-sizing: border-box;
  font-size: 1.5rem;
  color: #ffffff;
  line-height: 1.75rem;
}
.tooltip-container .tool-icon[data-v-f683b4b3] {
  width: 2.58333rem;
  height: 2.58333rem;
}
.tooltip-container .tool-name[data-v-f683b4b3] {
  margin-top: 0.41667rem;
}
.tooltip-container .resource-button[data-v-f683b4b3] {
  width: 100%;
  padding-bottom: 1.58333rem;
  border-bottom: 0.08333rem dashed rgba(255, 255, 255, 0.4);
}
.tooltip-container .class-end-button[data-v-f683b4b3] {
  width: 100%;
  padding-top: 1.58333rem;
  border-top: 0.08333rem dashed rgba(255, 255, 255, 0.4);
}
.tooltip-container .tool-list[data-v-f683b4b3] {
  padding: 1.58333rem 0;
  gap: 1.58333rem;
}
.tooltip-container .tool-item .tool-name[data-v-f683b4b3] {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 5.41667rem;
}
.tooltip-container .tool-item .tool-name .tool-char[data-v-f683b4b3] {
  box-sizing: border-box;
  text-align: center;
  flex-shrink: 0;
  gap: 0.16667rem;
}
.tooltip-container .tool-item .tool-name[data-v-f683b4b3]:has(.tool-char:nth-child(4)) {
  width: 4.16667rem;
  /* 有4个字时让每行 2 个 */
}
.tooltip-container .system-operator-container[data-v-f683b4b3] {
  margin-top: 1.16667rem;
  font-size: 2.66667rem;
  gap: 1.16667rem;
  color: #ffffff;
}
.tooltip-container .svg-wrapper[data-v-f683b4b3] {
  width: 3.41667rem;
  height: 3.41667rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tooltip-container .svg-wrapper.selected[data-v-f683b4b3] {
  background: rgba(95, 82, 227, 0.25);
}
.setting-popover.el-popover.el-popper {
  background: #000000;
  background: rgba(0, 0, 0, 0.65);
  border: none;
  width: auto !important;
  min-width: 10rem;
  border-radius: 1.58333rem;
  font-size: 2.66667rem;
  padding: 0.66667rem 1.58333rem;
  box-sizing: border-box;
  color: #ffffff;
}
.setting-popover .setting-wrapper {
  gap: 1.58333rem;
}
.container[data-v-4688ee50] {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}
.tool-bar[data-v-4688ee50] {
  position: absolute;
  right: 0;
  top: 16.66667rem;
  height: 8.33333rem;
  width: 8.33333rem;
  background: var(--color-white);
}

#app {
  background: transparent !important;
}
