.el-switch{--el-switch-on-color:var(--el-color-primary);--el-switch-off-color:var(--el-border-color);align-items:center;display:inline-flex;font-size:1.16667rem;height:2.66667rem;line-height:1.66667rem;position:relative;vertical-align:middle}.el-switch.is-disabled .el-switch__core,.el-switch.is-disabled .el-switch__label{cursor:not-allowed}.el-switch__label{color:var(--el-text-color-primary);cursor:pointer;display:inline-block;font-size:1.16667rem;font-weight:500;height:1.66667rem;transition:var(--el-transition-duration-fast);vertical-align:middle}.el-switch__label.is-active{color:var(--el-color-primary)}.el-switch__label--left{margin-right:0.83333rem}.el-switch__label--right{margin-left:0.83333rem}.el-switch__label *{display:inline-block;font-size:1.16667rem;line-height:1}.el-switch__label .el-icon{height:inherit}.el-switch__label .el-icon svg{vertical-align:middle}.el-switch__input{height:0;margin:0;opacity:0;position:absolute;width:0}.el-switch__input:focus-visible~.el-switch__core{outline:0.16667rem solid var(--el-switch-on-color);outline-offset:0.08333rem}.el-switch__core{align-items:center;background:var(--el-switch-off-color);border:0.08333rem solid var(--el-switch-border-color,var(--el-switch-off-color));border-radius:0.83333rem;box-sizing:border-box;cursor:pointer;display:inline-flex;height:1.66667rem;min-width:3.33333rem;outline:none;position:relative;transition:border-color var(--el-transition-duration),background-color var(--el-transition-duration)}.el-switch__core .el-switch__inner{align-items:center;display:flex;height:1.33333rem;justify-content:center;overflow:hidden;padding:0 0.33333rem 0 1.5rem;transition:all var(--el-transition-duration);width:100%}.el-switch__core .el-switch__inner .is-icon,.el-switch__core .el-switch__inner .is-text{color:var(--el-color-white);font-size:1rem;overflow:hidden;text-overflow:ellipsis;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap}.el-switch__core .el-switch__action{align-items:center;background-color:var(--el-color-white);border-radius:var(--el-border-radius-circle);color:var(--el-switch-off-color);display:flex;height:1.33333rem;justify-content:center;left:0.08333rem;position:absolute;transition:all var(--el-transition-duration);width:1.33333rem}.el-switch.is-checked .el-switch__core{background-color:var(--el-switch-on-color);border-color:var(--el-switch-border-color,var(--el-switch-on-color))}.el-switch.is-checked .el-switch__core .el-switch__action{color:var(--el-switch-on-color);left:calc(100% - 1.41667rem)}.el-switch.is-checked .el-switch__core .el-switch__inner{padding:0 1.5rem 0 0.33333rem}.el-switch.is-disabled{opacity:.6}.el-switch--wide .el-switch__label.el-switch__label--left span{left:0.83333rem}.el-switch--wide .el-switch__label.el-switch__label--right span{right:0.83333rem}.el-switch .label-fade-enter-from,.el-switch .label-fade-leave-active{opacity:0}.el-switch--large{font-size:1.16667rem;height:3.33333rem;line-height:2rem}.el-switch--large .el-switch__label{font-size:1.16667rem;height:2rem}.el-switch--large .el-switch__label *{font-size:1.16667rem}.el-switch--large .el-switch__core{border-radius:1rem;height:2rem;min-width:4.16667rem}.el-switch--large .el-switch__core .el-switch__inner{height:1.66667rem;padding:0 0.5rem 0 1.83333rem}.el-switch--large .el-switch__core .el-switch__action{height:1.66667rem;width:1.66667rem}.el-switch--large.is-checked .el-switch__core .el-switch__action{left:calc(100% - 1.75rem)}.el-switch--large.is-checked .el-switch__core .el-switch__inner{padding:0 1.83333rem 0 0.5rem}.el-switch--small{font-size:1rem;height:2rem;line-height:1.33333rem}.el-switch--small .el-switch__label{font-size:1rem;height:1.33333rem}.el-switch--small .el-switch__label *{font-size:1rem}.el-switch--small .el-switch__core{border-radius:0.66667rem;height:1.33333rem;min-width:2.5rem}.el-switch--small .el-switch__core .el-switch__inner{height:1rem;padding:0 0.16667rem 0 1.16667rem}.el-switch--small .el-switch__core .el-switch__action{height:1rem;width:1rem}.el-switch--small.is-checked .el-switch__core .el-switch__action{left:calc(100% - 1.08333rem)}.el-switch--small.is-checked .el-switch__core .el-switch__inner{padding:0 1.16667rem 0 0.16667rem}.container[data-v-68a1b17a] {
  height: 32.91667rem;
  padding: 3.16667rem 3.75rem 1.58333rem 3.75rem;
  box-sizing: border-box;
  position: relative;
}
.container .header[data-v-68a1b17a] {
  font-size: 2.16667rem;
  color: rgba(0, 0, 0, 0.85);
  line-height: 3.08333rem;
  justify-content: space-between;
  font-weight: 500;
}
.container[data-v-68a1b17a] .el-switch {
  width: 5.58333rem;
  height: 3rem;
}
.container[data-v-68a1b17a] .el-switch .el-switch__core {
  width: 5.58333rem;
  height: 3rem;
  border-radius: 1.5rem;
}
.container[data-v-68a1b17a] .el-switch .el-switch__action {
  width: 2.33333rem;
  height: 2.33333rem;
}
.container[data-v-68a1b17a] .el-switch.is-checked .el-switch__core .el-switch__action {
  left: calc(100% - 2.33333rem);
}
.container .desc[data-v-68a1b17a] {
  margin-top: 2.5rem;
  font-weight: 400;
  font-size: 2.16667rem;
  color: rgba(0, 0, 0, 0.65);
  line-height: 3.08333rem;
}
.setting-dialog[data-v-d0500c4c] .dialog-container {
  width: 93.75rem;
  padding-bottom: 3.08333rem;
}
.setting-dialog .setting-container[data-v-d0500c4c] {
  margin: 0 auto;
  width: calc(100% - 6.33333rem);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3.16667rem;
  min-height: 29.66667rem;
}
.setting-dialog .setting-menu[data-v-d0500c4c] {
  width: 15.66667rem;
  padding: 3.16667rem 2.33333rem;
  font-weight: 600;
  font-size: 2.16667rem;
  color: var(--text-color-regular);
  line-height: 3.08333rem;
  box-sizing: border-box;
  gap: 1.66667rem;
  white-space: nowrap;
}
.setting-dialog .setting-menu .selected[data-v-d0500c4c] {
  color: var(--color-primary);
}
.setting-dialog .setting-content[data-v-d0500c4c] {
  width: calc(100% - 15.66667rem);
  background: #ffffff;
  border-radius: 3.16667rem;
}
.setting-dialog .badge[data-v-d0500c4c] {
  position: relative;
}
.setting-dialog .badge[data-v-d0500c4c]::after {
  content: '';
  position: absolute;
  left: 4.58333rem;
  top: 0.66667rem;
  width: 0.58333rem;
  height: 0.58333rem;
  background: #ff3a3a;
  border-radius: 50%;
}
