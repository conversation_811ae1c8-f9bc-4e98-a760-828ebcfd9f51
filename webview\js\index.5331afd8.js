import "./base.676dddc3.js";
import "./index.5a98c998.js";
import { E as <PERSON>P<PERSON><PERSON> } from "./el-popover.8564ddf0.js";
import { _ as __vite_glob_0_5, b as __vite_glob_0_21, a as __vite_glob_0_23 } from "./<EMAIL>";
import { S as SvgIcon } from "./index.e0df5fdb.js";
import { d as defineComponent, r as ref, b as openBlock, m as createElementBlock, j as createBaseVNode, F as Fragment, R as renderList, k as normalizeClass, p as createVNode, f as withCtx, H as toDisplayString } from "./bootstrap.ab073eb8.js";
import { _ as _export_sfc } from "./index.2e7c5603.js";
import "./index.1c1fd1ce.js";
import "./index.4d07c967.js";
import "./index.4e3d2b08.js";
import "./typescript.063380fa.js";
import "./use-form-common-props.6b0d7cd2.js";
import "./isUndefined.a6a5e481.js";
import "./hlwhiteboard.b54f17ff.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
const __vite_glob_0_0 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_1 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_2 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_3 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_4 = "" + new URL("../webp/audio.f061b984.webp", import.meta.url).href;
const __vite_glob_0_6 = "data:image/webp;base64,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";
const __vite_glob_0_7 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _imports_1 = "data:image/webp;base64,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";
const __vite_glob_0_9 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _imports_0 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_11 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_12 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_13 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_14 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_15 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_16 = "data:image/webp;base64,UklGRtQIAABXRUJQVlA4WAoAAAAwAAAAVwAAVwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhM5gYAAC9XwBUQ7+ZItm3TeffbNpvhzDOXNNm08xHbGML8u5FkW1Xuud/dDSckhGXDTkiJ3d1dFbZt2wYlO11AzaZtk6SddcctWdNMQ6sxI0iFxNFAEdbJRFck4WQXmmFuW0YwisCu7vXkmNpYUiOzPSOTVjVIHB0ZRxTKwJAkugYtqNPRqS4uRZylYwx+Tz45e5ehsHJ6usjEXkOUifkX2dm6u3bVEKyD+84dli1zbj266MytEqneo1fHZvviY6gyXd0NAMJgAGBUUSSWgEAYDIEwAEzMevXPyvHgeI7eEVlGqozNJEkcvJQ43blm796t29AUHHlXzh48NktzbhmeHZLIymnhBDimN7+182huPwDc/JW42FJELoCoa+spYMkARM/Qi09Wzsqckzk8nqkjZ2InSWJqriVH36AIAMTPDwAgEPCP5Q/JCwfAP7r/JQiA9e3/16d1ztzdNzp3335zd3d3d1fGyUmVMljG6kaTBfexjCAtNQpLXRk5nOr9v5Dfl1z9/dKHexDRfwiSJLdttgooHkgCLUeGHwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPjx7du3bt26dev2rZshIB3HT1Jov7hx42bQBwvUAGk5fp48169du37j2rVr169dD6r0HMlz7YFrKysrD8TnXVupHZeeI3mefP/Kyv3VlZW4CgBpOZ6cxoFPa2pqqlaXg75arVar1Wo1keOTTU1NT0tv8uXl5aU11f+Wl5eXEzk+lO5SS0tLS1eXri4tXQkFiRwp58qVK1euXL4SzHS1Jkjk+GC6uXT50qVLl1bPC6vVPpHjA+nmwoWLFy6G1YX4vESO96eb8+fPnz8X92djwLnziRzvSzdnz549e/ZMUJ09V5spkeO96eb3Z06fPn369OnTp8/E1ep4Eseb0s2XKpVKpVKpVCqVSqVyqlJJ4nh5unnpYmVxcXHxv3WOS+JI+6//wsJiUC0sLCzGMyVxpJ0fzZ+cX5hfWDi5cPLkQiy4c8fb0s5D54/Ph9XxE/Pz8/Mn7tyR/r/Bzx47evzY8WNHjx8/fuzY8dp5d+p4cwP+vx49eiSojh4NBXfomGvE/+1HHJ47PDc3dySo5ubm5u7M8fCGXBA8+/DsoUOzh2YPHT586FAseLyn13PEC8y+oEFXGi+enp2Znp2ZnZmZic+LKY+q44hJr2/YJczzpqemp6enp1dniquAssYRVG9s4LXRE8vlcrlcLpfL5XI5kNR1vLKhF133/HlycnJycnJycnLyNXjPf+owntLoy7mvlkqlUqlUKpVKf/x0qR7iQY2/TnxhaWJiYmJi4t8TEyEgdnz57rgC/dn4v8Ynxsfj8wLAxPiz7pZL2+eOjY2NjY6Nj8bnjY5+7u65ZP72yMjI6MjwyPDo8PDoyOjIM++SrRcNDw0NDQ0NDw2uAQwPfeOuaN8ZGAz6gYE/fCoABMc9ruFbj+nv6+sf6O/v6+97LT7ct9bxqga3l/X09fb19PT29vb1BpB6js80tH2k2NPd3dPTXYyrIHUdv2xg+0qxWCwWi8VisVj8ZzGg1HUUftOw9vV8oZAv5AuFQqFQKOQDSx1HvMCvGtQ+kT+Yz3fl8/l8Pp/P58PUdRzMH/x8Q9orug50dXV1dXUd7AoXCFLXEbte0oD2kP379+8/EFarfTxTwKnnCF0NyO/27tsXVHvj8/bu3bc34NRzhK7vpt6ev3fP7rjfvWdPeNzePV/ED+s41roelnZ27dq9a+euWlUDxDMlcHwr5fbgnTt27NixY+eaanU8iSPlvGHbtm3btm/bti1YoAZI4nh0uvlm57bOzs7Of9Sq2nE1QhLHq9PN33O5zqDK5XKdnblcIsdb0k1HrqOjI9eR+1gURVEURVEURVEURVEURVEURVEURV/IdeRyf8u9O+V0dLR3dLQnxT2yPda9K920t7e3t7W/NbHua+1/bWt/Z7ppa9va1rb1dYl1H411Kae1tbV1S+ufEuu2tLa2bn1Hutm8efOmzVs2/fqxiWxP+u2mWPf29PY99TnPec6mjZs2bqhz3MaNiRwfX7du3TPWbCX79Bs2bNiwvm6VluMJyZPNZrPZbDabzd6XvS+bzabnSJ5MJpPJZDKZTCaTubdWpedInp+2tLS0tLS03BvO9JeWtBzfT2HHD1qam5ubm5trM4WAdBzf+/+h1zoA";
const __vite_glob_0_17 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const __vite_glob_0_18 = "data:image/webp;base64,UklGRogEAABXRUJQVlA4WAoAAAAwAAAAQQAAQQAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMmQIAAC9BQBAQF8K2kSRFPfqgmRndD/Fd9mjTYBVJspNdggeM5OANTzjI8B46+MUB4zqSbSU6hxclf+4QjrtmRSSyexi2jaQ4uZmFu2eGyCZpfeHbB+7iZpkvkjHxIzeal4qsLq04B+LGVh6u+NIn49oHftU9Rzw/FLdOfegwmzZcPW4dpqRePxMOxCvd6OKi/0dgpV+6TmoMMIDKADIl/fWbTx9meZVnACTRxnn0Sg8EYYYBZCK0mSGgsAowY4SENJBIiS1AIP0SMREkujAhwdzKpwsTXuUbZNm2nTZSMzMzScVczdy5e/6TCqqlxr+I/jtw28aRhPH2ot29m/IFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA1pMkSZIk6wOWNpNkxcJm73xMGsrHvjlIsntyqcjpwyQHPXMhydmVLjeTXOjIh+Reg8yb5ENHkrTIXJ3a9KXJphOnHr4qpZTkSymllFJKKaWUUuZtMrvSq4enfmLpZZIkSZIkSZIkTT+ol635nHQsn5tfevotNH6Kd915tzpnk76l6ad+NICjpv3dd/9V2b+ES/sDebvE6u0otpdqbY/h/Qqv9x1oF7t4/8q82Ahez+l8m0wmkzMzg9c/T6211lprrbXWWmu9NrP5PpnMykzhWoNZn1r28mSeOwMb3McLrA3kxgKPRrb6CzQ0LJ3q1hczm/NzuT5TeDHgQj/mfR5sPZkX+DHil3xrhdetMf/HE0u1ToyqE84tsTo3rl7aW5DaG1s3Hu8c/wo1/H8ZXwfk60qeD+B5S+drfPfrbvfcbakdNzpno62Cvd01t5sr6WefOm359PQ/dkQNAA==";
const __vite_glob_0_19 = "data:image/webp;base64,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";
const __vite_glob_0_20 = "" + new URL("../webp/transcoding-bg.2c596a8b.webp", import.meta.url).href;
const __vite_glob_0_22 = "" + new URL("../webp/uploading.1eda6700.webp", import.meta.url).href;
const _hoisted_1$1 = { class: "tooltip-container flex flex-v flex-ac" };
const _hoisted_2 = { class: "tool-list flex flex-ac flex-v" };
const _hoisted_3 = ["src"];
const _hoisted_4 = { class: "tool-name" };
const _hoisted_5 = { class: "system-operator-container flex flex-v flex-ac flex-jc" };
const _hoisted_6 = { class: "svg-wrapper" };
const _hoisted_7 = { class: "flex flex-ac setting-wrapper" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "ToolBar",
  props: {
    appDialogVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ["open-resource", "end-class", "show-all-apps"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const handleShutdown = async () => {
      window.electron.ipcRenderer.send("win-minimize");
    };
    const handleOpenNet = async () => {
      window.electron.ipcRenderer.send("open-system-network");
    };
    const images = /* @__PURE__ */ Object.assign({
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_0,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_1,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_2,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_3,
      "/src/assets/image/classroom/audio.webp": __vite_glob_0_4,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_5,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_6,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_7,
      "/src/assets/image/classroom/<EMAIL>": _imports_1,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_9,
      "/src/assets/image/classroom/<EMAIL>": _imports_0,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_11,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_12,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_13,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_14,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_15,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_16,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_17,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_18,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_19,
      "/src/assets/image/classroom/transcoding-bg.webp": __vite_glob_0_20,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_21,
      "/src/assets/image/classroom/uploading.webp": __vite_glob_0_22,
      "/src/assets/image/classroom/<EMAIL>": __vite_glob_0_23
    });
    const getImageUrl = (name) => {
      return images[`/src/assets/image/classroom/${name}@2x.webp`] || "";
    };
    const handleOpenResource = () => {
      emit("open-resource");
    };
    const handleEndClass = () => {
      console.log("handleEndClass");
      emit("end-class");
    };
    const showAllApps = () => {
      emit("show-all-apps");
    };
    const toolList = ref([
      {
        name: "随机点名",
        icon: getImageUrl("random")
      },
      {
        name: "计时器",
        icon: getImageUrl("interval")
      },
      {
        name: "计时器",
        icon: getImageUrl("interval")
      },
      {
        name: "计时器",
        icon: getImageUrl("interval")
      },
      {
        name: "计时器",
        icon: getImageUrl("interval")
      }
    ]);
    return (_ctx, _cache) => {
      const _component_el_popover = ElPopover;
      return openBlock(), createElementBlock("div", _hoisted_1$1, [
        createBaseVNode("div", {
          class: "resource-button flex flex-ac flex-v",
          onClick: handleOpenResource
        }, _cache[0] || (_cache[0] = [
          createBaseVNode("img", {
            class: "tool-icon",
            src: _imports_0,
            alt: "resource"
          }, null, -1),
          createBaseVNode("span", { class: "tool-name" }, "资源", -1)
        ])),
        createBaseVNode("div", _hoisted_2, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(toolList.value, (item, index2) => {
            return openBlock(), createElementBlock("div", {
              key: index2,
              class: "tool-item flex flex-ac flex-v"
            }, [
              createBaseVNode("img", {
                class: "tool-icon",
                src: item.icon,
                alt: "resource"
              }, null, 8, _hoisted_3),
              createBaseVNode("div", _hoisted_4, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(item.name.split(""), (char, charIndex) => {
                  return openBlock(), createElementBlock("span", {
                    key: charIndex,
                    class: "tool-char"
                  }, toDisplayString(char), 1);
                }), 128))
              ])
            ]);
          }), 128))
        ]),
        createBaseVNode("div", {
          class: "class-end-button flex flex-ac flex-v",
          onClick: handleEndClass
        }, _cache[1] || (_cache[1] = [
          createBaseVNode("img", {
            class: "tool-icon",
            src: _imports_1,
            alt: "resource"
          }, null, -1),
          createBaseVNode("span", { class: "tool-name" }, "下课", -1)
        ])),
        createBaseVNode("div", _hoisted_5, [
          createBaseVNode("div", {
            class: normalizeClass(["svg-wrapper", { selected: props.appDialogVisible }])
          }, [
            createVNode(SvgIcon, {
              "icon-class": "more-app",
              onClick: showAllApps
            })
          ], 2),
          createBaseVNode("div", _hoisted_6, [
            createVNode(_component_el_popover, {
              placement: "left",
              trigger: "click",
              "popper-class": "setting-popover",
              offset: 40,
              "show-arrow": false
            }, {
              reference: withCtx(() => [
                createVNode(SvgIcon, { "icon-class": "setting" })
              ]),
              default: withCtx(() => [
                createBaseVNode("div", _hoisted_7, [
                  createVNode(SvgIcon, {
                    "icon-class": "net",
                    onClick: handleOpenNet
                  }),
                  createVNode(SvgIcon, {
                    "icon-class": "guanji",
                    onClick: handleShutdown
                  })
                ])
              ]),
              _: 1
            })
          ])
        ])
      ]);
    };
  }
});
const ToolBar_vue_vue_type_style_index_0_scoped_f683b4b3_lang = "";
const ToolBar_vue_vue_type_style_index_1_lang = "";
const ToolBar = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-f683b4b3"]]);
const _hoisted_1 = { class: "container" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createVNode(ToolBar)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_4688ee50_lang = "";
const index_vue_vue_type_style_index_1_lang = "";
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-4688ee50"]]);
export {
  index as default
};
