#include "ZIndexManager.h"
#include "WindowActivationBlocker.h"
#include <QApplication>
#include <QWidget>
#include <algorithm>
#include <QDebug>

#ifdef Q_OS_WIN
#include <windows.h>
#include <QWindow>
#endif

ZIndexManager::ZIndexManager(QObject* parent)
    : QObject(parent)
{
    // qDebug() << "ZIndexManager: 初始化Z-Index管理器，启动CEF监控";
}

ZIndexManager::~ZIndexManager()
{
    qDebug() << "ZIndexManager: 销毁Z-Index管理器";

    // 清理所有拦截器对象，防止野指针
    for (auto it = m_blockers.begin(); it != m_blockers.end(); ++it) {
        WindowActivationBlocker* blocker = it.value();
        if (blocker) {
            // 清除窗口管理器引用，防止野指针访问
            blocker->setWindowManager(nullptr);
        }
    }
    m_blockers.clear();
    m_blockerInstalled.clear();
}

void ZIndexManager::registerComponent(QWidget* widget, ZIndexLevel level, ComponentType type, const QString& name)
{
    if (!widget) {
        qWarning() << "ZIndexManager: 尝试注册空组件";
        return;
    }
    
    // 如果组件已存在，先注销
    if (m_components.contains(widget)) {
        unregisterComponent(widget);
    }
    
    // 确保组件有有意义的名称用于调试
    QString componentName = name;
    if (componentName.isEmpty()) {
        componentName = widget->objectName();
        if (componentName.isEmpty()) {
            componentName = QString("未命名组件_%1").arg(reinterpret_cast<quintptr>(widget), 0, 16);
        }
    }

    applyWindowLevel(widget, level);

    // 创建组件信息
    ComponentInfo info(level, type, componentName);
    m_components[widget] = info;

    // 添加到层级映射
    m_levelMap[level].append(widget);



    qDebug() << QString("ZIndexManager: 注册组件 [%1] 到层级 %2 (类型: %3)")
                .arg(info.name)
                .arg(static_cast<int>(level))
                .arg(static_cast<int>(type));

    // 立即应用Z-Order
    applyZOrder();
}

void ZIndexManager::unregisterComponent(QWidget* widget)
{
    if (!widget || !m_components.contains(widget)) {
        return;
    }
    
    ComponentInfo info = m_components[widget];

    // 从窗口句柄缓存中移除
#ifdef Q_OS_WIN
    if (m_widgetToHwnd.contains(widget)) {
        HWND hwnd = m_widgetToHwnd[widget];
        m_hwndToWidget.remove(hwnd);
        m_widgetToHwnd.remove(widget);
        qDebug() << QString("ZIndexManager: 移除widget [%1] 的HWND映射")
                    .arg(widget->objectName());
    }
#endif

    // 从层级映射中移除
    m_levelMap[info.level].removeAll(widget);
    if (m_levelMap[info.level].isEmpty()) {
        m_levelMap.remove(info.level);
    }

    // 从组件映射中移除
    m_components.remove(widget);

    // 清理拦截器对象和记录
    if (m_blockers.contains(widget)) {
        WindowActivationBlocker* blocker = m_blockers.take(widget);
        if (blocker) {
            // 清除窗口管理器引用，防止野指针访问
            blocker->setWindowManager(nullptr);
            // 不删除blocker对象，让Qt的父子关系自动管理
            // 因为blocker的parent是widget，当widget销毁时会自动清理blocker
        }
    }
    m_blockerInstalled.remove(widget);

    qDebug() << QString("ZIndexManager: 注销组件 [%1]").arg(info.name);
}

void ZIndexManager::updateComponentLevel(QWidget* widget, ZIndexLevel newLevel)
{
    if (!widget || !m_components.contains(widget)) {
        qWarning() << "ZIndexManager: 尝试更新不存在的组件层级";
        return;
    }
    
    ComponentInfo& info = m_components[widget];
    ZIndexLevel oldLevel = info.level;
    
    if (oldLevel == newLevel) {
        return; // 层级没有变化
    }
    
    // 从旧层级移除
    m_levelMap[oldLevel].removeAll(widget);
    if (m_levelMap[oldLevel].isEmpty()) {
        m_levelMap.remove(oldLevel);
    }
    
    // 添加到新层级
    info.level = newLevel;
    m_levelMap[newLevel].append(widget);
    
    qDebug() << QString("ZIndexManager: 组件 [%1] 层级从 %2 更新到 %3")
                .arg(info.name)
                .arg(static_cast<int>(oldLevel))
                .arg(static_cast<int>(newLevel));
    
    emit levelChanged(widget, oldLevel, newLevel);
    
    // 应用新的Z-Order
    applyZOrder();
}

void ZIndexManager::applyZOrder()
{
    // 清理无效组件
    cleanupInvalidComponents();

    // 获取所有层级并排序
    QList<ZIndexLevel> levels = m_levelMap.keys();
    std::sort(levels.begin(), levels.end(), [](ZIndexLevel a, ZIndexLevel b) {
        return static_cast<int>(a) < static_cast<int>(b);
    });

    // qDebug() << "ZIndexManager: 应用Z-Order，共" << levels.size() << "个层级";

    // 分别处理子组件和独立窗口
    QList<QWidget*> independentWindows;

    // 先处理子组件
    for (ZIndexLevel level : levels) {
        QList<QWidget*> components = m_levelMap[level];

        for (QWidget* widget : components) {
            if (!widget) {
                continue;
            }

            ComponentInfo& info = m_components[widget];

            if (isIndependentWindow(widget)) {
                // 收集独立窗口，稍后按层级顺序处理
                independentWindows.append(widget);
            } else {
                // 子组件：使用传统Z-Order管理
                applyChildLevel(widget, level);
                // qDebug() << QString("  - 设置子组件 [%1] Z-Order: %2")
                //             .arg(info.name)
                //             .arg(static_cast<int>(level));
            }
        }
    }

    // 预构建层级映射以提高性能
    QMap<QWidget*, ZIndexLevel> widgetLevelMap;
    for (auto it = m_levelMap.begin(); it != m_levelMap.end(); ++it) {
        for (QWidget* w : it.value()) {
            widgetLevelMap[w] = it.key();
        }
    }

    // 使用稳定排序保持同层级窗口的原始顺序
    std::stable_sort(independentWindows.begin(), independentWindows.end(),
                     [&widgetLevelMap](QWidget* a, QWidget* b) {
                         ZIndexLevel levelA = widgetLevelMap.value(a, ZIndexLevel::BOTTOM_LAYER_BASE);
                         ZIndexLevel levelB = widgetLevelMap.value(b, ZIndexLevel::BOTTOM_LAYER_BASE);
                         return static_cast<int>(levelA) < static_cast<int>(levelB);
                     });

    // qDebug() << "ZIndexManager: 排序后的独立窗口顺序:";
    for (int i = 0; i < independentWindows.size(); i++) {
        QWidget* widget = independentWindows[i];
        ZIndexLevel level = ZIndexLevel::CANVAS_LAYER;
        for (auto it = m_levelMap.begin(); it != m_levelMap.end(); ++it) {
            if (it.value().contains(widget)) {
                level = it.key();
                break;
            }
        }
        // qDebug() << QString("  %1. [%2] 层级: %3")
        //             .arg(i + 1)
        //             .arg(widget->objectName())
        //             .arg(static_cast<int>(level));
    }

    // 按照zindex处理完毕之后，循环进行raise处理确保正确的层级顺序
    // 使用Windows API确保层级顺序正确，反向遍历以保持正确的层级顺序
    #ifdef Q_OS_WIN
    HWND hWndInsertAfter = HWND_TOPMOST;
    for (int i = independentWindows.size() - 1; i >= 0; i--) {
        QWidget* widget = independentWindows[i];
        if (widget && widget->isVisible()) {
            HWND hWnd = (HWND)widget->winId();
            if (hWnd) {
                // 检查该widget是否已有旧的HWND映射
                if (m_widgetToHwnd.contains(widget)) {
                    HWND oldHwnd = m_widgetToHwnd[widget];
                    if (oldHwnd != hWnd) {
                        // 清理旧的映射
                        m_hwndToWidget.remove(oldHwnd);
                        
                    }
                }

                // 更新双向映射
                m_hwndToWidget[hWnd] = widget;
                m_widgetToHwnd[widget] = hWnd;

                // 使用SetWindowPos按顺序设置窗口层级，强制应用
                BOOL result = SetWindowPos(hWnd, hWndInsertAfter, 0, 0, 0, 0,
                           SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);

                // 如果第一次失败，再尝试一次不使用 SWP_NOSENDCHANGING
                if (!result) {
                    SetWindowPos(hWnd, hWndInsertAfter, 0, 0, 0, 0,
                               SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE | SWP_NOSENDCHANGING);
                }
                hWndInsertAfter = hWnd; // 下一个窗口插入到当前窗口之后
                // qDebug() << QString("ZIndexManager: SetWindowPos窗口 [%1] (反向遍历)")
                //             .arg(widget->objectName());
            }
        }
        installActivationBlocker(widget, false);
    }
    #endif
    
}

void ZIndexManager::bringToTop(QWidget* widget)
{
    if (!widget || !m_components.contains(widget)) {
        return;
    }
    
    ComponentInfo& info = m_components[widget];
    
    if (!info.isTemporaryTop) {
        // 保存原始层级
        info.originalLevel = info.level;
        info.isTemporaryTop = true;
        
        // 更新到临时置顶层级
        updateComponentLevel(widget, ZIndexLevel::TEMPORARY_TOP);
        
    }
}

void ZIndexManager::restoreOriginalLevel(QWidget* widget)
{
    if (!widget || !m_components.contains(widget)) {
        return;
    }
    
    ComponentInfo& info = m_components[widget];
    
    if (info.isTemporaryTop) {
        info.isTemporaryTop = false;
        
        // 恢复到原始层级
        updateComponentLevel(widget, info.originalLevel);
        
        
    }
}

ZIndexLevel ZIndexManager::getComponentLevel(QWidget* widget) const
{
    if (!widget || !m_components.contains(widget)) {
        return ZIndexLevel::BOTTOM_LAYER_BASE;
    }
    
    return m_components[widget].level;
}

QList<QWidget*> ZIndexManager::getComponentsAtLevel(ZIndexLevel level) const
{
    return m_levelMap.value(level, QList<QWidget*>());
}

void ZIndexManager::printLevelStatus() const
{
    
    QList<ZIndexLevel> levels = m_levelMap.keys();
    std::sort(levels.begin(), levels.end(), [](ZIndexLevel a, ZIndexLevel b) {
        return static_cast<int>(a) < static_cast<int>(b);
    });
    
    for (ZIndexLevel level : levels) {
        QList<QWidget*> components = m_levelMap[level];
        // qDebug() << QString("层级 %1: %2 个组件").arg(static_cast<int>(level)).arg(components.size());
        
        for (QWidget* widget : components) {
            if (m_components.contains(widget)) {
                const ComponentInfo& info = m_components[widget];
                // qDebug() << QString("  - [%1] (类型: %2, 临时置顶: %3)")
                //             .arg(info.name)
                //             .arg(static_cast<int>(info.type))
                //             .arg(info.isTemporaryTop ? "是" : "否");
            }
        }
    }
    
    // qDebug() << "=== 状态结束 ===";
}

void ZIndexManager::cleanupInvalidComponents()
{
    QList<QWidget*> invalidComponents;
    
    // 查找无效组件
    for (auto it = m_components.begin(); it != m_components.end(); ++it) {
        QWidget* widget = it.key();
        if (!widget) {
            invalidComponents.append(widget);
        }
    }
    
    // 移除无效组件
    for (QWidget* widget : invalidComponents) {
        unregisterComponent(widget);
    }
    
    if (!invalidComponents.isEmpty()) {
        qDebug() << QString("ZIndexManager: 清理了 %1 个无效组件").arg(invalidComponents.size());
    }
}

QList<QWidget*> ZIndexManager::sortComponentsByLevel(const QList<QWidget*>& components) const
{
    QList<QWidget*> sortedComponents = components;

    std::sort(sortedComponents.begin(), sortedComponents.end(),
              [this](QWidget* a, QWidget* b) {
                  if (!a || !b) return false;

                  ZIndexLevel levelA = getComponentLevel(a);
                  ZIndexLevel levelB = getComponentLevel(b);

                  return static_cast<int>(levelA) < static_cast<int>(levelB);
              });

    return sortedComponents;
}

bool ZIndexManager::isIndependentWindow(QWidget* widget) const
{
    if (!widget) {
        return false;
    }

    // 检查窗口标志，判断是否为独立窗口
    Qt::WindowFlags flags = widget->windowFlags();

    // 如果设置了Tool、Dialog、Window等独立窗口标志，则认为是独立窗口
    bool hasIndependentFlag = (flags & Qt::Tool) ||
                             (flags & Qt::Dialog) ||
                             (flags & Qt::Window);

    // 同时检查是否有父窗口但不是子组件关系
    bool hasTopLevelParent = widget->isWindow();

    return hasIndependentFlag || hasTopLevelParent;
}

Qt::WindowFlags ZIndexManager::getWindowFlagsForLevel(ZIndexLevel level) const
{
    // 根据层级返回对应的窗口标志组合
    switch (level) {
        case ZIndexLevel::CANVAS_LAYER:
            // 画布层保持为子组件，不需要窗口标志
            return Qt::Widget;

        case ZIndexLevel::CEF_APP_CENTER_FULLSCREEN:
        case ZIndexLevel::TEMPORARY_TOP:
            // 最高层级
            return Qt::Tool | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint;
        case ZIndexLevel::MODAL_DIALOG:
            // 模态对话框：最顶层
            return Qt::Dialog | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint;
        default:
            // 默认为基础独立窗口
            return Qt::Tool | Qt::FramelessWindowHint;
    }
}

void ZIndexManager::applyWindowLevel(QWidget* widget, ZIndexLevel level)
{
    if (!widget) {
        return;
    }

    // 获取目标窗口标志
    Qt::WindowFlags targetFlags = getWindowFlagsForLevel(level);
    Qt::WindowFlags currentFlags = widget->windowFlags();

    // 如果窗口标志需要更新
    if (currentFlags != targetFlags) {
        // 保存当前状态
        bool wasVisible = widget->isVisible();
        bool wasMaximized = widget->isMaximized();
        bool wasMinimized = widget->isMinimized();
        QPoint currentPos = widget->pos();
        QSize currentSize = widget->size();

        // qDebug() << QString("ZIndexManager: 更新窗口标志 [%1] 从 %2 到 %3")
        //             .arg(widget->objectName())
        //             .arg(currentFlags, 0, 16)
        //             .arg(targetFlags, 0, 16);

        // 设置新的窗口标志
        widget->setWindowFlags(targetFlags);

        // 恢复位置和大小
        widget->move(currentPos);
        widget->resize(currentSize);

        // 恢复窗口状态
        if (wasVisible) {
            if (wasMaximized) {
                widget->showMaximized();
            } else if (wasMinimized) {
                widget->showMinimized();
            } else {
                widget->show();
            }
        }
    }

    // 禁用窗口的自动置顶行为
    disableWindowAutoRaise(widget);

    // 使用Windows API设置精确的窗口层级
    setWindowLevelWithWinAPI(widget, level);
}

void ZIndexManager::applyChildLevel(QWidget* widget, ZIndexLevel level)
{
    if (!widget) {
        return;
    }

    // 传统的子组件Z-Order管理
    if (level == ZIndexLevel::CANVAS_LAYER) {
        // 画布层：确保在最底层
        widget->lower();
    } else {
        // 其他层级：按顺序提升
        widget->raise();
    }
}

void ZIndexManager::setWindowLevelWithWinAPI(QWidget* widget, ZIndexLevel level)
{
#ifdef Q_OS_WIN
    if (!widget || !widget->windowHandle()) {
        return;
    }

    HWND hwnd = (HWND)widget->windowHandle()->winId();
    if (!hwnd) {
        return;
    }

    // 移除TOPMOST属性，防止自动置顶
    LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
    exStyle &= ~WS_EX_TOPMOST;
    SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle);

    // qDebug() << QString("ZIndexManager: 准备窗口 [%1] 层级: %2")
    //             .arg(widget->objectName())
    //             .arg(static_cast<int>(level));
#else
    Q_UNUSED(widget)
    Q_UNUSED(level)
#endif
}

void ZIndexManager::disableWindowAutoRaise(QWidget* widget)
{
#ifdef Q_OS_WIN
    if (!widget || !widget->windowHandle()) {
        return;
    }

    HWND hwnd = (HWND)widget->windowHandle()->winId();
    if (!hwnd) {
        return;
    }

    // 查找组件的层级
    ZIndexLevel level = ZIndexLevel::CANVAS_LAYER;
    for (auto it = m_levelMap.begin(); it != m_levelMap.end(); ++it) {
        if (it.value().contains(widget)) {
            level = it.key();
            break;
        }
    }

    // 移除TOPMOST属性，防止窗口自动置顶
    LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
    exStyle &= ~WS_EX_TOPMOST;
    SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle);

    // qDebug() << QString("ZIndexManager: 禁用自动置顶 [%1] 层级: %2 避免点击时自动激活")
    //             .arg(widget->objectName())
    //             .arg(static_cast<int>(level));
#else
    Q_UNUSED(widget)
#endif
}

void ZIndexManager::forceReapplyLevels()
{
    qDebug() << "ZIndexManager: 强制重新应用所有窗口层级";

    // 清理无效组件
    cleanupInvalidComponents();

    // 重新应用层级
    applyZOrder();
}

void ZIndexManager::installActivationBlocker(QWidget* widget, bool allowActivation)
{
    if (!widget) {
        return;
    }

    // 检查是否已经安装过拦截器
    if (m_blockerInstalled.contains(widget)) {
        // qDebug() << QString("ZIndexManager: 窗口 [%1] 已安装激活拦截器，跳过重复安装")
        //             .arg(widget->objectName());
        return;
    }

    // 为窗口安装消息拦截器
    WindowActivationBlocker* blocker = new WindowActivationBlocker(allowActivation, widget);
    blocker->setAllowActivation(allowActivation);
    blocker->setWindowManager(this);  // 设置窗口管理器接口引用

    // 连接窗口可见性变化信号
    connect(blocker, &WindowActivationBlocker::windowVisibilityChanged,
            this, &ZIndexManager::onWindowVisibilityChanged);

    // 将拦截器作为事件过滤器安装到窗口上
    widget->installEventFilter(blocker);

    // 记录已安装和拦截器对象
    m_blockerInstalled.insert(widget);
    m_blockers[widget] = blocker;

    qDebug() << QString("ZIndexManager: 为窗口 [%1] 安装激活拦截器，允许激活: %2")
                .arg(widget->objectName())
                .arg(allowActivation);
}

void ZIndexManager::onWindowVisibilityChanged()
{
    // 窗口可见性变化时，重新应用Z-order以确保层级正确
    // qDebug() << "ZIndexManager: 检测到窗口可见性变化，重新应用Z-order";

    // 立即应用一次，确保快速响应
    applyZOrder();

    // 再次延迟应用，确保覆盖系统的后续操作
    QTimer::singleShot(10, this, [this]() {
        applyZOrder();
    });
}

bool ZIndexManager::isWindowManaged(HWND hwnd) const
{
#ifdef Q_OS_WIN
    if (!hwnd) {
        return false;
    }

    // 使用缓存快速查找，O(1)复杂度
    return m_hwndToWidget.contains(hwnd);
#else
    Q_UNUSED(hwnd)
    return false;
#endif
}


