function loadImage(url, options) {
  return new Promise((resolve, reject) => {
    const image = new Image();
    let timeoutIndex = setTimeout(() => {
      reject("图片加载超时");
    }, options?.timeout || 1e4);
    image.onload = () => {
      if (timeoutIndex) {
        clearTimeout(timeoutIndex);
        timeoutIndex = null;
      }
      resolve(image);
    };
    image.onerror = (e) => {
      if (timeoutIndex) {
        clearTimeout(timeoutIndex);
        timeoutIndex = null;
      }
      reject(e);
    };
    image.src = url;
  });
}
function canvasToImageBlob(canvas, type, quality) {
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error("Failed to convert canvas to blob"));
        }
      },
      type || "image/jpeg",
      quality || 0.8
    );
  });
}
export {
  canvasToImageBlob as c,
  loadImage as l
};
