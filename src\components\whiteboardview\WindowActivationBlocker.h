#ifndef WINDOWACTIVATIONBLOCKER_H
#define WINDOWACTIVATIONBLOCKER_H

#include <QWidget>
#include <QDebug>
#include <QAbstractNativeEventFilter>
#include "IWindowManager.h"

#ifdef Q_OS_WIN
#include <windows.h>
#endif

/**
 * @brief 为现有QWidget安装消息拦截器
 *
 * 这个类可以作为事件过滤器安装到现有的QWidget上，
 * 无需修改现有的类继承关系
 */
class WindowActivationBlocker : public QObject, public QAbstractNativeEventFilter
{
    Q_OBJECT

public:
    explicit WindowActivationBlocker(bool allowActivation = false, QObject *parent = nullptr);
    ~WindowActivationBlocker();

    /**
     * @brief 设置是否允许窗口激活
     */
    void setAllowActivation(bool allow);

    /**
     * @brief 获取是否允许激活
     */
    bool allowActivation() const { return m_allowActivation; }

    /**
     * @brief 设置窗口管理器引用，用于检查窗口是否被管理
     */
    void setWindowManager(IWindowManager* manager) { m_windowManager = manager; }

signals:
    /**
     * @brief 窗口可见性变化信号
     */
    void windowVisibilityChanged();

private:
#ifdef Q_OS_WIN
    /**
     * @brief 检查窗口是否是ZIndexManager管理的窗口
     * @param hwnd 窗口句柄
     * @return 如果是ZIndexManager管理的窗口返回true
     */
    bool isZIndexManagedWindow(HWND hwnd);

    /**
     * @brief 检查窗口是否是QCefView相关的窗口
     * @param hwnd 窗口句柄
     * @return 如果是QCefView相关窗口返回true
     */
    bool isCefViewWindow(HWND hwnd);
#endif

protected:
    /**
     * @brief 事件过滤器
     */
    bool eventFilter(QObject *obj, QEvent *event) override;

    /**
     * @brief 拦截Windows原生消息
     */
    bool nativeEventFilter(const QByteArray &eventType, void *message, qintptr *result) override;

private:
    bool m_allowActivation;  ///< 是否允许窗口激活
    IWindowManager* m_windowManager;  ///< 窗口管理器接口引用
    bool m_isDestroying;  ///< 标记对象是否正在销毁
};

#endif // WINDOWACTIVATIONBLOCKER_H
